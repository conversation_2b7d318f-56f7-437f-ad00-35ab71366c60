
# Library

library(dplyr)
library(tidyr)
library(readr)
library(lubridate)
library(zoo)
library(purrr)
library(stringi)
library(stringr)
library(aws.s3)
library(DBI)
library(RODBC)
library(RPostgreSQL)
library(jsonlite)
library(yaml)

# Read ENV Var
readRenviron(".env")

# DB Connection

## Read
dbname_read <- Sys.getenv("DB_NAME_READ")
host_read <- Sys.getenv("DB_HOST_READ")
port_read <- Sys.getenv("DB_PORT_READ")
user_read <- Sys.getenv("DB_USER_READ")
password_read <- Sys.getenv("DB_PASSWORD_READ")

con_read <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_read,
  host = host_read,
  port = port_read,
  user = user_read,
  password = password_read
)

## Write
dbname_write <- Sys.getenv("DB_NAME_WRITE")
host_write <- Sys.getenv("DB_HOST_WRITE")
port_write <- Sys.getenv("DB_PORT_WRITE")
user_write <- Sys.getenv("DB_USER_WRITE")
password_write <- Sys.getenv("DB_PASSWORD_WRITE")

con_write <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_write,
  host = host_write,
  port = port_write,
  user = user_write,
  password = password_write
)


## Read config from db
schema <- "public" #params$schema
job_vacancy_id <- 2122 #params$job_vacancy_id

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)

sql_filename <- 'queries/base_line.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

# Prepare the statement
stmt <- dbSendQuery(con_read, query)

# Bind the parameter
dbBind(stmt, list(job_vacancy_id))

# Execute the query and fetch results
result <- dbFetch(stmt)

# Clear the result
dbClearResult(stmt)

df_base_line <- as.data.frame(result)

#View(df_base_line)


### WE

# base_line_we <-df_base_line$job_level

# Function to translate job level to working experience levels list
# translate_job_level_to_we <- function(job_level) {
#   # Define the complete list of experience ranges
#   we_levels <- list(
#     "0-2 tahun" = c(0, 2),
#     "2-5 tahun" = c(2, 5),
#     "7-12 tahun" = c(7, 12),
#     "10-15 tahun" = c(10, 15)
#   )
#
#   # Define a translation map for job levels
#   translation_map <- list(
#     "entry_level" = "0-2 tahun",
#     "mid_level" = "2-5 tahun",
#     "senior_level" = "7-12 tahun",
#     "manager" = "10-15 tahun"
#   )
#
#   # Check if the job_level exists in the translation map
#   if (job_level %in% names(translation_map)) {
#     # Return the corresponding experience range as a string
#     return(translation_map[[job_level]])
#   } else {
#     # Return NULL or a default value if the job_level is not found
#     return(NA)  # You can also return a default range if needed
#   }
#
#   # Get the corresponding working experience range
#   selected_we <- translation_map[[job_level]]
#
#   # Return the full we_levels list, but with the selected range first
#   we_levels[selected_we] <- we_levels[selected_we]
#
#   return(list(we_levels = we_levels,
#               selected_range = NA
#               ))
# }

translate_job_level_to_we <- function(job_level) {
  # Define the complete list of experience ranges
  we_levels <- list(
    "0-2 tahun" = c(0, 1.9999),
    "2-5 tahun" = c(2, 4.9999),
    "7-12 tahun" = c(7, 12),
    "10-15 tahun" = c(10, 15),
    "0-15 tahun" = c(0, 15)
  )

  # Define a translation map for job levels
  translation_map <- list(
    "entry_level" = "0-2 tahun",
    "med_associate" = "2-5 tahun",
    "senior_lead" = "7-12 tahun",
    "principal" = "10-15 tahun",
    "all_level_experience" = "0-15 tahun"
  )

  # Check if the job_level exists in the translation map
  if (job_level %in% names(translation_map)) {
    # Get the corresponding experience range as a string
    selected_range <- translation_map[[job_level]]

    # Return the full we_levels list and the selected range
    return(list(
      full_list = we_levels,
      selected_range = selected_range,
      selected_range_values = we_levels[[selected_range]]
    ))
  } else {
    # Return the full list and NA if the job_level is not found
    return(list(
      full_list = we_levels,
      selected_range = NA,
      selected_range_values = NA
    ))
  }
}

# Example usage
we_result <- df_base_line$job_level  # e.g., "entry_level"

# Call the function to translate job level to experience range
we_result <- translate_job_level_to_we(we_result)

base_line_we <- we_result$selected_range

### EDU

base_line_edu <-df_base_line$`Education Level`

### GPA

base_line_gpa <- NA

### Domicile

base_line_domisili <- df_base_line$Domicile

### Salary

base_line_expected_salary <- df_base_line$minimum_salary

### Industry

# Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
base_line_industry <- strsplit(df_base_line$`Previous Job Industry`, ", ")

# Flatten the list to ensure it's in the expected format
base_line_industry <- unlist(base_line_industry)

# Convert it back to a list where each element is a single string
base_line_industry <- as.list(base_line_industry)

### Skills n tools

# Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
base_line_skillntools <- strsplit(df_base_line$`Tools and Competencies Mastery`, ", ")

# Flatten the list to ensure it's in the expected format
base_line_skillntools <- unlist(base_line_skillntools)

# Convert it back to a list where each element is a single string
base_line_skillntools <- as.list(base_line_skillntools)

# ## Base Line Input
# ### Need to Handle Recruiter Input manually
# base_line_edu <- "D3"
# base_line_we <- "0-2 tahun"
# base_line_gpa <- "2.9-3.2"
# base_line_domisili <- "Kota Depok - Jawa Barat"
# base_line_expected_salary <- 5000000
# base_line_industry <- c("Technology")
# base_line_skillntools <- list("Data Analyst", "Exploratory Data Analysis", "Python", "Google Spreadsheet")


## 1.3 Variable Input
print("Setup Variable Input")
### 1.3.1 Config General

#### config default

create_MrU_GPA <- function(base_line_gpa, default_weight = 3) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_gpa)) {
    MrU_GPA <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_GPA <- list(weight = default_weight)
  }

  return(MrU_GPA)
}

#### a. MrU Criteria - Weight
print("Setup MrU")
# Define example inputs with weights
MrU_Education <- list(weight = 3)
MrU_WE <- list(weight = 2)
#MrU_GPA <- list(weight = 3)
MrU_GPA <- create_MrU_GPA(base_line_gpa)
MrU_Domisil <- list(weight = 1)
MrU_ES <- list(weight = 1)
MrU_Industry <- list(weight = 1)
MrU_Skillntools <- list(weight = 3)

# MrU_Education <- list(weight = 1)
# MrU_WE <- list(weight = 1)
# MrU_GPA <- list(weight = 1)
# MrU_Domisil <- list(weight = 1)
# MrU_ES <- list(weight = 1)
# MrU_Industry <- list(weight = 1)
# MrU_Skillntools <- list(weight = 1)

#CutOffValue <- as.numeric(config_cutoff$Value)
CutOffValue <- 40


#### b. MrC Sub Criteria - Norma
print("Setup MrC")
# Define levels for education
education_levels <- c("SMA/SMK", "D1", "D2", "D3", "D4", "S1", "S2", "S3")

# Define levels for working experience as numeric ranges
# we_levels <- list("0-2 tahun" = c(0, 2),
#                   "2-5 tahun" = c(2, 5),
#                   "7-12 tahun" = c(7, 12),
#                   "10-15 tahun" = c(10, 15))

we_levels <- we_result$full_list

print(we_levels)

# Define levels for GPA as numeric ranges
gpa_levels <- list("0-2.5" = c(0, 2.5),
                   "2.5-2.7" = c(2.5, 2.7),
                   "2.7-2.9" = c(2.7, 2.9),
                   "2.9-3.2" = c(2.9, 3.2),
                   "3.2-3.5" = c(3.2, 3.5),
                   "3.5-4" = c(3.5, 4))

# Function to create dynamic values for education
create_dynamic_values_edu <- function(levels, base_line) {
  base_index <- match(base_line, levels)
  values <- seq(0, length(levels) - base_index)
  # Ensure the length of values matches the length of levels
  values <- c(rep(0, base_index - 1), values)
  return(values)
}

# Function to convert a range string into a numeric range
convert_to_numeric_range <- function(range_str) {
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Function to create dynamic values for working experience

# Function to create dynamic values for working experience levels
create_dynamic_values_we <- function(levels, base_line) {
  # Convert the base_line to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Find the index where the base_line falls within the levels
  base_index <- which(sapply(names(levels), function(x) {
    exp_range <- convert_to_numeric_range(gsub(" tahun", "", x))
    return(base_range[1] >= exp_range[1] && base_range[2] <= exp_range[2])
  }))

  # Check if base_index is valid
  if (length(base_index) == 0) {
    stop("Base line range does not match any experience levels")
  }

  # Create a sequence of values starting from 0
  values <- seq(0, length(levels) - base_index + 1)

  # Adjust values so that all preceding levels get a value of 0
  values <- c(rep(0, base_index - 1), values)

  # Ensure the length of values matches the length of levels
  values <- values[1:length(levels)]

  return(values)
}

# create_dynamic_values_we <- function(levels, base_line) {
#   base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))
#   base_index <- which(sapply(names(levels), function(x) {
#     exp_range <- convert_to_numeric_range(gsub(" tahun", "", x))
#     return(base_range[1] >= exp_range[1] && base_range[2] <= exp_range[2])
#   }))
#   values <- seq(0, length(levels) - base_index)
#   # Ensure the length of values matches the length of levels
#   values <- c(rep(0, base_index - 1), values)
#   return(values)
# }

# Function to create dynamic values for GPA
# Function to convert GPA ranges (assumes ranges are in the format "low-high")
# Function to convert a range string into a numeric range
convert_to_numeric_range <- function(range_str) {
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Function to create dynamic values for working experience levels
create_dynamic_values_we <- function(levels, base_line) {
  # Convert the base_line to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Find the index where the base_line falls within the levels
  base_index <- which(sapply(names(levels), function(x) {
    exp_range <- convert_to_numeric_range(gsub(" tahun", "", x))
    return(base_range[1] >= exp_range[1] && base_range[2] <= exp_range[2])
  }))

  # Check if base_index is valid
  if (length(base_index) == 0) {
    stop("Base line range does not match any experience levels")
  }

  # Use the first match if multiple indices are found
  base_index <- base_index[1]

  # Create a sequence of values starting from 0
  values <- seq(0, length(levels) - base_index)

  # Adjust values so that all preceding levels get a value of 0
  values <- c(rep(0, base_index - 1), values)

  # Ensure the length of values matches the length of levels
  values <- values[1:length(levels)]

  return(values)
}

# # Run the function with your inputs
# gpa_values <- create_dynamic_values_gpa(gpa_levels, base_line_gpa)
#
# # Print the result
# print(gpa_values)

# create_dynamic_values_gpa <- function(levels, base_line) {
#   base_range <- convert_to_numeric_range(base_line)
#   base_index <- which(sapply(names(levels), function(x) {
#     gpa_range <- convert_to_numeric_range(x)
#     return(base_range[1] >= gpa_range[1] && base_range[2] <= gpa_range[2])
#   }))
#   values <- seq(0, length(levels) - base_index)
#   # Ensure the length of values matches the length of levels
#   values <- c(rep(0, base_index - 1), values)
#   return(values)
# }


#### c. MrC Dynamic Sub Criteria


# Function to convert range strings to numeric ranges
convert_to_numeric_range <- function(range_str) {
  range_str <- as.character(range_str)  # Ensure input is character
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Create data frames for each criterion
MrC_Education <- data_frame(level = education_levels,
                            value = create_dynamic_values_edu(education_levels, base_line_edu))

MrC_WE <- data_frame(level = names(we_levels),
                     value = create_dynamic_values_we(we_levels, base_line_we))

MrC_GPA <- data_frame(level = names(gpa_levels),
                      value = create_dynamic_values_gpa(gpa_levels, base_line_gpa))


### 1.3.2 Final Config JSON
print("Final Config Setup")

Education <- list(MrU_Education = MrU_Education,
                  MrC_Education = MrC_Education
)

Working_Experience <- list(MrU_WE = MrU_WE,
                           MrC_WE = MrC_WE
)

GPA <- list(MrU_GPA = MrU_GPA,
            MrC_GPA = MrC_GPA
)

Domisili <- list(MrU_Domisil = MrU_Domisil)

Expected_Salary <- list(MrU_ES = MrU_ES)

Industry <- list(MrU_Industry = MrU_Industry)

Skillntools <- list(MrU_Skillntools = MrU_Skillntools)


matchmaking_config <- list(Education = Education,
                           Working_Experience = Working_Experience,
                           GPA = GPA,
                           Domisili = Domisili,
                           Expected_Salary = Expected_Salary,
                           Industry = Industry,
                           Skillntools = Skillntools
)

print(matchmaking_config)
# match_making_config_json <- toJSON(matchmaking_config)
match_making_config_json <- json_data <- toJSON(matchmaking_config, auto_unbox = TRUE, pretty = TRUE)
#cat(match_making_config_json)
write_json(match_making_config_json, "match_making_config_json.json")

# # Prepare the query
# match_making_config_json <- '{
#     \"Education\": {
#       \"MrU_Education\": { \"weight\": [3] },
#       \"MrC_Education\": [
#         { \"level\": \"SMA/SMK\", \"value\": 0 },
#         { \"level\": \"D1\", \"value\": 0 },
#         { \"level\": \"D2\", \"value\": 0 },
#         { \"level\": \"D3\", \"value\": 0 },
#         { \"level\": \"D4\", \"value\": 0 },
#         { \"level\": \"S1\", \"value\": 0 },
#         { \"level\": \"S2\", \"value\": 1 },
#         { \"level\": \"S3\", \"value\": 2 }
#       ]
#     },
#     \"Working_Experience\": {
#       \"MrU_WE\": { \"weight\": [3] },
#       \"MrC_WE\": [
#         { \"level\": \"0-2 tahun\", \"value\": 0 },
#         { \"level\": \"2-5 tahun\", \"value\": 1 },
#         { \"level\": \"7-12 tahun\", \"value\": 2 },
#         { \"level\": \"10-15 tahun\", \"value\": 3 }
#       ]
#     },
#     \"GPA\": {
#       \"MrU_GPA\": { \"weight\": [1] },
#       \"MrC_GPA\": [
#         { \"level\": \"0-2.5\", \"value\": 0 },
#         { \"level\": \"2.5-2.7\", \"value\": 0 },
#         { \"level\": \"2.7-2.9\", \"value\": 0 },
#         { \"level\": \"2.9-3.2\", \"value\": 0 },
#         { \"level\": \"3.2-3.5\", \"value\": 1 },
#         { \"level\": \"3.5-4\", \"value\": 2 }
#       ]
#     },
#     \"Domisili\": { \"MrU_Domisili\": { \"weight\": [1] } },
#     \"Expected_Salary\": { \"MrU_ES\": { \"weight\": [1] } },
#     \"Industry\": { \"MrU_Industry\": { \"weight\": [1] } },
#     \"Skillntools\": { \"MrU_Skillntools\": { \"weight\": [1] } }
#   }'

# Escape the JSON string for safe use in SQL
escaped_json_config <- gsub("'", "''", match_making_config_json)

# Build the SQL query using paste0
update_query <- paste0(
  "UPDATE job_vacancies ",
  "SET config = jsonb_set(config, '{matchmaking_config}', '", escaped_json_config, "'::jsonb) ",
  "WHERE id = 2159;"
)

# Execute the query to update the record
dbExecute(con_write, update_query)

# Disconnect from the database after updating
dbDisconnect(con_write)

