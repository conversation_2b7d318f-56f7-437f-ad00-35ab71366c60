# Library

library(dplyr)
library(tidyr)
library(readr)
library(lubridate)
library(zoo)
library(purrr)
library(stringi)
library(stringr)
library(aws.s3)
library(DBI)
library(RODBC)
library(RPostgreSQL)
library(jsonlite)
library(yaml)

# Read ENV Var
readRenviron(".env")

# AWS Setup
AWS_ACCESS_KEY_ID <- Sys.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY <- Sys.getenv("AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION <- Sys.getenv("AWS_DEFAULT_REGION")

# DB Connection

## Read
dbname_read <- Sys.getenv("DB_NAME_READ")
host_read <- Sys.getenv("DB_HOST_READ")
port_read <- Sys.getenv("DB_PORT_READ")
user_read <- Sys.getenv("DB_USER_READ")
password_read <- Sys.getenv("DB_PASSWORD_READ")

con_read <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_read,
  host = host_read,
  port = port_read,
  user = user_read,
  password = password_read
)

## Write
dbname_write <- Sys.getenv("DB_NAME_WRITE")
host_write <- Sys.getenv("DB_HOST_WRITE")
port_write <- Sys.getenv("DB_PORT_WRITE")
user_write <- Sys.getenv("DB_USER_WRITE")
password_write <- Sys.getenv("DB_PASSWORD_WRITE")

con_write <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_write,
  host = host_write,
  port = port_write,
  user = user_write,
  password = password_write
)

# Setup Config

## Data for Schema
sql_schema_list_prod <- paste(readLines("queries/schema_list_production.sql"), collapse = "\n")
result_schema_list_prod <- dbGetQuery(con_read, sql_schema_list_prod)

schema_list_prod <- result_schema_list_prod$scheme
schema <- params$schema

if (!(schema %in% schema_list_prod)) {
  # Close connection
  DBI::dbDisconnect(con_write)
  DBI::dbDisconnect(con_read)
  stop("Schema not found")
}

## Read config from db
schema <- params$schema
job_vacancy_id <- params$job_vacancy_id

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)

sql_filename <- 'queries/config_by_job_vacancy_id.sql'
query <- paste(readLines(sql_filename), collapse = "\n")
safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
result <- dbGetQuery(con_read, safe_query)
json_string <- result$matchmaking_config

json_data <- tryCatch({
  fromJSON(json_string)
}, error = function(e) {
  list()
})

## Norma Education
config_education <- json_data$Education

if (is.null(config_education)) {
  config_education <- list(
    Value = 10,
    norma_education = data.frame(
      LastEducation = c("S3", "S2", "S2", "S1", "S1", "S1", "D4", "D4", "D4", "D3"),
      LastMajor = c("([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)"),
      SecondLastEducation = c("S2", "S1", "D4", NA, "SMA/SMK", "D3", NA, "SMA/SMK", "D3", NA),
      SecondLastMajor = c("([a-zA-Z ]+)", "([a-zA-Z ]+)", "([a-zA-Z ]+)", NA, "([a-zA-Z ]+)", "([a-zA-Z ]+)", NA, "([a-zA-Z ]+)", "([a-zA-Z ]+)", NA),
      Score = c(5, 5, 5, 3, 3, 3, 3, 3, 3, 1)
    )
  )
}

## Norma Age
config_age <- json_data$Age

if (is.null(config_age)){
  config_age <- list(
    Value = 10,
    norma_age = data.frame(
      Minimum_Age = c(18, 30, 33, 36, 0),
      Maximum_Age = c(30, 33, 36, 55, 18),
      Score = c(5, 4, 3, 2, 0)
    )
  )
}

## Norma Working Experience
config_we <- json_data$Working_Experience

if (is.null(config_we)) {
  config_we <- list(
    Value = 10,
    norma_we = data.frame(
      PositionRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
      FieldRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
      MajorRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
      Min_Exp = c(3, 2, 1, 0.01, 0),
      Max_Exp = c(30, 3, 2, 1, 0.01),
      Score = c(5, 4, 3, 2, 1)
    )
  )
}

## Norma GPA
config_gpa <- json_data$GPA

if (is.null(config_gpa)) {
  config_gpa <- list(
    Value = 10,
    norma_gpa_above_bachelor = data.frame(
      Minimum = c(3.7, 3.5, 3.2, 2.9, 2.7, 2.5, 0),
      Maximum = c(4, 3.7, 3.5, 3.2, 2.9, 2.7, 2.5),
      Score = c(5, 5, 4, 3, 2, 1, 0)
    ),
    norma_gpa_bachelor = data.frame(
      Minimum = c(3.5, 3.2, 2.9, 2.7, 2.5, 0, 0),
      Maximum = c(4, 3.5, 3.2, 2.9, 2.7, 2.5, 0),
      Score = c(5, 4, 3, 2, 1, 0, 0)
    ),
    norma_gpa_below_bachelor = data.frame(
      Minimum = c(3, 0, 0, 0, 0, 0, 0),
      Maximum = c(4, 3, 0, 0, 0, 0, 0),
      Score = c(1, 0, 0, 0, 0, 0, 0)
    )
  )
}

## Norma Campus
config_campus <- json_data$Campus

if (is.null(config_campus)) {
  config_campus <- list(
    Value = 10,
    norma_campus = data.frame(
      Description = c("QS WR 1- 100", "QS WR 101 - 1000 atau top 10 Indonesia", "BAN-PT Unggul, tapi tidak Top 10 maupun QSWR 1000 besar", "BAN-PT <Unggul"),
      Score = c(2.5, 2, 1.5, 1)
    )
  )
}

## Cutoff Status
config_cutoff <- json_data$CutoffStatus

if (is.null(config_cutoff)) {
  config_cutoff <- list(
    Value = 40.0
  )
}

# User Input

## Data Candidate
schema <- params$schema
job_vacancy_id <- params$job_vacancy_id
recalculate_all <- as.logical(params$recalculate_all)

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)
dbExecute(con_read, "SET work_mem='32MB'")

sql_filename <- 'queries/candidate_data.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

if (!recalculate_all) {
  query <- paste0(
    query,
    " LEFT JOIN user_vacancy_matchmaking_results",
    " ON candidate_datas.user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id"
  )
}

query <- paste0(
  query,
  " WHERE candidate_datas.job_vacancy_id = ?job_vacancy_id"
)

if (!recalculate_all) {
  query <- paste0(
    query,
    " AND user_vacancy_matchmaking_results.score IS NULL"
  )
}

safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)

result <- dbGetQuery(con_read, safe_query)
df_candidate <- as.data.frame(result)

# Save File
# write.csv(df_candidate, "df_candidate.csv")

## Data Experience
sql_filename <- 'queries/experience_data.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

if (!recalculate_all) {
  query <- paste0(
    query,
    " LEFT JOIN user_vacancy_matchmaking_results",
    " ON user_job_vacancies.id = user_vacancy_matchmaking_results.user_job_vacancy_id"
  )
}

query <- paste0(
  query,
  " WHERE user_job_vacancies.discarded_at IS NULL",
  " AND user_job_vacancies.state != 'pool'",
  " AND experiences.work_type != 'intern'",
  " AND user_job_vacancies.job_vacancy_id = ?job_vacancy_id"
)

if (!recalculate_all) {
  query <- paste0(
    query,
    " AND user_vacancy_matchmaking_results.score IS NULL"
  )
}

safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)

result <- dbGetQuery(con_read, safe_query)
df_experience <- as.data.frame(result)

## Data Campuus
df_campus <- s3readRDS(object = "df_campus.rds", bucket = "shiny-dashboard/dashboard_ops")

## Data Domisili
df_domisili <- s3readRDS(object = "df_domisili.rds", bucket = "shiny-dashboard/dashboard_ops")

# Variable Input

## Wight
weight_education <- config_education$Value
weight_age <- config_age$Value
weight_we <- config_we$Value
weight_gpa <- config_gpa$Value
weight_campus <- config_campus$Value

all_weight <- weight_education + weight_age + weight_we + weight_gpa + weight_campus

## CutoffStatus
CutOffValue <- as.numeric(config_cutoff$Value)

if (is.null(CutOffValue) || length(CutOffValue) == 0 || is.na(CutOffValue)) {
  CutOffValue <- 40
}

## Norma
data_education <- config_education$norma_education
data_age <- config_age$norma_age
data_we <- config_we$norma_we
data_gpa_above_bachelor <- config_gpa$norma_gpa_above_bachelor
data_gpa_bachelor <- config_gpa$norma_gpa_bachelor
data_gpa_below_bachelor <- config_gpa$norma_gpa_below_bachelor
data_status_campus <- config_campus$norma_campus |> rename(Status = Description)

# Data Wrangling

print("Data Join Campus & Status")
df_campus_status_join_last <- left_join(df_campus, data_status_campus) |>
  rename(Institution = `Perguruan Tinggi`) |>
  select(Institution, Score, Status)

df_campus_status_join_last_2nd <- left_join(df_campus, data_status_campus) |>
  rename(`Institution 2nd` = `Perguruan Tinggi`) |>
  select(`Institution 2nd`, Score, Status)

df_campus_status_join_last_checked <- left_join(df_campus, data_status_campus) |>
  rename(Institution = `Perguruan Tinggi`) |>
  mutate(Institution = trimws(tolower(Institution))) |>
  select(Institution, Score, Status)

df_campus_status_join_last_2nd_checked <- left_join(df_campus, data_status_campus) |>
  rename(`Institution 2nd` = `Perguruan Tinggi`) |>
  mutate(`Institution 2nd` = trimws(tolower(`Institution 2nd`))) |>
  select(`Institution 2nd`, Score, Status)

df_campus_join <- df_campus |>
  select(`Perguruan Tinggi`, Score) |>
  group_by(`Perguruan Tinggi`) |>
  slice(1) |>
  ungroup()

df_campus_join_checked <- df_campus |>
  mutate(`Perguruan Tinggi` = trimws(tolower(`Perguruan Tinggi`))) |>
  select(`Perguruan Tinggi`, Score) |>
  group_by(`Perguruan Tinggi`) |>
  slice(1) |>
  ungroup()

print("Data Experience Processing - Main Table")
current_date <- format(Sys.Date(), "%d/%m/%y")
current_date_time <- paste(current_date, "00:00")
`Major (Cluster)` <- config_education$norma_education[1,2]
`Major (Specific)` <- config_education$norma_education[1,2]
`Industry` <- config_education$norma_education[1,2]

df_experience_clean <- df_experience |>
  mutate(`Applied Date` = dmy_hm(`Applied Date`),
         starts_at = dmy_hm(starts_at),
         starts_at = as.Date(starts_at),
         start_month = as.yearmon(starts_at),
         ends_at = ifelse(is.na(ends_at), current_date_time, ends_at),
         ends_at = dmy_hm(ends_at),
         ends_at = as.Date(ends_at),
         end_month = as.yearmon(ends_at),
         `YoE Month` = floor(interval(start_month, end_month) / months(1)),
         `YoE Year` = round(`YoE Month`/12, 2),
         JobRoleRelevancy = ifelse(`Job Role` != "", grepl(`Major (Cluster)`, `Job Role`), ""),
         JobRoleFieldRelevancy = ifelse(industry != "", grepl(`Major (Cluster)`, industry), "")) |>
  filter(work_type != "intern")

# Save File
# write.csv(df_experience_clean, "df_experience_clean.csv")

df_experience_clean <- df_experience_clean |>
  select(`User ID`, JobRoleRelevancy, JobRoleFieldRelevancy, `YoE Year`) |>
  group_by(`User ID`, JobRoleRelevancy, JobRoleFieldRelevancy) |>
  summarise(`YoE Year` = sum(`YoE Year`)) |>
  ungroup() |>
  rowwise() |>
  mutate(
    ExperienceScore = ifelse(
      nrow(df_experience_clean) > 0,
      data_we |>
      filter(
        PositionRelevancy == JobRoleRelevancy &
        FieldRelevancy == JobRoleFieldRelevancy &
        `YoE Year` >= Min_Exp &
        `YoE Year` < Max_Exp
      ) |>
      pull(Score),
      0
    )
  ) |>
  ungroup() |>
  replace_na(list(ExperienceScore = 0))

print("Data Experience Processing - Max Experience Score Table")
df_experience_max_experience <- df_experience_clean |>
  #filter(work_type %in% "full_time") |>
  group_by(`User ID`) |>
  summarise(Total_ExperienceScore = sum(ExperienceScore)) |>
  mutate(Max_ExperienceScore = if_else(Total_ExperienceScore > 5 , 5, Total_ExperienceScore))

## Data Experience Processing - JobRoleRelevant WE (years) Table
df_experience_job_relevant_we <- df_experience_clean |>
  filter(#work_type %in% "full_time",
    JobRoleRelevancy %in% TRUE
  ) |>
  group_by(`User ID`) |>
  summarise(jobrelevant_we = sum(`YoE Year`))

## Data Experience Processing - JobRole+FieldRelevant WE (years) Table
df_experience_job_industry_relevant_we <- df_experience_clean |>
  filter(#work_type %in% "full_time",
    JobRoleRelevancy %in% TRUE,
    JobRoleFieldRelevancy %in% TRUE
  ) |>
  group_by(`User ID`) |>
  summarise(job_industry_relevant_we = sum(`YoE Year`))

## Data Experience Processing - IrRelevant WE (years) Table
df_experience_job_irrelevant_we <- df_experience_clean |>
  filter(#work_type %in% "full_time",
    JobRoleRelevancy %in% FALSE
  ) |>
  group_by(`User ID`) |>
  summarise(irrelevant_we = sum(`YoE Year`))

print("Data Calculation Process - Candidate Institution Score & Status")
df_campus_status_join_last <- df_campus_status_join_last |>
  group_by(Institution) |>
  slice(1) |>
  ungroup()

df_campus_status_join_last_2nd <- df_campus_status_join_last_2nd |>
  group_by(`Institution 2nd`) |>
  slice(1) |>
  ungroup()

df_candidate_join_status_campus <- df_candidate |>
  #mutate(Institution = trimws(tolower(Institution))) |>
  left_join(df_campus_status_join_last, by = "Institution") |>
  rename(LastInstitutionScore = Score,
         LastInstitutionRank = Status)

df_candidate_join_status_campus <- df_candidate_join_status_campus |>
  #mutate(`Institution 2nd` = trimws(tolower(`Institution 2nd`))) |>
  left_join(df_campus_status_join_last_2nd, by = "Institution 2nd") |>
  rename(SecondLastInstitutionScore = Score,
         SecondLastInstitutionRank = Status)

institution_notjoin <- df_candidate_join_status_campus |>
  anti_join(df_campus_status_join_last, by = "Institution") |>
  filter(!is.na(Institution))

institution2nd_notjoin <- df_candidate_join_status_campus |>
  anti_join(df_campus_status_join_last_2nd, by = "Institution 2nd") |>
  filter(!is.na(`Institution 2nd`))

institution_notjoin_fine <- institution_notjoin |>
  mutate(Institution = trimws(tolower(Institution))) |>
  left_join(df_campus_status_join_last_checked, by = "Institution") |>
  mutate(LastInstitutionScore = Score,
         LastInstitutionRank = Status
  ) |>
  filter(!is.na(LastInstitutionScore))

institution2nd_notjoin_fine <- institution2nd_notjoin |>
  mutate(`Institution 2nd` = trimws(tolower(`Institution 2nd`))) |>
  left_join(df_campus_status_join_last_2nd_checked, by = "Institution 2nd") |>
  mutate(SecondLastInstitutionScore = Score,
         SecondLastInstitutionRank = Status
  ) |>
  filter(!is.na(SecondLastInstitutionScore))

institution_notjoin_combine <- rbind(institution_notjoin_fine, institution2nd_notjoin_fine)

df_candidate_join_status_campus <- df_candidate_join_status_campus |>
  left_join(institution_notjoin_fine |> select(`User ID`, LastInstitutionScore, LastInstitutionRank), by = "User ID") |>
  mutate(
    LastInstitutionScore = ifelse(!is.na(LastInstitutionScore.y), LastInstitutionScore.y, LastInstitutionScore.x),
    LastInstitutionRank = ifelse(!is.na(LastInstitutionRank.y), LastInstitutionRank.y, LastInstitutionRank.x)
  ) |>
  select(-LastInstitutionScore.x, -LastInstitutionRank.x, -LastInstitutionScore.y, -LastInstitutionRank.y)

df_candidate_join_status_campus <- df_candidate_join_status_campus |>
  left_join(institution2nd_notjoin_fine |> select(`User ID`, SecondLastInstitutionScore, SecondLastInstitutionRank), by = "User ID") |>
  mutate(
    SecondLastInstitutionScore = ifelse(!is.na(SecondLastInstitutionScore.y), SecondLastInstitutionScore.y, SecondLastInstitutionScore.x),
    SecondLastInstitutionRank = ifelse(!is.na(SecondLastInstitutionRank.y), SecondLastInstitutionRank.y, SecondLastInstitutionRank.x)
  ) |>
  select(-SecondLastInstitutionScore.x, -SecondLastInstitutionRank.x, -SecondLastInstitutionScore.y, -SecondLastInstitutionRank.y)

print("Mapped Data")
posting_date <- "09/06/2024"

posting_date <- dmy(posting_date)

posting_date <- as.Date(posting_date)

df_experience_job_relevant_we <- df_experience_job_relevant_we |>
  mutate(`User ID` = as.integer(`User ID`))

df_experience_job_industry_relevant_we <- df_experience_job_industry_relevant_we |>
  mutate(`User ID` = as.integer(`User ID`))

df_experience_job_irrelevant_we <- df_experience_job_irrelevant_we |>
  mutate(`User ID` = as.integer(`User ID`))

df_candidate_join_status_campus <- df_candidate_join_status_campus |>
  mutate(`User ID` = as.integer(`User ID`))

df_candidate_join_status_campus_mapped_data <- df_candidate_join_status_campus |>
  select(-Provinces) |>
  mutate(dob = dmy_hm(dob),
         dob = as.Date(dob),
         age = ifelse(dob < posting_date,
                      ifelse(time_length(interval(dob, posting_date), "years") <= 60,
                             time_length(interval(dob, posting_date), "years"),
                             0),
                      0),
         age = floor(age)) |>
  rename(DomisiliKotaKab = `Kota - Kab`
         #DomisiliProvince = Provinces
  ) |>
  left_join(df_domisili, by = "DomisiliKotaKab") |>
  left_join(df_experience_job_relevant_we, by = "User ID") |>
  left_join(df_experience_job_industry_relevant_we, by = "User ID") |>
  left_join(df_experience_job_irrelevant_we, by = "User ID") |>
  replace_na(list(jobrelevant_we = 0, job_industry_relevant_we = 0, irrelevant_we = 0)) |>
  select(
    `User ID`, Fullname,  Degree, Major, Institution, GPA, LastInstitutionScore, LastInstitutionRank,
    `Degree 2nd`, `Major 2nd`, `Institution 2nd`, `GPA 2nd`, SecondLastInstitutionScore, SecondLastInstitutionRank,
    age, jobrelevant_we, job_industry_relevant_we, irrelevant_we, DomisiliKotaKab, DomisiliProvince,
    user_job_vacancy_id
  ) |>
  rename(LastDegree = Degree,
         LastMajor = Major,
         LastInstitution = Institution,
         LastGPA = GPA,
         SecondLastDegree = `Degree 2nd`,
         SecondLastMajor = `Major 2nd`,
         SecondLastInstitution = `Institution 2nd`,
         SecondLastGPA = `GPA 2nd`
  )

print("Filtered Helper")
df_candidate_join_status_campus_mapped_data_filter_helper <- df_candidate_join_status_campus_mapped_data |>
  mutate(EduxWEHelper = ifelse(!is.na(LastDegree) & LastDegree != "",
                               ifelse(LastDegree == "S2", TRUE,
                                      ifelse(LastDegree %in% c("S1", "D4") & jobrelevant_we >= 2, TRUE, FALSE)),
                               FALSE),

         Education = ifelse((!is.na(LastDegree) & LastDegree != "") | (!is.na(SecondLastDegree) & SecondLastDegree != ""),
                            ifelse(LastDegree %in% c("S3", "S2", "S1", "D4") | SecondLastDegree %in% c("S3", "S2", "S1", "D4"), TRUE, FALSE),
                            NA),

         AgeHelper = ifelse(!is.na(age) & age != "",
                            ifelse(age < 31, TRUE, FALSE),
                            NA),

         JobRole_FieldRelevancyHelper = ifelse(!is.na(age) & age != "",
                                               ifelse(job_industry_relevant_we > 0, TRUE, FALSE),
                                               NA))

# norma_standardized_s3_s2 <- data.frame(
#   Description = c("Custom (InJourney) - 37.00 or above", "Custom (InJourney) - 35.00 - 36,99", "Custom (InJourney) - 32.00 - 34.99", "Custom (InJourney) - 29.00 - 31.99", "Custom (InJourney) - 27.00 - 28.99", "Custom (InJourney) - 25.00 - 26.99", "Custom (InJourney) - 25.00 > 0"),
#   Minimum = c(3.7, 3.5, 3.2, 2.9, 2.7, 2.5, 2.5),
#   Maximum = c(4, 3.7, 3.5, 3., 2.9, 2.7, 0),
#   Score = c(5,5, 4, 3, 2, 1, 0)
# )
#
# norma_standardized_s1 <- data.frame(
#   Description = c("Standard - 35. 00 or above", "Standard - 32.00 - 34,99", "Standard - 29.00 - 31.99", "Standard - 27.00 - 28.99", "Standard - 25.00 - 26.99", "Standard - 25.00 > 0"),
#   Minimum = c(3.5, 3.2, 2.9, 2.7, 2.5, 2.5),
#   Maximum = c(4, 35, 3.2, 2.9, 2., 0),
#   Score = c(5, 4, 3,2, 1, 0)
# )
#
# norma_standardized_d3 <- data.frame(
#   Description = c("30.00 or above", "below 30.00"),
#   Minimum = c(3, 0),
#   Maximum = c(4, 3),
#   Score = c(1, 0)
# )

# df_norma_standardized_age <- data_age

## Translated Data
norma_standardized_s3_s2 <- data_gpa_above_bachelor
norma_standardized_s1 <- data_gpa_bachelor
norma_standardized_d3 <- data_gpa_below_bachelor

df_norma_standardized_age <- data_age

df_candidate_join_status_campus_mapped_data_filter_helper_translated <- df_candidate_join_status_campus_mapped_data_filter_helper |>
  mutate(
    GPA_S3 = ifelse((!is.na(LastDegree) & LastDegree != "") | (!is.na(SecondLastDegree) & SecondLastDegree != ""),
                    ifelse(LastDegree == "S3", LastGPA,
                           ifelse(SecondLastDegree == "S3", SecondLastGPA, 0)),
                    0),

    GPA_S2 = ifelse((!is.na(LastDegree) & LastDegree != "") | (!is.na(SecondLastDegree) & SecondLastDegree != ""),
                    ifelse(LastDegree == "S2", LastGPA,
                           ifelse(SecondLastDegree == "S2", SecondLastGPA, 0)),
                    0),

    GPA_S1_D4 = ifelse((!is.na(LastDegree) & LastDegree != "") | (!is.na(SecondLastDegree) & SecondLastDegree != ""),
                       ifelse(LastDegree %in% c("S1", "D4"), LastGPA,
                              ifelse(SecondLastDegree %in% c("S1", "D4"), SecondLastGPA, 0)),
                       0),

    GPA_D3 = ifelse((!is.na(LastDegree) & LastDegree != "") | (!is.na(SecondLastDegree) & SecondLastDegree != ""),
                    ifelse(LastDegree == "D3", LastGPA,
                           ifelse(SecondLastDegree == "D3", SecondLastGPA, 0)),
                    0),

    GPA_S3_Scored = case_when(
      GPA_S3 >= norma_standardized_s3_s2$Minimum[1] & GPA_S3 < norma_standardized_s3_s2$Maximum[1] ~ norma_standardized_s3_s2$Score[1],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[2] & GPA_S3 < norma_standardized_s3_s2$Maximum[2] ~ norma_standardized_s3_s2$Score[2],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[3] & GPA_S3 < norma_standardized_s3_s2$Maximum[3] ~ norma_standardized_s3_s2$Score[3],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[4] & GPA_S3 < norma_standardized_s3_s2$Maximum[4] ~ norma_standardized_s3_s2$Score[4],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[5] & GPA_S3 < norma_standardized_s3_s2$Maximum[5] ~ norma_standardized_s3_s2$Score[5],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[6] & GPA_S3 < norma_standardized_s3_s2$Maximum[6] ~ norma_standardized_s3_s2$Score[6],
      GPA_S3 >= norma_standardized_s3_s2$Minimum[7] & GPA_S3 < norma_standardized_s3_s2$Maximum[7] ~ norma_standardized_s3_s2$Score[7],
      TRUE ~ 0
    ),

    GPA_S2_Scored = case_when(
      GPA_S2 >= norma_standardized_s3_s2$Minimum[1] & GPA_S2 <= norma_standardized_s3_s2$Maximum[1] ~ norma_standardized_s3_s2$Score[1],
      GPA_S2 >= norma_standardized_s3_s2$Minimum[2] & GPA_S2 < norma_standardized_s3_s2$Maximum[2] ~ norma_standardized_s3_s2$Score[2],
      GPA_S2 >= norma_standardized_s3_s2$Minimum[3] & GPA_S2 < norma_standardized_s3_s2$Maximum[3] ~ norma_standardized_s3_s2$Score[3],
      GPA_S2 >= norma_standardized_s3_s2$Minimum[4] & GPA_S2 < norma_standardized_s3_s2$Maximum[4] ~ norma_standardized_s3_s2$Score[4],
      GPA_S2 >= norma_standardized_s3_s2$Minimum[5] & GPA_S2 < norma_standardized_s3_s2$Maximum[5] ~ norma_standardized_s3_s2$Score[5],
      GPA_S2 >= norma_standardized_s3_s2$Minimum[6] & GPA_S2 < norma_standardized_s3_s2$Maximum[6] ~ norma_standardized_s3_s2$Score[6],
      GPA_S2 < norma_standardized_s3_s2$Minimum[7] ~ norma_standardized_s3_s2$Score[7],
      TRUE ~ 0
    ),

    GPA_S1_Scored = case_when(
      GPA_S1_D4 >= norma_standardized_s1$Minimum[1] & GPA_S1_D4 <= norma_standardized_s1$Maximum[1] ~ norma_standardized_s1$Score[1],
      GPA_S1_D4 >= norma_standardized_s1$Minimum[2] & GPA_S1_D4 < norma_standardized_s1$Maximum[2] ~ norma_standardized_s1$Score[2],
      GPA_S1_D4 >= norma_standardized_s1$Minimum[3] & GPA_S1_D4 < norma_standardized_s1$Maximum[3] ~ norma_standardized_s1$Score[3],
      GPA_S1_D4 >= norma_standardized_s1$Minimum[4] & GPA_S1_D4 < norma_standardized_s1$Maximum[4] ~ norma_standardized_s1$Score[4],
      GPA_S1_D4 >= norma_standardized_s1$Minimum[5] & GPA_S1_D4 < norma_standardized_s1$Maximum[5] ~ norma_standardized_s1$Score[5],
      GPA_S1_D4 < norma_standardized_s1$Minimum[6] ~ norma_standardized_s1$Score[6],
      TRUE ~ 0
    ),

    GPA_D3_Scored = case_when(
      GPA_D3 >= norma_standardized_d3$Minimum[1] & GPA_D3 <= norma_standardized_d3$Maximum[1] ~ norma_standardized_d3$Score[1],
      GPA_D3 >= norma_standardized_d3$Minimum[2] & GPA_D3 < norma_standardized_d3$Maximum[2] ~ norma_standardized_d3$Score[2],
      TRUE ~ 0)) |> #,


  mutate(LastInstitution = trimws(tolower(LastInstitution)),
         SecondLastInstitution = trimws(tolower(SecondLastInstitution))
  ) |>



  mutate(



    Campus_S3_Existed = case_when(
      (LastDegree == "S3" & !is.na(LastDegree)) ~ map_chr(LastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      (SecondLastDegree == "S3" & !is.na(SecondLastDegree)) ~ map_chr(SecondLastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      TRUE ~ ""
    ),

    Campus_S2_Existed = case_when(
      (LastDegree == "S2" & !is.na(LastDegree)) ~ map_chr(LastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      (SecondLastDegree == "S2" & !is.na(SecondLastDegree)) ~ map_chr(SecondLastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      TRUE ~ ""
    ),

    Campus_S1_Existed = case_when(
      (LastDegree %in% c("S1", "D4") & !is.na(LastDegree)) ~ map_chr(LastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      (SecondLastDegree %in% c("S1", "D4") & !is.na(SecondLastDegree)) ~ map_chr(SecondLastInstitution, ~ ifelse(.x %in% df_campus_join_checked$`Perguruan Tinggi`, .x, "NOT EXIST")),
      TRUE ~ ""
    ) #,

  ) |>


  left_join(df_campus_join_checked, by = c("LastInstitution" = "Perguruan Tinggi")) |>
  rename(Score_Last = Score) |>
  left_join(df_campus_join_checked, by = c("SecondLastInstitution" = "Perguruan Tinggi")) |>
  rename(Score_SecondLast = Score) |>
  mutate(
    Campus_S3_Scored = ifelse((!is.na(LastDegree) | !is.na(SecondLastDegree)),
                              ifelse(LastDegree == "S3", Score_Last,
                                     ifelse(SecondLastDegree == "S3", Score_SecondLast, 0)), 1),
    Campus_S2_Scored = ifelse((!is.na(LastDegree) | !is.na(SecondLastDegree)),
                              ifelse(LastDegree == "S2", Score_Last,
                                     ifelse(SecondLastDegree == "S2", Score_SecondLast, 0)), 1),
    Campus_S1_Scored = ifelse((!is.na(LastDegree) | !is.na(SecondLastDegree)),
                              ifelse(LastDegree %in% c("S1", "D4"), Score_Last,
                                     ifelse(SecondLastDegree %in% c("S1", "D4"), Score_SecondLast, 0)), 1),
    Campus_D3_Scored = ifelse((!is.na(LastDegree) | !is.na(SecondLastDegree)),
                              ifelse(LastDegree == "D3", Score_Last,
                                     ifelse(SecondLastDegree == "D3", Score_SecondLast, 0)), 1)
  ) |>
  mutate(
    LastMajorRelevancy = ifelse(LastMajor != "", str_detect(tolower(LastMajor), "[a-zA-Z ]+"), ""),
    SecondLastMajorRelevancy = ifelse(SecondLastMajor != "", str_detect(tolower(SecondLastMajor), "[a-zA-Z ]+"), ""),

    AgeScored = case_when(
      age == "" ~ 0,
      between(age, df_norma_standardized_age$Minimum_Age[1], df_norma_standardized_age$Maximum_Age[1]) ~ df_norma_standardized_age$Score[1],
      between(age, df_norma_standardized_age$Minimum_Age[2], df_norma_standardized_age$Maximum_Age[2]) ~ df_norma_standardized_age$Score[2],
      between(age, df_norma_standardized_age$Minimum_Age[3], df_norma_standardized_age$Maximum_Age[3]) ~ df_norma_standardized_age$Score[3],
      between(age, df_norma_standardized_age$Minimum_Age[4], df_norma_standardized_age$Maximum_Age[4]) ~ df_norma_standardized_age$Score[4],
      between(age, df_norma_standardized_age$Minimum_Age[5], df_norma_standardized_age$Maximum_Age[5]) ~ df_norma_standardized_age$Score[5],
      TRUE ~ 0
    )

  ) |>

  left_join(df_experience_max_experience, by = c("User ID" = "User ID")) |>
  mutate(
    WEScore = ifelse(is.na(Max_ExperienceScore), 0, Max_ExperienceScore)
  ) |>
  rename(WEScored = WEScore)

df_candidate_join_status_campus_mapped_data_filter_helper_translated |>
  #select(-c("Peringkat", "No. SK", "Tahun SK", "Wilayah", "Tanggal Kadaluwarsa")) |>
  distinct()

print("Calculated Data")
df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated <- df_candidate_join_status_campus_mapped_data_filter_helper_translated |>
  select(-Max_ExperienceScore) |>
  replace_na(list(Campus_S3_Scored = 0, Campus_S2_Scored = 0, Campus_S1_Scored = 0, Campus_D3_Scored = 0))

# Function to check if two values match, considering NA
match_value <- function(x, y) {
  (is.na(x) & is.na(y)) | (!is.na(x) & !is.na(y) & x == y)
}

# Function to check if a pattern matches, considering NA
match_pattern <- function(x, pattern) {
  is.na(x) | grepl(pattern, tolower(x))
}

# Main function to calculate EducationScore
calculate_education_score <- function(df, data_education) {
  df |>
    rowwise() |>
    mutate(
      EducationScore = case_when(
        is.na(LastDegree) | LastDegree == "" ~ 0,
        TRUE ~ {
          matching_row <- which(
            match_value(LastDegree, data_education$LastEducation) &
              match_pattern(LastMajor, data_education$LastMajor) &
              match_value(SecondLastDegree, data_education$SecondLastEducation) &
              match_pattern(SecondLastMajor, data_education$SecondLastMajor)
          )
          if (length(matching_row) > 0) data_education$Score[matching_row[1]] else 0
        }
      )
    ) |>
    ungroup()
}


# Usage
df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated <- calculate_education_score(df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated, data_education)

df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated <- df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated |>
  mutate(

    AgeScore = AgeScored,

    WEScore = WEScored,

    GPAScore = ifelse(`User ID` != "", GPA_S2_Scored + GPA_S1_Scored, NA),

    CampusScore = ifelse(`User ID` != "", Campus_S2_Scored + Campus_S1_Scored, NA),

    GPAxCampusScore = ifelse(`User ID` != "", (GPA_S3_Scored * Campus_S3_Scored + GPA_S2_Scored * Campus_S2_Scored + GPA_S1_Scored * Campus_S1_Scored + GPA_D3_Scored * Campus_D3_Scored) / 5, NA),

    EducationConstanta = ifelse(`User ID` != "", (weight_education/all_weight), NA),
    AgeConstanta = ifelse(`User ID` != "", (weight_age/all_weight), NA),
    WEConstanta = ifelse(`User ID` != "", (weight_we/all_weight), NA),
    GPAxCampusConstanta = ifelse(`User ID` != "", ((weight_gpa + weight_campus)/all_weight), NA),

    Edu_raw = ifelse(`User ID` != "", EducationScore * EducationConstanta, NA),
    Age_raw = ifelse(`User ID` != "", AgeScored * AgeConstanta, NA),
    WE_raw = ifelse(`User ID` != "", WEScore * WEConstanta, NA),
    GPAxCampus_raw = ifelse(`User ID` != "", GPAxCampusScore * GPAxCampusConstanta, NA),

    calculation_raw = ifelse(`User ID` != "", GPAxCampusScore * GPAxCampusConstanta + EducationScore * EducationConstanta + AgeScored * AgeConstanta + WEScore * WEConstanta, NA),

    Score = ifelse(calculation_raw != "", calculation_raw / 5 * 100, NA),


    # Sisa ini, perlu di ganti ke config
    CutOffStatus = ifelse(Score != "", ifelse(Score >= CutOffValue, "PASSED", "NOT PASSED"), NA)

  )



df_result <- df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated |>
  arrange(desc(Score))

# Save File
# write.csv(df_result, "df_result.csv")

glimpse(df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated)
print(df_candidate_join_status_campus_mapped_data_filter_helper_translated_calculated)

df_result <- df_result |>
  select(Score, CutOffStatus, user_job_vacancy_id) |>
  rename(
    score = Score,
    status = CutOffStatus
  ) |>
  select(user_job_vacancy_id, score, status) |>
  drop_na() |>
  distinct()

glimpse(df_result)
print(df_result)

print("Write for DB")
if (nrow(df_result) > 0) {
  schema <- params$schema
  schema_sql <- paste0("SET search_path = ?schema;")
  safe_schema_sql <- sqlInterpolate(con_write, schema_sql, schema = schema)
  dbExecute(con_write, safe_schema_sql)

  query <- 'INSERT INTO user_vacancy_matchmaking_results (user_job_vacancy_id, score, status, created_at, updated_at) VALUES'

  for(i in 1:nrow(df_result)) {
    is_first <- i == 1
    is_last <- i == nrow(df_result)

    if (i %% 1000 == 0) {
      query <- paste0(
        query,
        "ON CONFLICT (user_job_vacancy_id) DO UPDATE SET score = EXCLUDED.score, status = EXCLUDED.status, updated_at = EXCLUDED.updated_at;"
      )

      DBI::dbExecute(con_write, query)
      query <- 'INSERT INTO user_vacancy_matchmaking_results (user_job_vacancy_id, score, status, created_at, updated_at) VALUES'
    } else if (!is_first) {
      query <- paste0(query, ",")
    } else {
      query <- paste0(query, " ")
    }

    query <- paste0(
      query,
      " (",
      df_result$user_job_vacancy_id[i], ", ",
      df_result$score[i], ", ",
      "'", df_result$status[i], "', ",
      "NOW(), ",
      "NOW()",
      ")"
    )

    if (is_last) {
      query <- paste0(
        query,
        "ON CONFLICT (user_job_vacancy_id) DO UPDATE SET score = EXCLUDED.score, status = EXCLUDED.status, updated_at = EXCLUDED.updated_at;"
      )
      DBI::dbExecute(con_write, query)
    }
  }
}

# Close connection
DBI::dbDisconnect(con_write)
DBI::dbDisconnect(con_read)

print("result")
your_data_processing_function <- function() {
  # Your data processing logic here
  df_result <- df_result
  return(df_result)
}

# Run the function to generate df_result
df_result <- your_data_processing_function()








































































































































