version: 2.1

orbs:
  aws-ecr: circleci/aws-ecr@6.15.3
  aws-eks: circleci/aws-eks@2.2.0

executors:
  k8s:
    working_directory: ~/scoring-candidate
    docker:
      - image: cimg/aws:2024.03.1

commands:
  rollout-deployment-staging:
    steps:
      - run:
          name: Rollout deployment
          command: |
            kubectl rollout restart deployment scoring-candidate
            kubectl annotate deploy/scoring-candidate kubernetes.io/change-cause="$CIRCLE_BRANCH:$CIRCLE_SHA1:latest"
  rollout-deployment-production:
    steps:
      - run:
          name: Rollout deployment
          command: |
            kubectl delete configmap scoring-candidate-version || true
            kubectl create configmap scoring-candidate-version --from-literal=VERSION=$IMAGE_TAG

            # Update image will trigger rollout restart deployments
            kubectl set image deploy/scoring-candidate scoring-candidate=$SCORING_CANDIDATE_IMAGE_URL:$IMAGE_TAG

            # Annotate the changes, so it will be useful for provide good informations on rollout history
            kubectl annotate deploy/scoring-candidate kubernetes.io/change-cause="$CIRCLE_BRANCH:$CIRCLE_SHA1:$IMAGE_TAG"
  set-tag-image:
    steps:
      - run:
          name: Set tag image
          command: |
            if [ -n "${CIRCLE_TAG}" ]; then
              echo "export RAW_TAG=${CIRCLE_TAG}" >> $BASH_ENV
              echo "export IMAGE_TAG=$(echo ${CIRCLE_TAG} | sed 's/v//')" >> $BASH_ENV
            else
              echo 'export RAW_TAG=$(echo ${CIRCLE_BRANCH})' >> $BASH_ENV
              echo 'export COMMIT_HASH=$(git log -1 --pretty=format:%h)' >> $BASH_ENV
              echo 'export IMAGE_TAG=$(echo ${CIRCLE_BRANCH} | sed "s/\//__/g")__${COMMIT_HASH}' >> $BASH_ENV
            fi

jobs:
  deploy-staging:
    executor: k8s
    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: false
      - aws-ecr/build-and-push-image:
          repo: scoring-candidate
          tag: latest
      - aws-eks/update-kubeconfig-with-authenticator:
          cluster-name: $CLUSTER_NAME
          install-kubectl: true
          kubectl-version: v1.27.7
          aws-region: $AWS_REGION
      - rollout-deployment-staging
  deploy-production:
    executor: k8s
    steps:
      - checkout
      - set-tag-image
      - setup_remote_docker:
          docker_layer_caching: false
      - aws-ecr/build-and-push-image:
          repo: scoring-candidate
          tag: $IMAGE_TAG
      - aws-eks/update-kubeconfig-with-authenticator:
          cluster-name: $CLUSTER_NAME
          install-kubectl: true
          kubectl-version: v1.27.7
          aws-region: $AWS_REGION
      - rollout-deployment-production

workflows:
  version: 2
  deploy-k8s-cluster:
    jobs:
      - approval:
          type: approval
      - deploy-staging:
          name: Deploy Scoring Candidate to Staging
          requires: [approval]
          filters:
            branches: { ignore: [master, /(release|hotfix|v)\/.*/] }
      - deploy-production:
          name: Deploy Scoring Candidate to PRODUCTION
          requires: [approval]
          filters:
            branches: { only: [master, /(release|hotfix|v)\/.*/] }
          context:
            - k8s-prod-secrets
