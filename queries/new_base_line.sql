SELECT * FROM (
    SELECT DISTINCT
        jv.name AS "Job Role",
        jv.id,
        jv.minimum_salary,
        jv.maximum_salary,
        jrg.name AS "Job Group Role",
        jr.name AS "Job Role Name",
        STRING_AGG(DISTINCT c.name, ', ') AS "Tools and Competencies Mastery",
        el.name AS "Education Level",
        ic.name AS "Previous Job Industry",
        l.name AS "Domicile",
        jv.job_level,
        jv.job_type,
        jv.job_vacancy_type,
        jv.work_mode,
        jv.min_age,
        jv.qualifications,
        jv.max_age,
        STRING_AGG(DISTINCT um.name, ', ') AS "Major"
    FROM job_vacancies jv
    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id
        AND jvc.discarded_at IS NULL
    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id
        AND jrg.discarded_at IS NULL
    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id
        AND jr.discarded_at IS NULL
    LEFT JOIN competencies c ON c.id = jvc.competency_id
        AND c.discarded_at IS NULL
    LEFT JOIN education_levels el ON el.id = jv.education_level_id
        AND el.discarded_at IS NULL
    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id
        AND ic.discarded_at IS NULL
    LEFT JOIN public.locations l ON l.id = jv.location_id
        AND l.discarded_at IS NULL
    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id
        AND jvum.discarded_at IS NULL
    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id
        AND um.discarded_at IS NULL
    WHERE jv.discarded_at IS NULL
    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,
        jv.job_type,
        jv.job_vacancy_type,
        jv.work_mode,
        jv.min_age,
        jv.qualifications,
        jv.max_age,
        jr.id,
        jv.id
) AS subquery
WHERE id = $1
