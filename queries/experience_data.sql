SELECT user_job_vacancies.applied_at AS "Applied Date", users.id AS "User ID",
  users.name AS "Fullname", job_roles.id as "Job Role ID", job_roles.name AS "Job Role",
  industry_categories.name AS industry, experiences.company_name, experiences.work_type,
  experiences.ends_at, experiences.starts_at, COALESCE(
    (
      EXTRACT(
        EPOCH FROM (
          COALESCE(experiences.ends_at, NOW()) - experiences.starts_at
        )
      ) / 60 / 60 / 24 / 30
    ),
    0.0
  ) AS "MoE"
FROM user_job_vacancies
JOIN users
  ON users.id=user_job_vacancies.user_id
  AND users.discarded_at IS NULL
LEFT JOIN experiences
  ON experiences.user_id=users.id
  AND experiences.discarded_at IS NULL
LEFT JOIN job_roles
  ON job_roles.id=experiences.job_role_id
  AND job_roles.discarded_at IS NULL
LEFT JOIN experience_industry_categories
  ON experience_industry_categories.experience_id=experiences.id
  AND experience_industry_categories.discarded_at IS NULL
LEFT JOIN industry_categories
  ON industry_categories.id=experience_industry_categories.industry_category_id
  AND industry_categories.discarded_at IS NULL
