WITH base_toefl_data AS (
    SELECT
        u.name as "Fullname",
        u.email as "Email",
        vq.question as "Question",
        uvqa.answer as "toefl_score",
        uvqa.user_id as "User ID",
        jv.name as "Role",
        ujv.id as "user_job_vacancy_id",
        jv.id as job_vacancy_id
    FROM vacancy_questions vq
    JOIN job_vacancy_section_questions jvsq
        ON jvsq.vacancy_question_id = vq.id
        AND jvsq.discarded_at IS NULL
    JOIN job_vacancy_sections jvs
        ON jvs.id = jvsq.job_vacancy_section_id
        AND jvs.discarded_at IS NULL
    JOIN job_vacancies jv
        ON jv.id = jvs.job_vacancy_id
        AND jv.discarded_at IS NULL
    JOIN user_vacancy_question_answers uvqa
        ON uvqa.vacancy_question_id = vq.id
    JOIN user_job_vacancies ujv
        ON ujv.job_vacancy_id = jv.id
        AND ujv.discarded_at IS NULL
    JOIN users u
        ON u.id = uvqa.user_id
        AND u.id = ujv.user_id
    WHERE vq.question ILIKE '%Skor TOEFL%'
)
SELECT
    btd."Fullname",
    btd."Email",
    btd."Question",
    btd."toefl_score",
    btd."User ID",
    btd."Role" as "Formation Apply",
    btd."user_job_vacancy_id",
    btd.job_vacancy_id
FROM base_toefl_data btd
