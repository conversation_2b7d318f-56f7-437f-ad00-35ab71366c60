WITH applied_user_vacancies AS(
  SELECT user_job_vacancies.user_id, job_vacancies.name AS job_vacancy_name,
    user_job_vacancies.applied_at, user_job_vacancies.state,
    user_job_vacancies.job_vacancy_id, user_job_vacancies.id
  FROM user_job_vacancies
  JOIN job_vacancies
    ON job_vacancies.id=user_job_vacancies.job_vacancy_id
    AND job_vacancies.discarded_at IS NULL
  WHERE user_job_vacancies.discarded_at IS NULL
    AND user_job_vacancies.state!='pool'
), user_educations AS(
  SELECT educations.user_id,
    COALESCE(education_levels.name, educations.degree) AS degree,
    COALESCE(university_majors.name, educations.major) AS major,
    COALESCE(universities.name, educations.school) AS school,
    educations.score AS gpa,
    ROW_NUMBER() OVER(
      PARTITION BY educations.user_id
      ORDER BY educations.start_date DESC NULLS LAST,
        educations.end_date DESC NULLS FIRST,
        educations.created_at DESC
    ) AS row_number
  FROM educations
  LEFT JOIN education_levels
    ON education_levels.id=educations.education_level_id
    AND education_levels.discarded_at IS NULL
  LEFT JOIN public.university_majors university_majors
    ON university_majors.id=educations.university_major_id
    AND university_majors.discarded_at IS NULL
  LEFT JOIN universities
    ON universities.id=educations.university_id
    AND universities.discarded_at IS NULL
  WHERE educations.discarded_at IS NULL
), first_educations AS(
  SELECT * FROM user_educations
  WHERE row_number=1
), second_educations AS(
  SELECT * FROM user_educations
  WHERE row_number=2
), user_experiences AS(
  SELECT experiences.user_id,
    COALESCE(job_roles.name, experiences.role_name) AS role_name,
    ROW_NUMBER() OVER(
      PARTITION BY experiences.user_id
      ORDER BY experiences.ends_at DESC NULLS FIRST,
        experiences.starts_at DESC NULLS LAST,
        experiences.created_at DESC
    ) AS row_number
  FROM experiences
  LEFT JOIN job_roles
    ON job_roles.id=experiences.job_role_id
    AND job_roles.discarded_at IS NULL
  WHERE experiences.discarded_at IS NULL
), last_experiences AS(
  SELECT * FROM user_experiences
  WHERE row_number=1
), user_total_experiences AS(
  SELECT experiences.user_id,
    SUM(
      COALESCE(experiences.ends_at, NOW()) - experiences.starts_at
    ) AS total_experience
  FROM experiences
  WHERE experiences.discarded_at IS NULL
    AND experiences.work_type != 'intern'
  GROUP BY experiences.user_id
), user_skill_and_tool_names AS(
  SELECT DISTINCT ON (job_infos.user_id, LOWER(competencies.name))
    job_infos.user_id,
    competencies.name
  FROM job_info_competencies
  JOIN competencies
    ON competencies.id = job_info_competencies.competency_id
    AND competencies.discarded_at IS NULL
  JOIN job_infos
    ON job_infos.id = job_info_competencies.job_info_id
  WHERE job_info_competencies.discarded_at IS NULL
    AND competencies.name IS NOT NULL
    AND competencies.name != ''
  ORDER BY job_infos.user_id, LOWER(competencies.name)
), user_skill_and_tools AS(
  SELECT user_skill_and_tool_names.user_id,
    ARRAY_AGG(user_skill_and_tool_names.name) AS skill_and_tool_names
  FROM user_skill_and_tool_names
  GROUP BY user_skill_and_tool_names.user_id
), candidate_datas AS(
  SELECT applied_user_vacancies.applied_at AS "Applied Date",
    applied_user_vacancies.job_vacancy_name AS "Role",
    users.email AS "Email", users.id AS "User ID",
    users.name AS "Fullname", locations.name AS "Kota - Kab",
    provinces.name AS "Provinces", first_educations.degree AS "Degree",
    first_educations.gpa AS "GPA", first_educations.major AS "Major",
    first_educations.school AS "Institution", second_educations.degree AS "Degree 2nd",
    second_educations.gpa AS "GPA 2nd", second_educations.major AS "Major 2nd",
    second_educations.school AS "Institution 2nd", last_experiences.role_name AS "Job Role",
    COALESCE(
      (EXTRACT(EPOCH FROM user_total_experiences.total_experience) / 60 / 60 / 24 / 365),
      0.0
    ) as "YoE", 0 AS "Experience", user_skill_and_tools.skill_and_tool_names AS "Skill & Tools",
    users.resume_url AS "CV Upload", users.linkedin_url AS "Linkedin Link",
    users.portfolio_url AS "Portfolio Link", users.instagram_url AS "Instagram Link",
    users.twitter_url AS "Twitter Link", applied_user_vacancies.state AS "State",
    users.status_availability AS "Status Availability", users.date_of_birth AS "dob",
    users.place_of_birth AS "pob", users.address_ktp AS "ktp", users.address,
    users.phone_number as hp, users.gender, user_digital_cv_settings.unique_address AS dcp,
    job_infos.latest_salary_ends_at AS current_max, job_infos.latest_salary_starts_at as current_min,
    job_infos.expected_salary_ends_at as expect_max, job_infos.expected_salary_starts_at as expect_min,
    applied_user_vacancies.job_vacancy_id, applied_user_vacancies.id AS user_job_vacancy_id,
    user_aggregates.calculated_years_experience AS "Calculated YoE"
  FROM applied_user_vacancies
  JOIN users
    ON users.id=applied_user_vacancies.user_id
    AND users.discarded_at IS NULL
  LEFT JOIN public.locations locations
    ON locations.id=users.location_id
    AND locations.discarded_at IS NULL
  LEFT JOIN provinces
    ON provinces.id=users.province_id
    AND provinces.discarded_at IS NULL
  LEFT JOIN first_educations
    ON first_educations.user_id=users.id
  LEFT JOIN second_educations
    ON second_educations.user_id=users.id
  LEFT JOIN last_experiences
    ON last_experiences.user_id=users.id
  LEFT JOIN user_total_experiences
    ON user_total_experiences.user_id=users.id
  LEFT JOIN user_skill_and_tools
    ON user_skill_and_tools.user_id=users.id
  LEFT JOIN user_digital_cv_settings
    ON user_digital_cv_settings.user_id=users.id
    AND user_digital_cv_settings.discarded_at IS NULL
  LEFT JOIN job_infos
    ON job_infos.user_id=users.id
  LEFT JOIN user_aggregates
    ON user_aggregates.user_id=users.id
)

SELECT candidate_datas.* from candidate_datas
