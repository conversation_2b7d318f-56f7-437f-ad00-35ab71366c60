library(dplyr)
library(stringdist)
library(textTinyR)
library(lubridate)
library(tidyr)

# Example data
user_inputs <- data.frame(
  Institution = c("Harvrd Univrsty", "Stanford", "UGM"),
  stringsAsFactors = FALSE
)

mapped_data <- data.frame(
  actual_input = c("Harvard University", "Stanford University", "Massachusetts Institute of Technology", "MIT"),
  mapped_input = c("Harvard University", "Stanford University", "Massachusetts Institute of Technology", "Massachusetts Institute of Technology"),
  rank_value = c("QS World Ranking 1 - 100", "QS World Ranking 1 - 100", "QS World Ranking 1 - 100", "QS World Ranking 1 - 100"),
  return_value = c(2.5, 2.5, 3.0, 3.0),
  stringsAsFactors = FALSE
)

match_and_score_with_embedding <- function(user_inputs, mapped_data, threshold = 80, embedding_threshold = 0.75) {
  results <- user_inputs %>%
    rowwise() %>%
    mutate(
      # String distance-based matching
      match_details = list(
        mapped_data %>%
          mutate(
            exact_ratio = stringdist(Institution, actual_input, method = "jw") %>%
              {1 - .} * 100,
            partial_ratio = stringdist(Institution, actual_input, method = "lv") %>%
              {1 - .} * 100,
            token_sort_ratio = stringdist(Institution, actual_input, method = "cosine") %>%
              {1 - .} * 100
          ) %>%
          filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold) %>%
          slice_max(order_by = exact_ratio, n = 1) # Select the best match
      )
    ) %>%
    # Fallback to embedding-based matching for unmatched cases
    mutate(
      match_details = if (length(match_details[[1]]) == 0) {
        # Compute embeddings
        user_embedding <- BERT_VECTOR(Institution, model = "bert_base_multilingual_cased", method = "mean_pooling")
        mapped_embeddings <- BERT_VECTOR(mapped_data$actual_input, model = "bert_base_multilingual_cased", method = "mean_pooling")

        embedding_similarities <- textTinyR::cosine_similarity(user_embedding, mapped_embeddings)
        best_match_idx <- which.max(embedding_similarities)

        if (embedding_similarities[best_match_idx] >= embedding_threshold) {
          tibble(
            actual_input = Institution,
            mapped_input = mapped_data$actual_input[best_match_idx],
            rank_value = mapped_data$rank_value[best_match_idx],
            return_value = mapped_data$return_value[best_match_idx],
            exact_ratio = 0, # Embedding-based match, so no string distance
            partial_ratio = 0,
            token_sort_ratio = 0,
            embedding_score = embedding_similarities[best_match_idx] * 100
          )
        } else {
          tibble(
            actual_input = Institution,
            mapped_input = "No Match Found",
            rank_value = "Not Ranked",
            return_value = 0,
            exact_ratio = 0,
            partial_ratio = 0,
            token_sort_ratio = 0,
            embedding_score = 0
          )
        }
      } else {
        match_details
      }
    ) %>%
    unnest(match_details) %>%
    mutate(
      pass = exact_ratio >= threshold | embedding_score >= embedding_threshold * 100,
      created_at = Sys.time(),
      updated_at = Sys.time(),
      input_table_name = "universities"
    ) %>%
    select(
      created_at, updated_at, actual_input, mapped_value = mapped_input, rank_value,
      return_value, exact_ratio, partial_ratio, token_sort_ratio, embedding_score, pass, input_table_name
    )

  return(results)
}



# Apply the function
final_results <- match_and_score_with_embedding(user_inputs, mapped_data)

# View results
print(final_results)
View(final_results)
