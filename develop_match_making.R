# Library

library(dplyr)
library(tidyr)
library(readr)
library(lubridate)
library(zoo)
library(purrr)
library(stringi)
library(stringr)
library(aws.s3)
library(DBI)
library(RODBC)
library(RPostgreSQL)
library(jsonlite)
library(yaml)
library(stringdist)

# Read ENV Var
readRenviron(".env")

# AWS Setup
AWS_ACCESS_KEY_ID <- Sys.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY <- Sys.getenv("AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION <- Sys.getenv("AWS_DEFAULT_REGION")

#<---COMMENT--->#

# DB Connection

## Read
# dbname_read <- Sys.getenv("DB_NAME_READ")
# host_read <- Sys.getenv("DB_HOST_READ")
# port_read <- Sys.getenv("DB_PORT_READ")
# user_read <- Sys.getenv("DB_USER_READ")
# password_read <- Sys.getenv("DB_PASSWORD_READ")
#
# con_read <- dbConnect(
#   RPostgres::Postgres(),
#   dbname = dbname_read,
#   host = host_read,
#   port = port_read,
#   user = user_read,
#   password = password_read
# )

## Write
# dbname_write <- Sys.getenv("DB_NAME_WRITE")
# host_write <- Sys.getenv("DB_HOST_WRITE")
# port_write <- Sys.getenv("DB_PORT_WRITE")
# user_write <- Sys.getenv("DB_USER_WRITE")
# password_write <- Sys.getenv("DB_PASSWORD_WRITE")
#
# con_write <- dbConnect(
#   RPostgres::Postgres(),
#   dbname = dbname_write,
#   host = host_write,
#   port = port_write,
#   user = user_write,
#   password = password_write
# )

# Recruiter Input
# print("Recruiter Input")


# print("Base Line 2")
## Data for Schema

## Read config from db
# schema <- params$schema
# job_vacancy_id <- params$job_vacancy_id
#
# schema_sql <- paste0("SET search_path = ?schema;")
# safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
# dbExecute(con_read, safe_schema_sql)
#
# sql_filename <- 'queries/base_line.sql'
# query <- paste(readLines(sql_filename), collapse = "\n")
#
# # Prepare the statement
# stmt <- dbSendQuery(con_read, query)
#
# # Bind the parameter
# dbBind(stmt, list(job_vacancy_id))
#
# # Execute the query and fetch results
# result <- dbFetch(stmt)
#
# # Clear the result
# dbClearResult(stmt)
#
# df_base_line <- as.data.frame(result)
#
# print(query)
# print(typeof(job_vacancy_id))
# print(job_vacancy_id)
# print(dbIsValid(con_read))
#
# glimpse(df_base_line)
#
# print("Recruiter Input Done")
#
# print("Base Line Setup")
#
# print("Base Line Setup Done")

# Recruiter Input

#<---Develop_Matching_Variable--->#
df_base_line <- read_csv("develop_data_input/[Match_Making]_base_line_2024_11_22.csv")

# Edit Value
# Replace values in specific columns
# df_base_line <- df_base_line |>
#   mutate(
#     `Job Role` = ifelse(`Job Role` == "", "", `Job Role`),
#     id = ifelse(id == "", "", id),
#     minimum_salary = ifelse(minimum_salary == "", "", minimum_salary),
#     maximum_salary = ifelse(maximum_salary == "", "", maximum_salary),
#     `Job Group Role` = ifelse(`Job Group Role` == "", "", `Job Group Role`),
#     `Job Role Name` = ifelse(`Job Role Name` == "", "", `Job Role Name`),
#     `Tools and Competencies Mastery` = ifelse(`Tools and Competencies Mastery` == "", "", `Tools and Competencies Mastery`),
#     `Education Level` = ifelse(`Education Level` == "", "", `Education Level`),
#     `Previous Job Industry` = ifelse(`Previous Job Industry` == "", "", `Previous Job Industry`),
#     Domicile = ifelse(Domicile == "", "", Domicile),
#     job_level = ifelse(job_level == "", "", job_level),
#     job_type = ifelse(job_type == "", "", job_type),
#     job_vacancy_type = ifelse(job_vacancy_type == "", "", job_vacancy_type),
#     work_mode = ifelse(work_mode == "", "", work_mode),
#     min_age = ifelse(min_age == "", "", min_age),
#     qualifications = ifelse(qualifications == "", "", qualifications),
#     max_age = ifelse(max_age == "", "", max_age)
#   )

# Enhance working experience
translate_job_level_to_we <- function(job_level) {
  # Define the complete list of experience ranges
  we_levels <- list(
    "0-3 tahun" = c(0, 2.9999),
    "3-6 tahun" = c(3, 5.9999),
    "6-11 tahun" = c(6, 10.9999),
    "11-99 tahun" = c(11, 99),
    "0-99 tahun" = c(0, 99)  # Add this to handle "all_level_experience"
  )

  # Define a translation map for job levels
  translation_map <- list(
    "entry_level" = "0-3 tahun",
    "med_associate" = "3-6 tahun",
    "senior_lead" = "6-11 tahun",
    "principal" = "11-99 tahun",
    "all_level_experience" = "0-99 tahun" # Dynamically handle it here
  )

  # Unified logic to handle all cases
  if (is.na(job_level) || is.null(job_level)) {
    # Handle NA or null job_level
    return(list(
      full_list = we_levels,
      selected_range = NA,
      selected_range_values = NA
    ))
  } else if (job_level == "all_level_experience") {
    # Handle the special case of "all_level_experience"
    return(list(
      full_list = we_levels,
      selected_range = "0-99 tahun",
      selected_range_values = c(0, 99)
    ))
  } else if (job_level %in% names(translation_map)) {
    # Handle valid job levels
    selected_range <- translation_map[[job_level]]
    return(list(
      full_list = we_levels,
      selected_range = selected_range,
      selected_range_values = we_levels[[selected_range]]
    ))
  } else {
    # Handle unknown job levels
    return(list(
      full_list = we_levels,
      selected_range = NA,
      selected_range_values = NA
    ))
  }
}

# Example usage
we_result <- df_base_line$job_level  # e.g., "entry_level"

# Call the function to translate job level to experience range
we_result <- translate_job_level_to_we(we_result)

base_line_we <- we_result$selected_range

### EDU

base_line_edu <-df_base_line$`Education Level`

### GPA

base_line_gpa <- NA

### Domicile

base_line_domisili <- df_base_line$Domicile

### Salary

base_line_expected_salary <- df_base_line$minimum_salary

# Define the baseline range for expected salary
base_line_expected_salary_min <- df_base_line$minimum_salary
base_line_expected_salary_max <- df_base_line$maximum_salary


### Industry

# Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
base_line_industry <- strsplit(df_base_line$`Previous Job Industry`, ", ")

#base_line_industry <- NA

# Flatten the list to ensure it's in the expected format
base_line_industry <- unlist(base_line_industry)

# Convert it back to a list where each element is a single string
base_line_industry <- as.list(base_line_industry)

### Skills n tools

# Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
base_line_skillntools <- strsplit(df_base_line$`Tools and Competencies Mastery`, ", ")

# Flatten the list to ensure it's in the expected format
base_line_skillntools <- unlist(base_line_skillntools)

# Convert it back to a list where each element is a single string
base_line_skillntools <- as.list(base_line_skillntools)

##---##


# Setup Config
# print("Setup Config From DB")
# ## Data for Schema
# sql_schema_list_prod <- paste(readLines("queries/schema_list_production.sql"), collapse = "\n")
# result_schema_list_prod <- dbGetQuery(con_read, sql_schema_list_prod)
#
# schema_list_prod <- result_schema_list_prod$scheme
# schema <- params$schema
#
# if (!(schema %in% schema_list_prod)) {
#   # Close connection
#   DBI::dbDisconnect(con_write)
#   DBI::dbDisconnect(con_read)
#   stop("Schema not found")
# }
#
# ## Read config from db
# schema <- params$schema
# job_vacancy_id <- params$job_vacancy_id
#
# schema_sql <- paste0("SET search_path = ?schema;")
# safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
# dbExecute(con_read, safe_schema_sql)
#
# sql_filename <- 'queries/config_by_job_vacancy_id.sql'
# query <- paste(readLines(sql_filename), collapse = "\n")
# safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
# result <- dbGetQuery(con_read, safe_query)
# json_string <- result$matchmaking_config
#
# json_data <- tryCatch({
#   fromJSON(json_string)
# }, error = function(e) {
#   list()
# })
#
# #json_data
# #class(json_data)
#
#
# print(json_data)
# #cat(json_data)
#
# print("Setup Config From DB")

### Need to Handle Config manually first


#<---Develop_Matching_Variable--->#
#result <- read_csv("develop_data_input/[Match_Making]_config_by_jv_id_2024_11_22.csv")
# TMMIN Project
result <- read_csv("develop_data_input/Setup for TMMIN - Config.csv")

json_string <- result$matchmaking_config

# # Replace invalid syntax
# clean_json_string <- json_string %>%
#   str_replace_all("u'", "\"") %>%   # Replace u' with "
#   str_replace_all("'", "\"")        # Replace remaining ' with "

# Enhanced cleaning for Python-style JSON
clean_json_string <- json_string %>%
  str_replace_all("u'", "\"") %>%        # Replace u' with "
  str_replace_all("'", "\"") %>%         # Replace single quotes with double quotes
  str_replace_all("\n", "") %>%          # Remove newlines
  str_replace_all(",\\s*\\}", "}") %>%   # Remove trailing commas before closing braces
  str_replace_all(",\\s*\\]", "]")       # Remove trailing commas before closing brackets

# Parse JSON into a list
json_data <- tryCatch({
  fromJSON(clean_json_string, simplifyVector = FALSE)
}, error = function(e) {
  message("Error parsing JSON: ", e)
  list()
})
# json_data <- tryCatch({
#   fromJSON(clean_json_string)
# }, error = function(e) {
#   message("Error parsing JSON: ", e)
#   list()
# })


# TMMIN Project - GPA

# Define the function to check existence and non-zero condition
detect_and_assign <- function(data_list, var_path, baseline_var_name, input_value) {
  # Check if the variable exists in the nested list
  var_exists <- tryCatch({
    eval(parse(text = paste0("data_list$", var_path)))
    TRUE
  }, error = function(e) {
    FALSE
  })

  if (var_exists) {
    # Extract the variable's value
    var_value <- eval(parse(text = paste0("data_list$", var_path)))

    # Check if the variable contains non-zero values
    if (any(var_value != 0)) {
      # Assign input value to the baseline variable if condition is met
      assign(baseline_var_name, input_value, envir = .GlobalEnv)
      return(TRUE)
    } else {
      # Assign NA to the baseline variable if all values are zero
      assign(baseline_var_name, NA, envir = .GlobalEnv)
      return(FALSE)
    }
  }

  # If the variable doesn't exist, assign NA and return FALSE
  assign(baseline_var_name, NA, envir = .GlobalEnv)
  return(FALSE)
}

# Call the function
result <- detect_and_assign(
  json_data,
  "GPA$MrU_GPA$weight",
  "base_line_gpa",
  #3.0
  "2.9-3.2"
)

base_line_gpa


# User Input
# print("User Input")
# ## Data Candidate
# schema <- params$schema
# job_vacancy_id <- params$job_vacancy_id
# recalculate_all <- as.logical(params$recalculate_all)
#
# schema_sql <- paste0("SET search_path = ?schema;")
# safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
# dbExecute(con_read, safe_schema_sql)
# dbExecute(con_read, "SET work_mem='32MB'")
#
# sql_filename <- 'queries/candidate_data_new.sql'
# query <- paste(readLines(sql_filename), collapse = "\n")
#
# if (!recalculate_all) {
#   query <- paste0(
#     query,
#     " LEFT JOIN user_vacancy_matchmaking_results",
#     " ON candidate_datas.user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id"
#   )
# }
#
# query <- paste0(
#   query,
#   " WHERE candidate_datas.job_vacancy_id = ?job_vacancy_id"
# )
#
# if (!recalculate_all) {
#   query <- paste0(
#     query,
#     " AND user_vacancy_matchmaking_results.main_score IS NULL"
#   )
# }
# # BUG - harusnya bukan score lagi -> main_score
#
# safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
#
# result <- dbGetQuery(con_read, safe_query)
# df_candidate <- as.data.frame(result)
#View(df_candidate)
# BUG - harusnya tidak duplicate ID nya

## Data Experience
# sql_filename <- 'queries/experience_data.sql'
# query <- paste(readLines(sql_filename), collapse = "\n")
#
# if (!recalculate_all) {
#   query <- paste0(
#     query,
#     " LEFT JOIN user_vacancy_matchmaking_results",
#     " ON user_job_vacancies.id = user_vacancy_matchmaking_results.user_job_vacancy_id"
#   )
# }
#
# query <- paste0(
#   query,
#   " WHERE user_job_vacancies.discarded_at IS NULL",
#   " AND user_job_vacancies.state != 'pool'",
#   " AND experiences.work_type != 'intern'",
#   " AND user_job_vacancies.job_vacancy_id = ?job_vacancy_id"
# )
#
# if (!recalculate_all) {
#   query <- paste0(
#     query,
#     " AND user_vacancy_matchmaking_results.score IS NULL"
#   )
# }
#
# safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
#
# result <- dbGetQuery(con_read, safe_query)
# df_experience <- as.data.frame(result)

#View(df_experience)


#<---Develop_Matching_Variable--->#

df_candidate <- read_csv("develop_data_input/[Match_Making]_candidate_data_new_2024_11_22.csv")

df_experience <- read_csv("develop_data_input/[Match_Making]_experience_data_2024_11_22.csv")

# Load Data From S3

## Data Campuus
df_campus <- s3readRDS(object = "df_campus.rds", bucket = "shiny-dashboard/dashboard_ops")

## Data Domisili
df_domisili <- s3readRDS(object = "df_domisili.rds", bucket = "shiny-dashboard/dashboard_ops")

#<---New_Data--->

## Data Univ
### S3
df_univ_s3 <- s3readRDS(object = "df_univ.rds", bucket = "shiny-dashboard/dashboard_scoring")
### redash
df_univ_redash <- read_csv("https://redash.rakamin.com/api/queries/2079/results.csv?api_key=YbmT1uVOD8P06iG1GPyDIFXe447d6B7uUMdlvt2V")

## Data Role
df_role_s3 <- s3readRDS(object = "df_role.rds", bucket = "shiny-dashboard/dashboard_scoring")
### redash
df_role_redash <- read_csv("https://redash.rakamin.com/api/queries/2080/results.csv?api_key=BWaZWyg5WdArRXqY6hQyUv8LxBdixmCcM0vKyZ31")


## Data Prov
df_prov_s3 <- s3readRDS(object = "df_prov.rds", bucket = "shiny-dashboard/dashboard_scoring")
### redash
df_prov_redash <- read_csv("https://redash.rakamin.com/api/queries/2083/results.csv?api_key=DRhXzcLIHhPOn1Fy8Q0fd034sFDh5nNHQakioyxv")


## Data Major
df_major_s3 <- s3readRDS(object = "df_major.rds", bucket = "shiny-dashboard/dashboard_scoring")
### redash
df_major_redash <- read_csv("https://redash.rakamin.com/api/queries/2081/results.csv?api_key=ZYQYYzlx4RZQEucZsEa2r6GxQ8IlNIH2dvb8VJcL")


## Data Kab Kota
df_kab_kota_s3 <- s3readRDS(object = "df_kab_kota.rds", bucket = "shiny-dashboard/dashboard_scoring")
### redash
df_location_redash <- read_csv("https://redash.rakamin.com/api/queries/2082/results.csv?api_key=VokKTMG1ayc8oAsyfZqJxZkv8XmduIKrID8YSapn")


### Enhance Column DF Candidate
print("Setup Candidate")

print(df_candidate)
str(df_candidate)
print(df_experience)
str(df_experience)

print("Data Experience Processing - Start")
current_date <- format(Sys.Date(), "%Y-%m-%y") #2021-05-21
current_date_time <- paste(current_date, "00:00:00")
current_date_time <- as.Date(current_date_time)
`Major (Cluster)` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]
`Major (Specific)` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]
`Industry` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]

df_experience <- df_experience |>
  mutate(
    starts_at = as.Date(starts_at),  # Convert starts_at to Date
    start_month = as.yearmon(starts_at),  # Convert to yearmon for month-level calculations
    ends_at = as.Date(ends_at),  # Convert ends_at to Date
    ends_at = coalesce(ends_at, current_date_time),  # Replace NA values in ends_at with the current date
    end_month = as.yearmon(ends_at),  # Convert to yearmon for month-level calculations
    `YoE Month` = floor(interval(starts_at, ends_at) / months(1)),  # Calculate months of experience
    `YoE Year` = round(`YoE Month` / 12, 2),  # Convert months of experience to years
    JobRoleRelevancy = ifelse(`Job Role` != "", grepl(`Major (Cluster)`, `Job Role`), FALSE),  # Replace NA with FALSE
    JobRoleRelevancy = ifelse(is.na(JobRoleRelevancy), FALSE, JobRoleRelevancy),  # Explicitly handle NA as FALSE
    JobRoleFieldRelevancy = ifelse(industry != "", grepl(`Major (Cluster)`, industry), FALSE),  # Replace NA with FALSE
    JobRoleFieldRelevancy = ifelse(is.na(JobRoleFieldRelevancy), FALSE, JobRoleFieldRelevancy)  # Explicitly handle NA as FALSE
  ) %>%
  filter(work_type != "intern")  # Filter out internships

#View(df_experience)


data_we <- data.frame(
  PositionRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  FieldRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  MajorRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  Min_Exp = c(3, 2, 1, 0.01, 0),
  Max_Exp = c(30, 3, 2, 1, 0.01),
  Score = c(5, 4, 3, 2, 1)
)

df_experience_industry <- df_experience |>
  select(`User ID`, industry) |>
  group_by(`User ID`) |>
  summarise(industry = paste(unique(na.omit(industry)), collapse = "\", \"")) |>
  mutate(industry = paste0("{\"", industry, "\"}"))

#View(df_experience_industry)

df_experience_yoe <- df_experience |>
  select(-c(Fullname, `Job Role`, `Applied Date`)) |>
  #select(`User ID`, JobRoleRelevancy, JobRoleFieldRelevancy, `YoE Year`) |>
  group_by(`User ID`) |>  #, JobRoleRelevancy, JobRoleFieldRelevancy) |>
  summarise(`YoE Year` = sum(`YoE Year`)) |>
  ungroup() #|>
  # rowwise() |>
  # mutate(
  #   ExperienceScore = ifelse(
  #     nrow(df_experience) > 0,
  #     data_we |>
  #       filter(
  #         PositionRelevancy == JobRoleRelevancy &
  #           FieldRelevancy == JobRoleFieldRelevancy &
  #           `YoE Year` >= Min_Exp &
  #           `YoE Year` < Max_Exp
  #       ) |>
  #       pull(Score),
  #     0
  #   )
  # ) |>
  # ungroup() |>
  # replace_na(list(ExperienceScore = 0))

#View(df_experience_yoe)

df_experience_yoe <- df_experience_yoe |>
  left_join(df_experience_industry, by = "User ID")

#View(df_experience_test)

print("Data Experience Processing - Done")

df_experience <- df_experience |>
  select(-c(Fullname, `Job Role`, `Applied Date`, industry))

df_experience <- df_experience_yoe |>
  left_join(df_experience, by = c("User ID", "YoE Year"))

#View(df_experience)

candidates <- df_candidate |>
  left_join(df_experience, by = "User ID")

#View(candidates_test)


candidates <- candidates |>
  select(Fullname, Degree, Institution, `Calculated YoE`, GPA, `Kota - Kab`, expect_min, industry, `Skill & Tools`, Major, user_job_vacancy_id) |> #Add Institution
  rename(Candidate = Fullname,
         Education = Degree,
         Experience = `Calculated YoE`,
         GPA = GPA,
         Domisili = `Kota - Kab`,
         #Expected_Salary_Min = expect_min,
         #Expected_Salary_Max = expect_max,
         Expected_Salary = expect_min,
         Industry = industry,
         Skillntools = `Skill & Tools`
 ) |>
  mutate(Experience = floor(Experience))

#|>
  #select(Candidate, Education, Experience, GPA, Domisili, Expected_Salary, Industry, Skillntools, user_job_vacancy_id)
# write.csv(candidates, "candidate.csv")

#candidates_exp <- candidates
#View(candidates_exp)

#print(stop_code)

#<---Develop_Matching_Variable--->#

# print("Start Fuzzy")
#
# ## Add Fuzzy Function
#
# print("Start University")
#
# ### University
#
# # Call university actual input from users in data frame candidates in column Institution
# user_inputs_univ <- candidates |>
#   select(Candidate, Institution, user_job_vacancy_id)
#
# # Call data mapping university
# mapped_data_univ <- df_univ_redash
#
# match_and_score_univ <- function(user_inputs, mapped_data, threshold = 80) {
#   results <- user_inputs_univ %>%
#     rowwise() %>%
#     mutate(
#       match_details = list(
#         mapped_data_univ %>%
#           mutate(
#             exact_ratio = stringdist::stringdist(Institution, actual_input, method = "jw") %>%
#               {1 - .} * 100,
#             partial_ratio = stringdist::stringdist(Institution, actual_input, method = "lv") %>%
#               {1 - .} * 100,
#             token_sort_ratio = stringdist::stringdist(Institution, actual_input, method = "cosine") %>%
#               {1 - .} * 100
#           ) %>%
#           filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold) %>%
#           slice_max(order_by = exact_ratio, n = 1) # Select the best match
#       )
#     ) %>%
#     # Ensure match_details is never NULL or empty
#     mutate(
#       match_details = if (is.null(match_details) || length(match_details[[1]]) == 0) {
#         tibble(
#           actual_input = Institution, # Retain user input
#           mapped_input = "No Match Found",
#           rank_value = "Not Ranked",
#           return_value = 0,
#           exact_ratio = 0,
#           partial_ratio = 0,
#           token_sort_ratio = 0
#         )
#       } else {
#         match_details
#       }
#     ) %>%
#     unnest(match_details) %>%
#     mutate(
#       pass = exact_ratio >= threshold,
#       created_at = now(),
#       updated_at = now(),
#       input_table_name = "universities"
#     ) %>%
#     select(
#       created_at, updated_at, actual_input, mapped_value = mapped_input,
#       rank_value, return_value, exact_ratio, partial_ratio, token_sort_ratio, pass, input_table_name
#     )
#
#   return(results)
# }
#
# # Apply the function
# final_results_univ <- match_and_score_univ(user_inputs_univ, mapped_data_univ)
#
#
# print("Start Major")
#
# ### Major
#
# # Call university actual input from users in data frame candidates in column Institution
# user_inputs_major <- candidates |>
#   select(Candidate, Education, user_job_vacancy_id)
#
# # Call data mapping university
# mapped_data_major <- df_major_redash
#
# match_and_score_major <- function(user_inputs, mapped_data, threshold = 80) {
#   results <- user_inputs_major %>%
#     rowwise() %>%
#     mutate(
#       match_details = list(
#         mapped_data_major %>%
#           mutate(
#             exact_ratio = stringdist::stringdist(Institution, actual_input, method = "jw") %>%
#               {1 - .} * 100,
#             partial_ratio = stringdist::stringdist(Institution, actual_input, method = "lv") %>%
#               {1 - .} * 100,
#             token_sort_ratio = stringdist::stringdist(Institution, actual_input, method = "cosine") %>%
#               {1 - .} * 100
#           ) %>%
#           filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold) %>%
#           slice_max(order_by = exact_ratio, n = 1) # Select the best match
#       )
#     ) %>%
#     # Ensure match_details is never NULL or empty
#     mutate(
#       match_details = if (is.null(match_details) || length(match_details[[1]]) == 0) {
#         tibble(
#           actual_input = Institution, # Retain user input
#           mapped_input = "No Match Found",
#           rank_value = "Not Ranked",
#           return_value = 0,
#           exact_ratio = 0,
#           partial_ratio = 0,
#           token_sort_ratio = 0
#         )
#       } else {
#         match_details
#       }
#     ) %>%
#     unnest(match_details) %>%
#     mutate(
#       pass = exact_ratio >= threshold,
#       created_at = now(),
#       updated_at = now(),
#       input_table_name = "university_majors"
#     ) %>%
#     select(
#       created_at, updated_at, actual_input, mapped_value = mapped_input,
#       rank_value, return_value, exact_ratio, partial_ratio, token_sort_ratio, pass, input_table_name
#     )
#
#   return(results)
# }
#
# # Apply the function
# final_results_major <- match_and_score_major(user_inputs_major, mapped_data_major)
#
#
# print("Start Role")
#
# ### Role
#
# # Call university actual input from users in data frame candidates in column Institution
# user_inputs_role <- candidates |>
#   select(Candidate, Role, user_job_vacancy_id)
#
# # Call data mapping university
# mapped_data_role <- df_role_redash
#
# match_and_score_role <- function(user_inputs, mapped_data, threshold = 80) {
#   results <- user_inputs_role %>%
#     rowwise() %>%
#     mutate(
#       match_details = list(
#         mapped_data_role %>%
#           mutate(
#             exact_ratio = stringdist::stringdist(Institution, actual_input, method = "jw") %>%
#               {1 - .} * 100,
#             partial_ratio = stringdist::stringdist(Institution, actual_input, method = "lv") %>%
#               {1 - .} * 100,
#             token_sort_ratio = stringdist::stringdist(Institution, actual_input, method = "cosine") %>%
#               {1 - .} * 100
#           ) %>%
#           filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold) %>%
#           slice_max(order_by = exact_ratio, n = 1) # Select the best match
#       )
#     ) %>%
#     # Ensure match_details is never NULL or empty
#     mutate(
#       match_details = if (is.null(match_details) || length(match_details[[1]]) == 0) {
#         tibble(
#           actual_input = Institution, # Retain user input
#           mapped_input = "No Match Found",
#           rank_value = "Not Ranked",
#           return_value = 0,
#           exact_ratio = 0,
#           partial_ratio = 0,
#           token_sort_ratio = 0
#         )
#       } else {
#         match_details
#       }
#     ) %>%
#     unnest(match_details) %>%
#     mutate(
#       pass = exact_ratio >= threshold,
#       created_at = now(),
#       updated_at = now(),
#       input_table_name = "job_roles"
#     ) %>%
#     select(
#       created_at, updated_at, actual_input, mapped_value = mapped_input,
#       rank_value, return_value, exact_ratio, partial_ratio, token_sort_ratio, pass, input_table_name
#     )
#
#   return(results)
# }
#
# # Apply the function
# final_results_role <- match_and_score_role(user_inputs_role, mapped_data_role)
#
#
# print("Start Location")
#
# ### Location
#
# # Call university actual input from users in data frame candidates in column Institution
# user_inputs_location <- candidates |>
#   select(Candidate, Domisili, user_job_vacancy_id)
#
# # Call data mapping university
# mapped_datalocation <- df_location_redash
#
# match_and_score_location <- function(user_inputs, mapped_data, threshold = 80) {
#   results <- user_inputs_location %>%
#     rowwise() %>%
#     mutate(
#       match_details = list(
#         mapped_data_location %>%
#           mutate(
#             exact_ratio = stringdist::stringdist(Institution, actual_input, method = "jw") %>%
#               {1 - .} * 100,
#             partial_ratio = stringdist::stringdist(Institution, actual_input, method = "lv") %>%
#               {1 - .} * 100,
#             token_sort_ratio = stringdist::stringdist(Institution, actual_input, method = "cosine") %>%
#               {1 - .} * 100
#           ) %>%
#           filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold) %>%
#           slice_max(order_by = exact_ratio, n = 1) # Select the best match
#       )
#     ) %>%
#     # Ensure match_details is never NULL or empty
#     mutate(
#       match_details = if (is.null(match_details) || length(match_details[[1]]) == 0) {
#         tibble(
#           actual_input = Institution, # Retain user input
#           mapped_input = "No Match Found",
#           rank_value = "Not Ranked",
#           return_value = 0,
#           exact_ratio = 0,
#           partial_ratio = 0,
#           token_sort_ratio = 0
#         )
#       } else {
#         match_details
#       }
#     ) %>%
#     unnest(match_details) %>%
#     mutate(
#       pass = exact_ratio >= threshold,
#       created_at = now(),
#       updated_at = now(),
#       input_table_name = "locations"
#     ) %>%
#     select(
#       created_at, updated_at, actual_input, mapped_value = mapped_input,
#       rank_value, return_value, exact_ratio, partial_ratio, token_sort_ratio, pass, input_table_name
#     )
#
#   return(results)
# }
#
# # Apply the function
# final_results_location <- match_and_score_location(user_inputs_location, mapped_data_location)






#---Project TMMIN---#

# Function to Detect Education Match
detect_education_match <- function(df, baseline, education_column = "Education") {
  df$edu_matched <- df[[education_column]] == baseline
  return(df)
}

# Function to Detect Major Match
detect_major_match <- function(df, config, major_column = "Major", threshold = 0.2) {
  # Check if "Major" exists in the config
  if (!"Major" %in% names(config)) {
    message("Major is not configured. Skipping major matching.")
    df$major_match <- NA
    return(df)
  }

  # Get the baseline majors from the config
  baseline_majors <- tolower(config$Major$baseline)

  # Initialize match column
  df$major_match <- sapply(tolower(df[[major_column]]), function(candidate_major) {
    # Fuzzy matching with baseline majors
    distances <- stringdist(candidate_major, baseline_majors, method = "jw")
    min_distance <- min(distances)

    # Return TRUE if the closest match is within the threshold, otherwise FALSE
    return(min_distance <= threshold)
  })

  return(df)
}

# Function to Detect Education and Major Cross-Scoring
score_education_major <- function(df, config, base_line_edu, education_column = "Education", major_column = "Major") {
  # Check if Education_Major exists in the config
  if (!"Education_Major" %in% names(config)) {
    message("Education_Major is not configured. Skipping education-major scoring.")
    df$edu_major_weight <- NA
    return(df)
  }

  # Extract conditions and ensure it's a list
  edu_major_config <- config$Education_Major
  conditions <- edu_major_config$conditions
  if (!is.list(conditions)) stop("conditions must be a list of lists.")

  # Initialize weight column
  df$edu_major_weight <- 0

  # Iterate through rows and apply conditions
  for (i in 1:nrow(df)) {
    candidate_education <- df[[education_column]][i]
    candidate_major <- df[[major_column]][i]

    # Handle NA or NULL values
    if (is.na(candidate_education) || is.null(candidate_education) ||
        is.na(candidate_major) || is.null(candidate_major)) {
      df$major_match[i] <- FALSE
      next
    }

    # Check conditions
    for (condition in conditions) {
      if (candidate_education %in% condition$candidate_education) {
        if (condition$major_relevance == "any" ||
            (condition$major_relevance == "related" && df$major_match[i]) ||
            (condition$major_relevance == "not_related" && !df$major_match[i])) {
          df$edu_major_weight[i] <- condition$weight
          break
        }
      }
    }
  }
  return(df)
}


# score_education_major <- function(df, config, base_line_edu, education_column = "Education", major_column = "Major") {
#   # Check if Education_Major exists in the config
#   if (!"Education_Major" %in% names(config)) {
#     message("Education_Major is not configured. Skipping education-major scoring.")
#     df$edu_major_weight <- NA
#     return(df)
#   }
#
#   # Extract conditions and ensure it's a list
#   edu_major_config <- config$Education_Major
#   conditions <- edu_major_config$conditions
#   if (!is.list(conditions)) stop("conditions must be a list of lists.")
#
#   # Initialize weight column
#   df$edu_major_weight <- 0
#
#   # Iterate through rows and apply conditions
#   for (i in 1:nrow(df)) {
#     candidate_education <- df[[education_column]][i]
#     candidate_major <- df[[major_column]][i]
#
#     # Check conditions
#     for (condition in conditions) {
#       if (candidate_education %in% condition$candidate_education) {
#         if (condition$major_relevance == "any" ||
#             (condition$major_relevance == "related" && df$major_match[i]) ||
#             (condition$major_relevance == "not_related" && !df$major_match[i])) {
#           df$edu_major_weight[i] <- condition$weight
#           break
#         }
#       }
#     }
#   }
#   return(df)
# }


# score_education_major <- function(df, config, base_line_edu, education_column = "Education", major_column = "Major") {
#   # Check if Education_Major exists in the config
#   if (!"Education_Major" %in% names(config)) {
#     message("Education_Major is not configured. Skipping education-major scoring.")
#     df$edu_major_weight <- NA
#     return(df)
#   }
#
#   # Extract conditions
#   edu_major_config <- config$Education_Major
#   conditions <- edu_major_config$conditions
#
#   # Initialize weight column
#   df$edu_major_weight <- 0
#
#   # Iterate through rows and apply conditions
#   for (i in 1:nrow(df)) {
#     candidate_education <- df[[education_column]][i]
#     candidate_major <- df[[major_column]][i]
#
#     # Check conditions
#     for (condition in conditions) {
#       if (candidate_education %in% condition$candidate_education) {
#         if (condition$major_relevance == "any" ||
#             (condition$major_relevance == "related" && df$major_match[i]) ||
#             (condition$major_relevance == "not_related" && !df$major_match[i])) {
#           df$edu_major_weight[i] <- condition$weight
#           break
#         }
#       }
#     }
#   }
#   return(df)
# }

# Example Workflow
# Assume 'candidates' is your dataframe and 'json_data' is your config
base_line_edu <- df_base_line$`Education Level`  # Dynamically fetch baseline education
candidates <- detect_education_match(candidates, base_line_edu)
candidates <- detect_major_match(candidates, json_data)
#candidates <- score_education_major(candidates, json_data, base_line_edu)
candidates <- score_education_major(candidates, json_data, base_line_edu = NULL)

# Function to Detect Education Match
# detect_education_match <- function(df, baseline, education_column = "Education") {
#   df$edu_matched <- df[[education_column]] == baseline
#   return(df)
# }
#
# # Apply the Function
# candidates <- detect_education_match(candidates, base_line_edu)
#
#
# # Function to Detect Major Match
# detect_major_match <- function(df, config, major_column = "Major", threshold = 0.2) {
#   # Check if "Major" exists in the config
#   if (!"Major" %in% names(config)) {
#     message("Major is not configured. Skipping major matching.")
#     df$major_match <- NA
#     return(df)
#   }
#
#   # Get the baseline majors from the config
#   baseline_majors <- tolower(config$Major$baseline)
#
#   # Initialize match column
#   df$major_match <- sapply(tolower(df[[major_column]]), function(candidate_major) {
#     # Fuzzy matching with baseline majors
#     distances <- stringdist(candidate_major, baseline_majors, method = "jw")
#     min_distance <- min(distances)
#
#     # Return TRUE if the closest match is within the threshold, otherwise FALSE
#     return(min_distance <= threshold)
#   })
#
#   return(df)
# }
#
# # Apply the Function
# candidates <- detect_major_match(candidates, json_data)




str(candidates)
print("Setup Candidate Done")


## 1.3 Variable Input
print("Setup Variable Input")
### 1.3.1 Config General

#### config default

create_MrU_GPA <- function(base_line_gpa, default_weight = 3) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_gpa)) {
    MrU_GPA <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_GPA <- list(weight = default_weight)
  }

  return(MrU_GPA)
}

create_MrU_Industry <- function(base_line_industry, default_weight = json_data$Industry$MrU_Industry$weight) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_industry)) {
    MrU_Industry <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_Industry <- list(weight = default_weight)
  }

  return(MrU_Industry)
}

create_MrU_Domisil <- function(base_line_domisili, default_weight = json_data$Domisili$MrU_Domisil$weight) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_domisili)) {
    MrU_Domisil <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_Domisil <- list(weight = default_weight)
  }

  return(MrU_Domisil)
}

#<---TMMIN_Updated--->#
create_MrU_Education_Major <- function(config, default_weight = 0) {
  # Check if Education_Major exists in the config
  if (!"Education_Major" %in% names(config)) {
    message("Education_Major is not configured. Returning default weight.")
    return(default_weight)  # Return default weight if not configured
  }

  # Extract the weight for MrU_Education_Major or assign default
  mrU_config <- config$Education_Major$MrU_Education_Major
  weight <- ifelse(!is.null(mrU_config$weight), mrU_config$weight, default_weight)

  return(weight)  # Return the weight for MrU_Education_Major
}



#### a. MrU Criteria - Weight
print("Setup MrU")
# Define example inputs with weights
MrU_Education <- list(weight = json_data$Education$MrU_Education$weight)
MrU_WE <- list(weight = json_data$Working_Experience$MrU_WE$weight)
#MrU_GPA <- list(weight = 3)
MrU_GPA <- list(weight = json_data$GPA$MrU_GPA$weight)
MrU_Domisil <- create_MrU_Domisil(base_line_domisili)
MrU_ES <- list(weight = json_data$Expected_Salary$MrU_ES$weight)
MrU_Industry <- create_MrU_Industry(base_line_industry)
#MrU_Industry <- list(weight = json_data$Industry$MrU_Industry$weight)
MrU_Skillntools <- list(weight = json_data$Skillntools$MrU_Skillntools$weight)
MrU_Education_Major <- create_MrU_Education_Major(json_data)

# MrU_Education <- list(weight = 1)
# MrU_WE <- list(weight = 1)
# MrU_GPA <- list(weight = 1)
# MrU_Domisil <- list(weight = 1)
# MrU_ES <- list(weight = 1)
# MrU_Industry <- list(weight = 1)
# MrU_Skillntools <- list(weight = 1)

#CutOffValue <- as.numeric(config_cutoff$Value)
CutOffValue <- 40


#### b. MrC Sub Criteria - Norma
print("Setup MrC")
# Define levels for education
education_levels <- c("SMA/SMK", "D1", "D2", "D3", "D4", "S1", "S2", "S3")

# # Define levels for working experience as numeric ranges
# we_levels <- list("0-2 tahun" = c(0, 2),
#                   "2-5 tahun" = c(2, 5),
#                   "7-12 tahun" = c(7, 12),
#                   "10-15 tahun" = c(10, 15))

we_levels <- we_result$full_list

# Define levels for GPA as numeric ranges
gpa_levels <- list("0-2.5" = c(0, 2.5),
                   "2.5-2.7" = c(2.5, 2.7),
                   "2.7-2.9" = c(2.7, 2.9),
                   "2.9-3.2" = c(2.9, 3.2),
                   "3.2-3.5" = c(3.2, 3.5),
                   "3.5-4" = c(3.5, 4))

# Function to create dynamic values for education

# Function to create dynamic values for education
create_dynamic_values_edu <- function(levels, base_line) {
  # Check if base_line is NA
  if (is.na(base_line)) {
    # Return all 0 values if baseline is NA
    return(rep(0, length(levels)))
  }

  base_index <- match(base_line, levels)

  # Handle the case when base_line is not found
  if (is.na(base_index)) {
    stop("Base line education level does not match any education levels")
  }

  values <- seq(0, length(levels) - base_index)

  # Ensure the length of values matches the length of levels
  values <- c(rep(0, base_index - 1), values)

  return(values)
}

# Function to convert a range string into a numeric range
convert_to_numeric_range <- function(range_str) {
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Enhance all_level_experience
# Function to create dynamic values for working experience levels
create_dynamic_values_we <- function(levels, base_line) {
  print(base_line)  # For debugging

  # Handle NA or null base_line
  if (is.na(base_line) || is.null(base_line)) {
    print("Base line is NA or null. Returning all zeros.")
    return(rep(0, length(levels)))
  }

  # Handle the special case when base_line is "all_level_experience"
  if (base_line == "all_level_experience") {
    return(rep(max(seq(0, length(levels) - 1)), length(levels)))
  }

  # Convert the base_line to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Find the index where the base_line falls within the levels
  base_index <- which(sapply(names(levels), function(x) {
    exp_range <- convert_to_numeric_range(gsub(" tahun", "", x))
    return(base_range[1] >= exp_range[1] && base_range[2] <= exp_range[2])
  }))

  # Handle the case when base_index is not found
  if (length(base_index) == 0) {
    stop(paste("Base line range", base_line, "does not match any experience levels"))
  }

  # Use the first match if multiple indices are found
  base_index <- base_index[1]

  # Create a sequence of values starting from 0
  values <- seq(0, length(levels) - base_index)

  # Adjust values so that all preceding levels get a value of 0
  values <- c(rep(0, base_index - 1), values)

  # Ensure the length of values matches the length of levels
  values <- values[1:length(levels)]

  return(values)
}

# Function to convert GPA ranges (assumes ranges are in the format "low-high")
convert_to_numeric_range_gpa <- function(range_str) {
  return(as.numeric(strsplit(range_str, "-")[[1]]))
}

# Function to create dynamic values for GPA
create_dynamic_values_gpa <- function(levels, base_line) {
  # Check if the base_line is NA
  if (is.na(base_line)) {
    # If base_line is NA, return a vector of zeros matching the length of levels
    return(rep(0, length(levels)))
  }

  # Convert base_line and levels to numeric ranges
  base_range <- convert_to_numeric_range_gpa(base_line)

  # Find the base index that matches the base_line range
  base_index <- which(sapply(names(levels), function(x) {
    gpa_range <- convert_to_numeric_range_gpa(x)
    return(base_range[1] >= gpa_range[1] && base_range[2] <= gpa_range[2])
  }))

  # If no match is found for base_index, return zeros
  if (length(base_index) == 0) {
    return(rep(0, length(levels)))
  }

  # Create values from the base_index
  values <- seq(0, length(levels) - base_index)

  # Ensure the length of values matches the length of levels
  values <- c(rep(0, base_index - 1), values)

  return(values)
}

#### c. MrC Dynamic Sub Criteria

# Function to convert range strings to numeric ranges
convert_to_numeric_range <- function(range_str) {
  range_str <- as.character(range_str)  # Ensure input is character
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

print(base_line_industry)

print("check_debug_here")

print(education_levels)
print(base_line_edu)
print(we_levels)
print(base_line_we)
#print(base_line_industry)

# Create data frames for each criterion
MrC_Education <- data_frame(level = education_levels,
                            value = create_dynamic_values_edu(education_levels, base_line_edu))

MrC_WE <- data_frame(level = names(we_levels),
                     value = create_dynamic_values_we(we_levels, base_line_we))

MrC_GPA <- data_frame(level = names(gpa_levels),
                      value = create_dynamic_values_gpa(gpa_levels, base_line_gpa))

print("or here")

### 1.3.2 Final Config JSON
print("Final Config Setup")

Education <- list(MrU_Education = MrU_Education,
                  MrC_Education = MrC_Education
)

Working_Experience <- list(MrU_WE = MrU_WE,
                           MrC_WE = MrC_WE
)

GPA <- list(MrU_GPA = MrU_GPA,
            MrC_GPA = MrC_GPA
)

Domisili <- list(MrU_Domisil = MrU_Domisil)

Expected_Salary <- list(MrU_ES = MrU_ES)

Industry <- list(MrU_Industry = MrU_Industry)

Skillntools <- list(MrU_Skillntools = MrU_Skillntools)

Education_Major <- list(MrU_Education_Major = MrU_Education_Major)


matchmaking_config <- list(Education = Education,
                           Working_Experience = Working_Experience,
                           GPA = GPA,
                           Domisili = Domisili,
                           Expected_Salary = Expected_Salary,
                           Industry = Industry,
                           Skillntools = Skillntools,
                           Education_Major = Education_Major
)

print(Industry)
print(MrU_Industry)

print(matchmaking_config)
# match_making_config_json <- toJSON(matchmaking_config)
#match_making_config_json <- json_data <- toJSON(matchmaking_config, auto_unbox = TRUE, pretty = TRUE)
#cat(match_making_config_json)
#write_json(match_making_config_json, "match_making_config_json.json")

# Define not null Criteria
count_weight_data <- list(matchmaking_config$Education$MrU_Education,
                          matchmaking_config$Working_Experience$MrU_WE,
                          matchmaking_config$GPA$MrU_GPA,
                          matchmaking_config$Domisili$MrU_Domisil,
                          matchmaking_config$Expected_Salary$MrU_ES,
                          matchmaking_config$Industry$MrU_Industry,
                          matchmaking_config$Skillntools$MrU_Skillntools,
                          matchmaking_config$Education_Major$MrU_Education_Major
)

# Count the number of elements where the value is not 0
non_zero_count <- sum(sapply(count_weight_data, function(x) x != 0))

# Function to detect and count variables that contain 'MrC'
count_mrc_variables <- function(config_list) {
  count <- 0
  mrc_variables <- list()

  # Iterate over each variable in the config list
  for (variable_name in names(config_list)) {
    variable <- config_list[[variable_name]]

    # Check if any key in the variable list contains 'MrC'
    if (any(grepl("MrC", names(variable)))) {
      count <- count + 1
      mrc_variables <- c(mrc_variables, variable_name)
    }
  }

  return(list(count = count, variables_with_mrc = mrc_variables))
}

# Apply the function to your matchmaking_config
result <- count_mrc_variables(matchmaking_config)

# Display the result
count_mrc <- result$count  # This will give the count of variables containing 'MrC'
result$variables_with_mrc  # This will list the names of the variables containing 'MrC'



print("Final Config Done")

# 2. Calculation

## 2.1 MrU

### 2.1.1 Scale MrU

print("Scale MrU")
# Example input data
weight_data <- list(weight_education = Education$MrU_Education$weight,
                    weight_we = Working_Experience$MrU_WE$weight,
                    weight_gpa = GPA$MrU_GPA$weight,
                    weight_domisili = Domisili$MrU_Domisil$weight,
                    weight_es = Expected_Salary$MrU_ES$weight,
                    weight_industry = Industry$MrU_Industry$weight,
                    weight_skill = Skillntools$MrU_Skillntools$weight,
                    weight_education_major = Education_Major$MrU_Education_Major

)

print(weight_data)

# Filter out non-zero values and get their names
non_zero_values <- unlist(weight_data)[unlist(weight_data) != 0]
non_zero_names <- names(non_zero_values)

# Sum the non-zero values
sum_non_zero <- sum(non_zero_values)

# Determine the scaling factor to make the sum equal to 100
scaling_factor <- 100 / sum_non_zero

# Scale the non-zero values to make their sum 100
scaled_values <- non_zero_values * scaling_factor

# Convert scaled values back to a list with appropriate names
scaled_values_list <- setNames(as.list(scaled_values), non_zero_names)

print("Scaled value")

# Print the scaled values list
print(scaled_values_list)

print("Scale Mru Done")

### 2.1.2 Cal MrU

print("Cal MrU")
print("Test New Code")
print("Start New Function")

# BUG - Need to be can handle NA or NULL data
# Function to convert range strings to numeric ranges
convert_to_numeric_range <- function(range_str) {
  range_str <- as.character(range_str)  # Ensure input is character
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Helper function to determine if a candidate meets or exceeds the baseline for education

# ini yg paling baru
# Function to score candidate's education against the baseline

score_education <- function(candidate_education, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If baseline is NA, return score 0 and empty match/unmatch items
  if (is.na(base_line)) {
    return(list(score = 0, education = list(match_item = list(), unmatch_item = list())))
  }

  # Check if the candidate's education is NA or not in education levels
  if (is.na(candidate_education) || !(candidate_education %in% education_levels)) {
    match_items <- list(list(name = base_line, matched = FALSE))
    if (!is.na(candidate_education)) {
      unmatch_items <- list(list(name = candidate_education, matched = FALSE))
    }
    return(list(score = 0, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Remaining logic for comparing candidate's education with the baseline
  edu_index <- match(candidate_education, education_levels)
  base_index <- match(base_line, education_levels)

  if (edu_index < base_index) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_education, matched = FALSE))
    return(list(score = 0, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  } else if (edu_index == base_index) {
    match_items <- list(list(name = base_line, matched = TRUE))
    return(list(score = scaled_score, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  } else {
    match_items <- list(list(name = candidate_education, matched = TRUE))
    return(list(score = scaled_score, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  }
}


# Enhance all_level_experience
score_working_experience <- function(candidate_we, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  if (is.na(base_line)) {
    return(list(score = 0, working_experience = list(match_item = list(), unmatch_item = list())))
  }

  # Convert base line to numeric range for comparison
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Special case for "all_level_experience" - MrU only
  if (base_line == "0-99 tahun") {
    match_items <- list(list(name = "all levels", matched = TRUE))
    return(list(score = scaled_score, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Determine the candidate's level
  candidate_level <- names(we_levels)[sapply(we_levels, function(x) {
    candidate_we >= x[1] && candidate_we <= x[2]
  })]

  if (length(candidate_level) == 0) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_we, matched = FALSE))
    return(list(score = 0, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  candidate_range <- convert_to_numeric_range(gsub(" tahun", "", candidate_level))

  # Adjusted condition: Candidate experience should be >= base range to score MrC
  if (candidate_range[1] < base_range[1]) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_we, matched = FALSE))
    return(list(score = 0, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Return the score and match items
  match_items <- list(list(name = candidate_we, matched = TRUE))
  return(list(score = scaled_score, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
}


# Helper function to determine if a candidate meets or exceeds the baseline for GPA

score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {

  # Debug prints
  #print(paste("Candidate GPA:", candidate_gpa))
  #print(paste("Baseline GPA:", base_line_gpa))

  # Initialize empty lists for matches and unmatches
  match_items <- list()
  unmatch_items <- list()

  # Function to validate GPA value
  is_valid_gpa <- function(gpa) {
    if (is.null(gpa) || is.na(gpa)) {
      return(FALSE)
    }

    # For numeric (candidate) GPA
    if (is.numeric(gpa)) {
      return(gpa >= 0 && gpa <= 4.0)
    }

    # For range (baseline) GPA
    if (is.character(gpa)) {
      gpa_parts <- strsplit(gpa, "-")[[1]]
      if (length(gpa_parts) != 2) return(FALSE)

      gpa_range <- try(as.numeric(gpa_parts), silent = TRUE)
      if (inherits(gpa_range, "try-error") || length(gpa_range) != 2) {
        return(FALSE)
      }

      # Check if range values are valid GPAs
      return(all(gpa_range >= 0 & gpa_range <= 4.0))
    }
    return(FALSE)
  }

  # Parse GPA range into numeric vector
  parse_gpa_range <- function(gpa) {
    as.numeric(strsplit(gpa, "-")[[1]])
  }

  # Validate baseline GPA
  if (!is_valid_gpa(base_line_gpa)) {
    return(list(
      score = 0,
      gpa = list(
        match_item = list(),
        unmatch_item = list(list(
          name = "Invalid baseline GPA",
          matched = FALSE
        ))
      )
    ))
  }

  # Validate candidate GPA
  if (!is_valid_gpa(candidate_gpa)) {
    return(list(
      score = 0,
      gpa = list(
        match_item = list(),
        unmatch_item = list(list(
          name = "Invalid candidate GPA",
          matched = FALSE
        ))
      )
    ))
  }

  # Convert GPAs to appropriate numeric values
  base_range <- if (is.character(base_line_gpa)) parse_gpa_range(base_line_gpa) else base_line_gpa
  candidate_value <- as.numeric(candidate_gpa)

  # Evaluate if candidate meets requirements
  if (candidate_value >= base_range[1] && candidate_value <= 4.0) {
    match_items <- list(list(
      name = as.character(candidate_value),
      matched = TRUE
    ))
    score <- scaled_score
  } else {
    unmatch_items <- list(list(
      name = as.character(candidate_value),
      matched = FALSE
    ))
    score <- 0
  }

  # Return structured output
  return(list(
    score = score,
    gpa = list(
      match_item = match_items,
      unmatch_item = unmatch_items
    )
  ))
}


# score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {
#   # Initialize match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Function to validate GPA value
#   is_valid_gpa <- function(gpa) {
#     if (is.null(gpa) || is.na(gpa)) {
#       return(FALSE)
#     }
#     if (is.numeric(gpa)) {
#       return(gpa >= 0 && gpa <= 4.0)
#     }
#     numeric_gpa <- try(as.numeric(as.character(gpa)), silent = TRUE)
#     if (inherits(numeric_gpa, "try-error") || is.na(numeric_gpa)) {
#       return(FALSE)
#     }
#     return(numeric_gpa >= 0 && numeric_gpa <= 4.0)
#   }
#
#   # Check validity of baseline GPA
#   if (!is_valid_gpa(base_line_gpa)) {
#     unmatch_items <- list(list(name = "Invalid baseline GPA", matched = FALSE))
#     return(list(
#       score = 0,
#       gpa = list(match_item = match_items, unmatch_item = unmatch_items)
#     ))
#   }
#
#   # Check validity of candidate GPA
#   if (!is_valid_gpa(candidate_gpa)) {
#     unmatch_items <- list(list(name = "Invalid candidate GPA", matched = FALSE))
#     return(list(
#       score = 0,
#       gpa = list(match_item = match_items, unmatch_item = unmatch_items)
#     ))
#   }
#
#   # Convert GPAs to numeric values
#   candidate_gpa <- as.numeric(as.character(candidate_gpa))
#   base_line_gpa <- as.numeric(as.character(base_line_gpa))
#
#   # Check if the candidate GPA meets or exceeds the baseline GPA
#   if (candidate_gpa >= base_line_gpa) {
#     match_items <- list(list(name = as.character(candidate_gpa), matched = TRUE))
#     score <- scaled_score
#   } else {
#     unmatch_items <- list(list(name = as.character(candidate_gpa), matched = FALSE))
#     score <- 0
#   }
#
#   # Return the structured output
#   return(list(
#     score = score,
#     gpa = list(
#       match_item = match_items,
#       unmatch_item = unmatch_items
#     )
#   ))
# }


# score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {
#   # Initialize match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Function to validate GPA value
#   is_valid_gpa <- function(gpa) {
#     if (is.null(gpa) || is.na(gpa)) {
#       return(FALSE)
#     }
#     # Check if GPA is numeric and within valid range (0-4.0)
#     if (is.numeric(gpa)) {
#       return(gpa >= 0 && gpa <= 4.0)
#     }
#     # If GPA is character/factor, try to convert to numeric
#     numeric_gpa <- try(as.numeric(as.character(gpa)), silent = TRUE)
#     if (inherits(numeric_gpa, "try-error") || is.na(numeric_gpa)) {
#       return(FALSE)
#     }
#     return(numeric_gpa >= 0 && numeric_gpa <= 4.0)
#   }
#
#   # Check if the baseline GPA is invalid
#   if (!is_valid_gpa(base_line_gpa)) {
#     return(list(
#       score = 0,
#       matching_criteria = list(
#         match_item = list(),
#         unmatch_item = list(list(name = "Invalid baseline GPA", matched = FALSE))
#       )
#     ))
#   }
#
#   # Check if the candidate GPA is invalid
#   if (!is_valid_gpa(candidate_gpa)) {
#     return(list(
#       score = 0,
#       matching_criteria = list(
#         match_item = list(),
#         unmatch_item = list(list(name = "Invalid candidate GPA", matched = FALSE))
#       )
#     ))
#   }
#
#   # Convert GPAs to numeric values
#   candidate_gpa_num <- as.numeric(as.character(candidate_gpa))
#   base_line_gpa_num <- as.numeric(as.character(base_line_gpa))
#
#   # Compare candidate GPA with baseline GPA
#   if (candidate_gpa_num < base_line_gpa_num) {
#     # Candidate GPA is below the baseline
#     match_items <- list()
#     unmatch_items <- list(
#       list(name = as.character(candidate_gpa), matched = FALSE),
#       list(name = as.character(base_line_gpa), matched = FALSE)
#     )
#     return(list(
#       score = 0,
#       matching_criteria = list(
#         match_item = match_items,
#         unmatch_item = unmatch_items
#       )
#     ))
#   }
#
#   # Candidate GPA meets or exceeds the baseline but is within range (0-4.0)
#   match_items <- list(
#     list(name = as.character(candidate_gpa), matched = TRUE)
#   )
#   unmatch_items <- list()  # No unmatched items since candidate meets requirements
#
#   # Calculate the score
#   score <- scaled_score
#
#   # Return the complete result structure
#   return(list(
#     score = score,
#     matching_criteria = list(
#       match_item = match_items,
#       unmatch_item = unmatch_items
#     )
#   ))
# }


# score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {
#   # Initialize match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Function to validate GPA value with enhanced validation
#   is_valid_gpa <- function(gpa) {
#     if (is.null(gpa) || is.na(gpa)) {
#       return(FALSE)
#     }
#     # Check if GPA is numeric and within valid range (0-4.0)
#     if (is.numeric(gpa)) {
#       return(gpa >= 0 && gpa <= 4.0)
#     }
#     # If GPA is character/factor, try to convert to numeric
#     numeric_gpa <- try(as.numeric(as.character(gpa)), silent = TRUE)
#     if (inherits(numeric_gpa, "try-error") || is.na(numeric_gpa)) {
#       return(FALSE)
#     }
#     return(numeric_gpa >= 0 && numeric_gpa <= 4.0)
#   }
#
#   # Check if the baseline GPA is NA or invalid
#   if (!is_valid_gpa(base_line_gpa)) {
#     return(list(
#       score = 0,
#       gpa = list(
#         match_item = list(list(name = "Invalid baseline GPA", matched = FALSE)),
#         unmatch_item = unmatch_items
#       )
#     ))
#   }
#
#   # Check if the candidate GPA is valid
#   if (!is_valid_gpa(candidate_gpa)) {
#     return(list(
#       score = 0,
#       gpa = list(
#         match_item = list(),
#         unmatch_item = list(list(name = "Invalid candidate GPA", matched = FALSE))
#       )
#     ))
#   }
#
#   # Convert GPAs to numeric values
#   candidate_gpa_num <- as.numeric(as.character(candidate_gpa))
#   base_line_gpa_num <- as.numeric(as.character(base_line_gpa))
#
#   # Determine the candidate's GPA level
#   candidate_level <- names(gpa_levels)[sapply(gpa_levels, function(x) {
#     candidate_gpa_num >= x[1] && candidate_gpa_num <= x[2]
#   })]
#
#   # Handle case where no matching GPA level is found
#   if (length(candidate_level) == 0) {
#     return(list(
#       score = 0,
#       gpa = list(
#         match_item = list(),
#         unmatch_item = list(list(name = as.character(candidate_gpa), matched = FALSE))
#       )
#     ))
#   }
#
#   # Convert to numeric ranges
#   base_range <- convert_to_numeric_range(base_line_gpa_num)
#   candidate_range <- convert_to_numeric_range(candidate_gpa_num)
#
#   # Validate ranges
#   if (length(base_range) != 2 || length(candidate_range) != 2) {
#     return(list(
#       score = 0,
#       gpa = list(
#         match_item = list(),
#         unmatch_item = list(list(name = "Invalid GPA range", matched = FALSE))
#       )
#     ))
#   }
#
#   # Check if candidate GPA meets or exceeds the baseline requirements
#   match <- candidate_range[1] >= base_range[1]
#
#   # Prepare match and unmatch items with detailed information
#   if (match) {
#     match_items <- list(list(name = as.character(candidate_gpa), matched = TRUE))
#     unmatch_items <- list()
#   } else {
#     match_items <- list()
#     unmatch_items <- list(
#       list(name = as.character(candidate_gpa), matched = FALSE),
#       list(name = as.character(base_line_gpa), matched = FALSE)
#     )
#   }
#
#   # Calculate final score based on matching criteria
#   score <- if (match) scaled_score else 0
#
#   # Return the complete result structure
#   return(list(
#     score = score,
#     gpa = list(
#       match_item = match_items,
#       unmatch_item = unmatch_items
#     )
#   ))
# }

# score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {
#   # Initialize match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Function to validate GPA value
#   is_valid_gpa <- function(gpa) {
#     if (is.null(gpa) || is.na(gpa)) {
#       return(FALSE)
#     }
#     # Check if GPA is numeric and within valid range (0-4.0)
#     if (is.numeric(gpa)) {
#       return(gpa >= 0 && gpa <= 4.0)
#     }
#     # If GPA is character/factor, try to convert to numeric
#     numeric_gpa <- try(as.numeric(as.character(gpa)), silent = TRUE)
#     if (inherits(numeric_gpa, "try-error") || is.na(numeric_gpa)) {
#       return(FALSE)
#     }
#     return(numeric_gpa >= 0 && numeric_gpa <= 4.0)
#   }
#
#   # Check if the baseline GPA is valid
#   if (!is_valid_gpa(base_line_gpa)) {
#     return(list(
#       score = 0,
#       match_item = list(list(name = "Invalid baseline GPA", matched = FALSE)),
#       unmatch_item = unmatch_items
#     ))
#   }
#
#   # Check if the candidate GPA is valid
#   if (!is_valid_gpa(candidate_gpa)) {
#     return(list(
#       score = 0,
#       match_item = list(),
#       unmatch_item = list(list(name = "Invalid candidate GPA", matched = FALSE))
#     ))
#   }
#
#   # Convert GPAs to numeric values
#   candidate_gpa_num <- as.numeric(as.character(candidate_gpa))
#   base_line_gpa_num <- as.numeric(as.character(base_line_gpa))
#
#   # Convert to numeric ranges
#   base_range <- convert_to_numeric_range(base_line_gpa_num)
#   candidate_range <- convert_to_numeric_range(candidate_gpa_num)
#
#   # Validate ranges
#   if (length(base_range) != 2 || length(candidate_range) != 2) {
#     return(list(
#       score = 0,
#       match_item = list(),
#       unmatch_item = list(list(name = "Invalid GPA range", matched = FALSE))
#     ))
#   }
#
#   # Check if candidate GPA meets or exceeds the baseline range
#   match <- candidate_range[1] >= base_range[1] && candidate_range[2] <= base_range[2]
#
#   # Prepare match and unmatch items
#   if (match) {
#     match_items <- list(list(name = as.character(candidate_gpa), matched = TRUE))
#     unmatch_items <- list()
#   } else {
#     match_items <- list()
#     unmatch_items <- list(
#       list(name = as.character(candidate_gpa), matched = FALSE),
#       list(name = as.character(base_line_gpa), matched = FALSE)
#     )
#   }
#
#   # Calculate score based on matching criteria
#   score <- if (match) scaled_score else 0
#
#   return(list(
#     score = score,
#     match_item = match_items,
#     unmatch_item = unmatch_items
#   ))
# }




# score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {
#   # Initialize match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Check if the baseline GPA is NA
#   if (is.na(base_line_gpa)) {
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Convert baseline GPA to numeric range
#   base_range <- convert_to_numeric_range(base_line_gpa)
#
#   # Check if the candidate GPA is missing
#   if (is.na(candidate_gpa)) {
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Determine the candidate's GPA level
#   candidate_level <- names(gpa_levels)[sapply(gpa_levels, function(x) {
#     candidate_gpa >= x[1] && candidate_gpa <= x[2]
#   })]
#
#   if (length(candidate_level) == 0) {
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     unmatch_items <- list(list(name = candidate_gpa, matched = FALSE))
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Convert the candidate's level to a numeric range
#   candidate_range <- convert_to_numeric_range(candidate_level)
#
#   # Check if candidate GPA meets or exceeds the baseline range
#   if (candidate_range[1] < base_range[1]) {
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     unmatch_items <- list(list(name = candidate_gpa, matched = FALSE))
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Calculate score based on the range overlap
#   match_items <- list(list(name = candidate_gpa, matched = TRUE))
#   return(list(score = scaled_score, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
# }


# score_gpa <- function(candidate_gpa, base_line_gpa, weight) {
#   # Initialize empty lists for match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Check if the baseline GPA is NA and return empty lists if it is
#   if (is.na(base_line_gpa)) {
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Check if the candidate GPA is missing
#   if (is.na(candidate_gpa)) {
#     # If candidate GPA is missing, baseline is in match_item as unmatched
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
#
#   # Convert candidate GPA to a numeric value
#   candidate_range <- as.numeric(candidate_gpa)
#   base_range <- convert_to_numeric_range(base_line_gpa)
#
#   # Check if candidate GPA is below the baseline
#   if (candidate_range < base_range[1]) {
#     # Candidate GPA is in unmatch_item, and baseline GPA is in match_item as unmatched
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     unmatch_items <- list(list(name = candidate_gpa, matched = FALSE))
#     return(list(score = 0, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   } else {
#     # If candidate GPA meets or exceeds baseline, baseline is in match_item as unmatched
#     match_items <- list(list(name = base_line_gpa, matched = FALSE))
#     unmatch_items <- list(list(name = candidate_gpa, matched = TRUE))
#     score <- if (candidate_range > base_range[2]) weight else weight * (candidate_range - base_range[1]) / (base_range[2] - base_range[1])
#     return(list(score = score, gpa = list(match_item = match_items, unmatch_item = unmatch_items)))
#   }
# }



# 21 October Add Code
score_domisili <- function(candidate_domisili, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  if (is.na(base_line)) {
    # If baseline is NA and candidate has input, the result is 0
    match_items <- list(list(name = base_line, matched = FALSE))
    if (!is.na(candidate_domisili)) {
      unmatch_items <- list(list(name = candidate_domisili, matched = FALSE))
    }
  } else if (is.na(candidate_domisili)) {
    # If candidate data is missing, baseline is unmatched
    match_items <- list(list(name = base_line, matched = FALSE))
  } else if (candidate_domisili == base_line) {
    # If candidate matches baseline, it is matched
    match_items <- list(list(name = base_line, matched = TRUE))
  } else {
    # If candidate does not match baseline
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_domisili, matched = FALSE))
  }

  return(list(
    score = ifelse(!is.na(base_line) & candidate_domisili == base_line, scaled_score, 0),
    domicile = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}

# # Helper function to score Expected Salary

score_expected_salary <- function(candidate_salary, base_line_min, base_line_max, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If the candidate salary is missing, baseline is unmatched
  if (is.na(candidate_salary)) {
    match_items <- list(list(name = paste0(formatC(base_line_min, format = "f", big.mark = "", digits = 0), " - ",
                                           formatC(base_line_max, format = "f", big.mark = "", digits = 0)),
                             matched = FALSE))
    return(list(score = 0, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  candidate_salary <- as.numeric(candidate_salary)

  # If the candidate salary is greater than the maximum of the baseline
  if (candidate_salary > base_line_max) {
    # No score, add the candidate salary to unmatch_item, and baseline range to match_item
    match_items <- list(list(name = paste0(formatC(base_line_min, format = "f", big.mark = "", digits = 0), " - ",
                                           formatC(base_line_max, format = "f", big.mark = "", digits = 0)),
                             matched = FALSE))
    unmatch_items <- list(list(name = candidate_salary, matched = FALSE))
    return(list(score = 0, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # If the candidate salary is less than or equal to the baseline maximum, give score
  if (candidate_salary <= base_line_max) {
    # Score based on the candidate salary being within the range
    match_items <- list(list(name = candidate_salary, matched = TRUE))
    return(list(score = scaled_score, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }
}



# Helper function to score industry

score_industry <- function(candidate_industries_str, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If baseline is NA, return score 0 and empty match/unmatch items
  if (is.na(base_line)) {
    return(list(score = 0, industry = list(match_item = list(), unmatch_item = list())))
  }

  # If candidate industries are missing, return baseline as unmatched
  if (is.null(candidate_industries_str) || is.na(candidate_industries_str) ||
      candidate_industries_str == "{\"\"}" || trimws(candidate_industries_str) == "") {
    match_items <- lapply(base_line, function(x) list(name = x, matched = FALSE))
    return(list(score = 0, industry = list(match_item = match_items, unmatch_item = list())))
  }

  # Remaining logic for industry comparison
  candidate_industries <- gsub("[{}\"]", "", candidate_industries_str)
  candidate_industries <- strsplit(candidate_industries, ",\\s*")[[1]]

  candidate_industries_lower <- tolower(candidate_industries)
  base_line_lower <- tolower(base_line)

  matches_lower <- intersect(candidate_industries_lower, base_line_lower)

  match_items <- lapply(matches_lower, function(match) {
    original_base_name <- base_line[which(tolower(base_line) == match)[1]]
    list(name = original_base_name, matched = TRUE)
  })

  unmatched_baseline_lower <- setdiff(base_line_lower, matches_lower)
  match_items <- c(match_items, lapply(unmatched_baseline_lower, function(item) {
    original_name <- base_line[which(tolower(base_line) == item)[1]]
    list(name = original_name, matched = FALSE)
  }))

  unmatched_candidate_lower <- setdiff(candidate_industries_lower, matches_lower)
  unmatch_items <- lapply(unmatched_candidate_lower, function(item) {
    original_name <- candidate_industries[which(tolower(candidate_industries) == item)[1]]
    list(name = original_name, matched = FALSE)
  })

  score <- if (length(matches_lower) > 0) scaled_score else 0

  return(list(
    score = score,
    industry = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}

score_skillntools <- function(candidate_skills, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # Store original versions of candidate and baseline skills
  original_candidate_skills <- unlist(strsplit(gsub("[{}\"]", "", candidate_skills), ",\\s*"))
  original_base_line <- base_line

  if (is.na(candidate_skills)) {
    match_items <- lapply(original_base_line, function(skill) list(name = skill, matched = FALSE))
    return(list(score = 0, skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Convert to lowercase for matching purposes
  candidate_skills <- tolower(unlist(strsplit(gsub("[{}\"]", "", candidate_skills), ",\\s*")))
  base_line <- tolower(unlist(base_line))

  # Check if all baseline skills are present in the candidate's skills
  all_matched <- all(base_line %in% candidate_skills)

  # All baseline skills go to match_items (with original casing)
  match_items <- lapply(seq_along(base_line), function(i) {
    skill <- base_line[i]
    original_name <- original_base_line[i]
    list(name = original_name, matched = skill %in% candidate_skills)
  })

  # Non-matching candidate skills go to unmatch_items (with original casing)
  non_matches_candidate <- setdiff(candidate_skills, base_line)
  unmatch_items <- lapply(non_matches_candidate, function(skill) {
    original_name <- original_candidate_skills[which(tolower(original_candidate_skills) == skill)[1]]
    list(name = original_name, matched = FALSE)
  })

  # If not all baseline skills are matched, return score of 0
  if (!all_matched) {
    return(list(
      score = 0,
      skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)
    ))
  }

  # Score based on the scaled score if all baseline skills are matched
  score <- scaled_score

  return(list(
    score = score,
    skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}

# Helper function to create matching_criteria for all 7 criteria
create_matching_criteria <- function(skill_match, skill_unmatch, domicile_match, domicile_unmatch,
                                     education_match, education_unmatch, experience_match, experience_unmatch,
                                     gpa_match, gpa_unmatch, salary_match, salary_unmatch, industry_match, industry_unmatch) {

  # Replace NULL or empty inputs with empty lists to avoid errors
  skill_match <- if (is.null(skill_match)) list() else skill_match
  skill_unmatch <- if (is.null(skill_unmatch)) list() else skill_unmatch
  domicile_match <- if (is.null(domicile_match)) list() else domicile_match
  domicile_unmatch <- if (is.null(domicile_unmatch)) list() else domicile_unmatch
  education_match <- if (is.null(education_match)) list() else education_match
  education_unmatch <- if (is.null(education_unmatch)) list() else education_unmatch
  experience_match <- if (is.null(experience_match)) list() else experience_match
  experience_unmatch <- if (is.null(experience_unmatch)) list() else experience_unmatch
  gpa_match <- if (is.null(gpa_match)) list() else gpa_match
  gpa_unmatch <- if (is.null(gpa_unmatch)) list() else gpa_unmatch
  salary_match <- if (is.null(salary_match)) list() else salary_match
  salary_unmatch <- if (is.null(salary_unmatch)) list() else salary_unmatch
  industry_match <- if (is.null(industry_match)) list() else industry_match
  industry_unmatch <- if (is.null(industry_unmatch)) list() else industry_unmatch

  # Create the nested list structure for matching_criteria
  matching_criteria <- list(
    skill_and_tools = list(
      match_item = lapply(skill_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(skill_unmatch, function(item) list(name = item))
    ),
    domicile = list(
      match_item = lapply(domicile_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(domicile_unmatch, function(item) list(name = item))
    ),
    education = list(
      match_item = lapply(education_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(education_unmatch, function(item) list(name = item))
    ),
    working_experience = list(
      match_item = lapply(experience_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(experience_unmatch, function(item) list(name = item))
    ),
    GPA = list(
      match_item = lapply(gpa_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(gpa_unmatch, function(item) list(name = item))
    ),
    expected_salary = list(
      match_item = lapply(salary_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(salary_unmatch, function(item) list(name = item))
    ),
    industry = list(
      match_item = lapply(industry_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(industry_unmatch, function(item) list(name = item))
    )
  )

  # Convert the list to JSON format
  return(toJSON(matching_criteria, auto_unbox = TRUE))
}

print("Done New Function")

print("Start New Calculation")

candidates <- if (nrow(candidates) > 0) {
  candidates %>%
    mutate(
      MrU_Score_Edu = as.numeric(unlist(mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Education))),
      MrU_Score_WE = as.numeric(unlist(mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Experience))),
      MrU_Score_GPA = as.numeric(unlist(mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, GPA))),
      MrU_Score_Domisili = as.numeric(unlist(mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Domisili))),
      MrU_Score_ES = as.numeric(unlist(mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Expected_Salary))),
      MrU_Score_Industry = as.numeric(unlist(mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Industry))),
      MrU_Score_Skillntools = as.numeric(unlist(mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Skillntools))),
      Match_Item_Edu = mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        return(result$education$match_item)
      }, Education, SIMPLIFY = FALSE),
      Unmatch_Item_Edu = mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        return(result$education$unmatch_item)
      }, Education, SIMPLIFY = FALSE),
      Match_Item_WE = mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        return(result$working_experience$match_item)
      }, Experience, SIMPLIFY = FALSE),
      Unmatch_Item_WE = mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        return(result$working_experience$unmatch_item)
      }, Experience, SIMPLIFY = FALSE),
      Match_Item_GPA = mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        return(result$gpa$match_item)
      }, GPA, SIMPLIFY = FALSE),
      Unmatch_Item_GPA = mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        return(result$gpa$unmatch_item)
      }, GPA, SIMPLIFY = FALSE),
      Match_Item_Domisili = mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        return(result$domicile$match_item)
      }, Domisili, SIMPLIFY = FALSE),
      Unmatch_Item_Domisili = mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        return(result$domicile$unmatch_item)
      }, Domisili, SIMPLIFY = FALSE),
      Match_Item_ES = mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        return(result$expected_salary$match_item)
      }, Expected_Salary, SIMPLIFY = FALSE),
      Unmatch_Item_ES = mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        return(result$expected_salary$unmatch_item)
      }, Expected_Salary, SIMPLIFY = FALSE),
      Match_Item_Industry = mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        return(result$industry$match_item)
      }, Industry, SIMPLIFY = FALSE),
      Unmatch_Item_Industry = mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        return(result$industry$unmatch_item)
      }, Industry, SIMPLIFY = FALSE),
      Match_Item_Skillntools = mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        return(result$skill_and_tools$match_item)
      }, Skillntools, SIMPLIFY = FALSE),
      Unmatch_Item_Skillntools = mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        return(result$skill_and_tools$unmatch_item)
      }, Skillntools, SIMPLIFY = FALSE),

      # Check if 'Education_Major' exists and apply weight
      #MrU_Score_Edu = ifelse(!is.null(edu_major_weight), MrU_Score_Edu * edu_major_weight, MrU_Score_Edu),
      # Apply edu_major_weight row by row
      MrU_Score_Edu = ifelse(!is.na(edu_major_weight), MrU_Score_Edu * edu_major_weight, MrU_Score_Edu),

      Total_Score = (MrU_Score_Edu + MrU_Score_WE + MrU_Score_GPA + MrU_Score_Domisili + MrU_Score_ES + MrU_Score_Industry + MrU_Score_Skillntools),
      MrU_Score_Edu = floor(MrU_Score_Edu),
      MrU_Score_WE = floor(MrU_Score_WE),
      MrU_Score_GPA = floor(MrU_Score_GPA),
      MrU_Score_Domisili = floor(MrU_Score_Domisili),
      MrU_Score_ES = floor(MrU_Score_ES),
      MrU_Score_Industry = floor(MrU_Score_Industry),
      MrU_Score_Skillntools = floor(MrU_Score_Skillntools),
      Total_Score = floor(Total_Score)
    )
} else {
  candidates  # Return the empty dataframe as is
}

print("Test New Create List Matching Criteria")

create_matching_criteria <- function(skill_match, skill_unmatch, domicile_match, domicile_unmatch,
                                     education_match, education_unmatch, experience_match, experience_unmatch,
                                     gpa_match, gpa_unmatch, salary_match, salary_unmatch, industry_match, industry_unmatch) {
  # Helper function to wrap items in a list (even if it's a single item)
  format_item <- function(item, include_matched = TRUE) {
    if (is.null(item) || length(item) == 0) {
      return(list())  # Return an empty list for NULL or empty items
    } else if (is.list(item) && length(item) > 0 && is.list(item[[1]])) {
      # For nested lists (like skill_and_tools) - handle multiple items
      return(item)
    } else if (is.list(item) && !is.null(item$name)) {
      # For single-item lists with a 'name' field (like gpa and working_experience)
      # Ensure it's wrapped in a list even for single items
      return(list(item))
    } else {
      # For simple values (numbers, strings), also ensure wrapping in a list
      return(list(list(name = item, matched = include_matched)))
    }
  }

  # Build the matching criteria using the helper function
  matching_criteria <- list(
    expected_salary = list(
      match_item = format_item(salary_match),
      unmatch_item = format_item(salary_unmatch, FALSE)
    ),
    industry = list(
      match_item = format_item(industry_match),
      unmatch_item = format_item(industry_unmatch, FALSE)
    ),
    domicile = list(
      match_item = if(length(domicile_match) > 0) domicile_match else list(),
      unmatch_item = if(length(domicile_unmatch) > 0) domicile_unmatch else list()
    ),
    gpa = list(
      match_item = format_item(gpa_match),
      unmatch_item = format_item(gpa_unmatch, FALSE)
    ),
    skill_and_tools = list(
      match_item = format_item(skill_match),
      unmatch_item = format_item(skill_unmatch, FALSE)
    ),
    working_experience = list(
      match_item = format_item(experience_match),
      unmatch_item = format_item(experience_unmatch, FALSE)
    ),
    education = list(
      match_item = format_item(education_match),
      unmatch_item = format_item(education_unmatch, FALSE)
    )
  )
  return(matching_criteria)
}

candidates$matching_criteria <- mapply(
  create_matching_criteria,
  candidates$Match_Item_Skillntools, candidates$Unmatch_Item_Skillntools,
  candidates$Match_Item_Domisili, candidates$Unmatch_Item_Domisili,
  candidates$Match_Item_Edu, candidates$Unmatch_Item_Edu,
  candidates$Match_Item_WE, candidates$Unmatch_Item_WE,
  candidates$Match_Item_GPA, candidates$Unmatch_Item_GPA,
  candidates$Match_Item_ES, candidates$Unmatch_Item_ES,
  candidates$Match_Item_Industry, candidates$Unmatch_Item_Industry,
  SIMPLIFY = FALSE
)

#View(candidates$matching_criteria)

candidates_mru <- candidates

#View(candidates_mru)

print("Done New Calculation")

print("Test New Code Done")

print(candidates)
#View(candidates)

print("Cal MrU Done")

## 2.2. MrC

### 2.2.1 Scale MrC
print("Scale MrC")
# Function to scale non-zero MrC values
scale_mrc_values <- function(mrc_data) {
  # Filter out non-zero values and get their names
  non_zero_values_mrc <- mrc_data$value[mrc_data$value != 0]

  # Sum the non-zero values
  sum_non_zero <- sum(non_zero_values_mrc)

  # Determine the scaling factor to make the sum equal to 100
  scaling_factor <- 100 / sum_non_zero

  # Scale the non-zero values to make their sum 100
  scaled_values <- non_zero_values_mrc * scaling_factor

  # Update the MrC data with scaled values
  mrc_data$value[mrc_data$value != 0] <- scaled_values

  return(mrc_data)
}


# Apply scaling to each MrC criterion
scaled_MrC_Education <- scale_mrc_values(matchmaking_config$Education$MrC_Education)
#scaled_MrC_WE <- scale_mrc_values(matchmaking_config$Working_Experience$MrC_WE)
scaled_MrC_WE <- scale_mrc_values(matchmaking_config$Working_Experience$MrC_WE[-nrow(matchmaking_config$Working_Experience$MrC_WE), ])
scaled_MrC_GPA <- scale_mrc_values(matchmaking_config$GPA$MrC_GPA)

# Update the matchmaking_config with scaled MrC values
matchmaking_config$Education$MrC_Education <- scaled_MrC_Education
matchmaking_config$Working_Experience$MrC_WE <- scaled_MrC_WE
matchmaking_config$GPA$MrC_GPA <- scaled_MrC_GPA

max_MrC_Edu <- matchmaking_config$Education$MrC_Education$value |>
  tail(1)

max_MrC_WE <- matchmaking_config$Working_Experience$MrC_WE$value |>
  tail(1)

max_MrC_GPA <- matchmaking_config$GPA$MrC_GPA$value |>
  tail(1)

total_max_MrC <- max_MrC_Edu + max_MrC_WE + max_MrC_GPA

scale_value_MrC <- 100/total_max_MrC

matchmaking_config$Education$MrC_Education <- matchmaking_config$Education$MrC_Education |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

matchmaking_config$Working_Experience$MrC_WE <- matchmaking_config$Working_Experience$MrC_WE |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

matchmaking_config$GPA$MrC_GPA <- matchmaking_config$GPA$MrC_GPA |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

print(matchmaking_config$Working_Experience$MrC_WE)
#print(a)

print("Scale MrC Done")

print("Start MrC New Code")

# Helper function for MrC Education scoring

score_mrc_education <- function(candidate_education, base_line, scaled_values) {
  # Check if candidate education or baseline is NA
  if (is.na(candidate_education) || is.na(base_line)) {
    return(list(
      score = 0,
      match_item = list(),
      unmatch_item = list(),
      additional_value = NULL
    ))
  }

  edu_index <- match(candidate_education, scaled_values$level)
  base_index <- match(base_line, scaled_values$level)

  # Check if either candidate or baseline index is NA
  if (is.na(edu_index) || is.na(base_index)) {
    return(list(
      score = 0,
      match_item = list(),
      unmatch_item = list(),
      additional_value = NULL
    ))
  }

  # If candidate education is higher than the baseline
  if (edu_index > base_index) {
    score <- scaled_values$new_value[edu_index]
    return(list(
      score = score,
      match_item = list(list(name = candidate_education, matched = TRUE)),
      unmatch_item = list(),
      additional_value = candidate_education
    ))
  }

  # If candidate education is less than or equal to the baseline
  return(list(
    score = 0,
    match_item = list(),
    unmatch_item = list(),
    additional_value = NULL
  ))
}

# Helper function for MrC Work Experience scoring


we_levels <- head(we_levels, -1)
print(we_levels)

# Enhance all_level_experience
score_mrc_work_experience <- function(candidate_we, base_line, scaled_values) {
  if (is.na(base_line)) {
    return(list(score = 0, match_item = list(), unmatch_item = list(), additional_value = NULL))
  }

  # Handle "all_level_experience": no MrC, only MrU
  if (base_line == "0-99 tahun") {
    return(list(score = 0, match_item = list(), unmatch_item = list(), additional_value = NULL))
  }

    # Convert the baseline string to a numeric range
    base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

    # Initialize match_item and unmatch_item as empty lists
    match_items <- list()
    unmatch_items <- list()

  # # Convert candidate_we to numeric for comparison
  # candidate_we <- as.numeric(gsub(" tahun", "", candidate_we))

  # Determine the candidate's level
  candidate_level <- names(we_levels)[which(sapply(we_levels, function(x) {
    candidate_we >= x[1] && candidate_we <= x[2]
  }))]

  # Find the corresponding scaled value for MrC
  candidate_scaled_value <- scaled_values$new_value[scaled_values$level == candidate_level]
  if (length(candidate_scaled_value) == 0) {
    candidate_scaled_value <- 0  # Default to 0 if no matching level found
  }

  # Convert baseline to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # If the candidate experience meets or exceeds baseline, return MrC score
    if (candidate_we > base_range[2]) {
      # Candidate exceeds the maximum baseline
      match_items <- list(list(name = paste(candidate_we, "tahun")))
      score <- candidate_scaled_value
      additional_value <- candidate_we
    } else if (candidate_we < base_range[1]) {
      # Candidate is below the minimum baseline
      unmatch_items <- list(list(name = paste(candidate_we, "tahun")))
      score <- 0
      additional_value <- NULL
    } else {
      # Candidate is within the baseline range
      score <- 0
      additional_value <- NULL
    }

    return(list(
      score = score,
      match_item = match_items,
      unmatch_item = unmatch_items,
      additional_value = additional_value
    ))
}

# Helper function for MrC GPA scoring
score_mrc_gpa <- function(candidate_gpa, base_line, scaled_values) {
  # Initialize empty lists for match and unmatch items
  match_items <- list()
  unmatch_items <- list()

  # Check if the baseline GPA is NA or if the candidate GPA is missing
  if (is.na(base_line) || is.na(candidate_gpa)) {
    return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
  }

  # Convert baseline to numeric range
  base_range <- convert_to_numeric_range(base_line)

  # Determine the candidate's level
  candidate_level <- names(gpa_levels)[which(sapply(gpa_levels, function(x) {
    candidate_gpa >= x[1] && candidate_gpa <= x[2]
  }))]

  # Handle cases where no matching GPA level is found
  if (length(candidate_level) != 1) {
    unmatch_items <- list(list(name = paste(candidate_gpa)))
    return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
  }

  # Find the corresponding scaled value for the candidate's GPA level
  candidate_scaled_value <- scaled_values$new_value[scaled_values$level == candidate_level]
  if (length(candidate_scaled_value) == 0) {
    candidate_scaled_value <- 0  # Default to 0 if no matching level found
  }

  # Determine if the candidate's GPA exceeds, is below, or within the baseline range
  if (candidate_gpa > base_range[2]) {
    # Candidate GPA exceeds the maximum baseline
    match_items <- list(list(name = paste(candidate_gpa)))
    score <- candidate_scaled_value
    additional_value <- candidate_gpa
  } else if (candidate_gpa < base_range[1]) {
    # Candidate GPA is below the minimum baseline
    unmatch_items <- list(list(name = paste(candidate_gpa)))
    score <- 0
    additional_value <- NULL
  } else {
    # Candidate GPA is within the baseline range
    match_items <- list(list(name = paste(candidate_gpa)))
    score <- 0
    additional_value <- NULL
  }

  # Return the results
  return(list(
    score = score,
    match_item = match_items,
    unmatch_item = unmatch_items,
    additional_value = additional_value
  ))
}


# score_mrc_gpa <- function(candidate_gpa, base_line, scaled_values) {
#   # Initialize empty lists for match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Check if the baseline GPA is NA or if the candidate GPA is missing
#   if (is.na(base_line) || is.na(candidate_gpa)) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Convert baseline to numeric range
#   base_range <- convert_to_numeric_range(base_line)
#
#   # Determine the candidate's level
#   gpa_level <- names(gpa_levels)[which(sapply(gpa_levels, function(x) {
#     candidate_gpa >= x[1] && candidate_gpa <= x[2]
#   }))]
#
#   # Handle cases with no matching GPA level
#   if (length(gpa_level) != 1) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Find the corresponding scaled value for the candidate's GPA level
#   gpa_info <- scaled_values %>% filter(level == gpa_level)
#
#   # Handle cases with no matching scaled value
#   if (nrow(gpa_info) != 1) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Calculate the score using the scaled value
#   score <- gpa_info$new_value[1]
#
#   # Determine if the candidate's GPA is above, below, or within the baseline range
#   if (candidate_gpa > base_range[2]) {
#     match_items <- list(list(name = paste(candidate_gpa)))
#     additional_value <- candidate_gpa
#   } else if (candidate_gpa < base_range[1]) {
#     unmatch_items <- list(list(name = paste(candidate_gpa)))
#     score <- 0
#     additional_value <- NULL
#   } else {
#     additional_value <- NULL
#   }
#
#   return(list(
#     score = score,
#     match_item = match_items,
#     unmatch_item = unmatch_items,
#     additional_value = additional_value
#   ))
# }


# score_mrc_gpa <- function(candidate_gpa, base_line, scaled_values) {
#   # Initialize empty lists for match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Check if the baseline GPA is NA or if the candidate GPA is missing
#   if (is.na(base_line) || is.na(candidate_gpa)) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items))
#   }
#
#   # Convert baseline to numeric range
#   base_range <- convert_to_numeric_range(base_line)
#
#   # Convert candidate GPA to a numeric value and find its corresponding GPA level
#   candidate_range <- as.numeric(candidate_gpa)
#   gpa_level <- names(gpa_levels)[sapply(gpa_levels, function(x) {
#     candidate_range >= x[1] && candidate_range <= x[2]
#   })]
#
#   # Find the corresponding scaled value for the candidate's GPA level
#   gpa_info <- scaled_values %>% filter(level == gpa_level)
#
#   # If no scaled value is found or no level is matched, return empty lists
#   if (is.na(gpa_level) || nrow(gpa_info) == 0) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items))
#   }
#
#   # Calculate the score using the scaled value
#   score <- gpa_info$new_value[1]
#
#   return(list(score = score, match_item = match_items, unmatch_item = unmatch_items))
# }

# Main function to calculate MrC scores # BUG

calculate_mrc_scores <- function(candidate, matchmaking_config) {
  edu_result <- score_mrc_education(candidate$Education, base_line_edu, matchmaking_config$Education$MrC_Education)
  we_result <- score_mrc_work_experience(candidate$Experience, base_line_we, matchmaking_config$Working_Experience$MrC_WE)
  gpa_result <- score_mrc_gpa(candidate$GPA, base_line_gpa, matchmaking_config$GPA$MrC_GPA)
  total_score <- edu_result$score + we_result$score + gpa_result$score

  create_matching_criteria_list <- function(result, field_name) {
    list(
      unmatch_item = if (!is.null(result$unmatch_item)) lapply(result$unmatch_item, function(x) list(name = x$name)) else list(),
      match_item = if (!is.null(result$match_item)) lapply(result$match_item, function(x) list(name = x$name)) else list()
    )
  }

  matching_criteria <- list(
    education = create_matching_criteria_list(edu_result, "education"),
    experience = create_matching_criteria_list(we_result, "experience"),
    gpa = create_matching_criteria_list(gpa_result, "gpa"),
    skill = list(unmatch_item = list(), match_item = list()),
    domicile = list(unmatch_item = list(), match_item = list()),
    salary = list(unmatch_item = list(), match_item = list()),
    industry = list(unmatch_item = list(), match_item = list())
  )

  return(list(
    Total_MrC_Score = round(total_score, 0),
    MrC_Score_Edu = round(edu_result$score, 0),
    MrC_Score_WE = round(we_result$score, 0),
    MrC_Score_GPA = round(gpa_result$score, 0),
    matching_criteria = matching_criteria
  ))
}


candidates <- if (nrow(candidates) > 0) {
  candidates %>%
    rowwise() %>%
    mutate(
      MrC_results = list(calculate_mrc_scores(cur_data(), matchmaking_config))
    ) %>%
    ungroup() %>%
    mutate(
      Total_MrC_Score = map_dbl(MrC_results, "Total_MrC_Score"),
      MrC_Score_Edu = map_dbl(MrC_results, "MrC_Score_Edu"),
      MrC_Score_WE = map_dbl(MrC_results, "MrC_Score_WE"),
      MrC_Score_GPA = map_dbl(MrC_results, "MrC_Score_GPA"),
      additional_values = map(MrC_results, "matching_criteria") |>
        map(~{
          if (!is.null(.x$MrC_Score_Edu) && !is.na(.x$MrC_Score_Edu) &&
              length(.x$education$match_item) > 0 && .x$MrC_Score_Edu == 0) {
            .x$education$match_item <- list()
          }
          if (!is.null(.x$MrC_Score_WE) && !is.na(.x$MrC_Score_WE) &&
              length(.x$experience$match_item) > 0 && .x$MrC_Score_WE == 0) {
            .x$experience$match_item <- list()
          }
          return(.x)
        })
    )
} else {
  candidates  # Return the empty dataframe as is
}

candidate_mrc <- candidates
#View(candidate_mrc)

#print(candidates_with_mrc)

#View(candidates_with_mrc)
#View(candidates)

print("Done MrC New Code")

print("Cal MrC Done")

# 3. Finalized Table
print("Finalized Table")

candidates <- if (nrow(candidates) > 0) {
  candidates %>%
  rename(Total_MrU_Score = Total_Score) |>
  mutate(
    Final_Score = paste(Total_MrU_Score, Total_MrC_Score, sep = "/")) |>
  arrange(desc(Total_MrU_Score), desc(Total_MrC_Score))
} else {
  candidates  # Return the empty dataframe as is
}

#View(candidates)



# Print results
# print(candidates)

df_result <- candidates

df_result <- if (nrow(df_result) > 0) {
  df_result %>%
  arrange(desc(Total_MrU_Score), desc(Total_MrC_Score)) |>
  rename(main_score = Total_MrU_Score,
         additional_score = Total_MrC_Score) |>
  select(user_job_vacancy_id, main_score, additional_score, matching_criteria, additional_values)
} else {
  df_result  # Return the empty dataframe as is
}

#print(candidates_stop)

#View(df_result)
#View(df_result$matching_criteria$Value)
glimpse(df_result)
print(df_result)
#write_csv(df_result, "develop_data_input/df_result_jv_13.csv")
print("Finalized Done")

# DB Write
print("Write for DB")


# result_list <- list()
#
# # Check if the dataframe is empty before starting the loop
# if (nrow(df_result) > 0) {
#   for (i in 1:nrow(df_result)) {
#     # Set schema for the query
#     schema <- params$schema
#     schema_sql <- paste0("SET search_path = ?schema;")
#     safe_schema_sql <- sqlInterpolate(con_write, schema_sql, schema = schema)
#     dbExecute(con_write, safe_schema_sql)
#
#     # Convert matching_criteria and additional_values to JSON without extra escaping
#     json_data_mru <- jsonlite::toJSON(df_result$matching_criteria[[i]], auto_unbox = TRUE, pretty = FALSE, null = "null")
#     json_data_mrc <- jsonlite::toJSON(df_result$additional_values[[i]], auto_unbox = TRUE, pretty = FALSE, null = "null")
#
#     # Handle single quotes for SQL
#     escaped_json_mru <- gsub("'", "''", json_data_mru)
#     escaped_json_mrc <- gsub("'", "''", json_data_mrc)
#
#     cleaned_main_score <- ifelse(is.na(df_result$main_score[i]), 0, df_result$main_score[i])
#     cleaned_additional_score <- ifelse(is.na(df_result$additional_score[i]), 0, df_result$additional_score[i])
#
#     # Prepare the INSERT query with jsonb casting
#     query <- paste0(
#       'INSERT INTO user_vacancy_matchmaking_results (user_job_vacancy_id, main_score, additional_score, matching_criteria, additional_values, created_at, updated_at) VALUES (',
#       df_result$user_job_vacancy_id[i], ", ",
#       cleaned_main_score, ", ",
#       cleaned_additional_score, ", ",
#       "'", escaped_json_mru, "'::jsonb, ",  # No additional backslashes in JSON
#       "'", escaped_json_mrc, "'::jsonb, ",  # No additional backslashes in JSON
#       "NOW(), NOW()) ",
#       "ON CONFLICT (user_job_vacancy_id) DO UPDATE SET ",
#       "main_score = EXCLUDED.main_score, additional_score = EXCLUDED.additional_score, ",
#       "matching_criteria = EXCLUDED.matching_criteria, additional_values = EXCLUDED.additional_values, updated_at = EXCLUDED.updated_at;"
#     )
#
#     # Execute the query inside the loop
#     DBI::dbExecute(con_write, query)
#
#     # Store the processed data in result_list for further operations if needed
#     result_list[[i]] <- list(
#       user_job_vacancy_id = df_result$user_job_vacancy_id[i],
#       main_score = cleaned_main_score,
#       additional_score = cleaned_additional_score,
#       matching_criteria = escaped_json_mru, #json_data without gsub modifications
#       additional_values = escaped_json_mrc #json_data without gsub modifications
#     )
#   }
# } else {
#   cat("No data available. Skipping database write process.\n")
# }
#
# # Convert the result list to a data frame
# # df_result <- do.call(rbind, lapply(result_list, as.data.frame))
#
# df_result <- do.call(rbind, lapply(result_list, function(x) {
#   as.data.frame(
#     x,
#     stringsAsFactors = FALSE,
#     row.names = "user_job_vacancy_id"
#   )
# }))
# # Return the cleaned data frame
# df_result
#
#
# # Close connection
# DBI::dbDisconnect(con_write)
# DBI::dbDisconnect(con_read)


























































