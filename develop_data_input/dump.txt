
Attaching package: ‘dplyr’

The following objects are masked from ‘package:stats’:

    filter, lag

The following objects are masked from ‘package:base’:

    intersect, setdiff, setequal, union


Attaching package: ‘lubridate’

The following objects are masked from ‘package:base’:

    date, intersect, setdiff, union


Attaching package: ‘zoo’

The following objects are masked from ‘package:base’:

    as.Date, as.Date.numeric

Running plumber API at http://0.0.0.0:5656
Running swagger Docs at http://127.0.0.1:5656/__docs__/

Attaching package: ‘jsonlite’

The following object is masked from ‘package:purrr’:

    flatten


Attaching package: ‘stringdist’

The following object is masked from ‘package:tidyr’:

    extract

[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 1655
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Painting Quality Intern"
$ id                               <int64> 1655
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "[], ActiveCampaign"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kota Jakarta Utara - DKI Jakarta"
$ job_level                        <chr> NA
$ job_type                         <chr> "intern"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Engineering, Kesehatan Hewan"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 2



$Major
$Major$weight
[1] 0

$Major$baseline
$Major$baseline[[1]]
[1] "Teknik Sipil"

$Major$baseline[[2]]
[1] "Teknik Informatika"



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 1


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 1


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0.4



$Education_Major
$Education_Major$conditions
$Education_Major$conditions[[1]]
$Education_Major$conditions[[1]]$weight
[1] 0

$Education_Major$conditions[[1]]$major_relevance
[1] "any"

$Education_Major$conditions[[1]]$candidate_education
$Education_Major$conditions[[1]]$candidate_education[[1]]
[1] "D3"



$Education_Major$conditions[[2]]
$Education_Major$conditions[[2]]$weight
[1] 0.5

$Education_Major$conditions[[2]]$major_relevance
[1] "not_related"

$Education_Major$conditions[[2]]$candidate_education
$Education_Major$conditions[[2]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[2]]$candidate_education[[2]]
[1] "D4"



$Education_Major$conditions[[3]]
$Education_Major$conditions[[3]]$weight
[1] 1

$Education_Major$conditions[[3]]$major_relevance
[1] "related"

$Education_Major$conditions[[3]]$candidate_education
$Education_Major$conditions[[3]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[3]]$candidate_education[[2]]
[1] "D4"




$Education_Major$MrU_Education_Major
$Education_Major$MrU_Education_Major$weight
[1] 1.4



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3


$Working_Experience$MrC_WE[[5]]
$Working_Experience$MrC_WE[[5]]$level
[1] "0-15 tahun"

$Working_Experience$MrC_WE[[5]]$value
[1] 0



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0.2



[1] "JSON Check"
List of 9
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 1
  .. ..$ weight: int 2
 $ Major             :List of 2
  ..$ weight  : int 0
  ..$ baseline:List of 2
  .. ..$ : chr "Teknik Sipil"
  .. ..$ : chr "Teknik Informatika"
 $ Domisili          :List of 1
  ..$ MrU_Domisil:List of 1
  .. ..$ weight: int 0
 $ Industry          :List of 1
  ..$ MrU_Industry:List of 1
  .. ..$ weight: int 0
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 2
  .. .. ..$ level: chr "SMA/SMK"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D3"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D4"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "S1"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "S2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S3"
  .. .. ..$ value: int 0
  ..$ MrU_Education:List of 1
  .. ..$ weight: int 0
 $ Skillntools       :List of 1
  ..$ MrU_Skillntools:List of 1
  .. ..$ weight: num 0.4
 $ Education_Major   :List of 2
  ..$ conditions         :List of 3
  .. ..$ :List of 3
  .. .. ..$ weight             : int 0
  .. .. ..$ major_relevance    : chr "any"
  .. .. ..$ candidate_education:List of 1
  .. .. .. ..$ : chr "D3"
  .. ..$ :List of 3
  .. .. ..$ weight             : num 0.5
  .. .. ..$ major_relevance    : chr "not_related"
  .. .. ..$ candidate_education:List of 2
  .. .. .. ..$ : chr "S1"
  .. .. .. ..$ : chr "D4"
  .. ..$ :List of 3
  .. .. ..$ weight             : int 1
  .. .. ..$ major_relevance    : chr "related"
  .. .. ..$ candidate_education:List of 2
  .. .. .. ..$ : chr "S1"
  .. .. .. ..$ : chr "D4"
  ..$ MrU_Education_Major:List of 1
  .. ..$ weight: num 1.4
 $ Expected_Salary   :List of 1
  ..$ MrU_ES:List of 1
  .. ..$ weight: int 0
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2 tahun"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2-5 tahun"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "7-12 tahun"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "10-15 tahun"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-15 tahun"
  .. .. ..$ value: int 0
  ..$ MrU_WE:List of 1
  .. ..$ weight: num 0.2
NULL
[1] "JSON Check Class"
[1] "integer"
Checking path: GPA$MrU_GPA$weight 
Retrieved value: 2 
Found non-zero value, assigning: 2.9-3.2 

Result: TRUE 
base_line_gpa value: 2.9-3.2 
[1] "Print GPA Base Line"
[1] NA
[[1]]
[1] "Teknik Sipil"

[[2]]
[1] "Teknik Informatika"

[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                    Role                     Email User ID
1 2024-12-23 07:17:13 Painting <NAME_EMAIL>      88
       Fullname             Kota - Kab Provinces Degree  GPA      Major
1 Estelle Darcy Anywhere St., Any City      <NA>     S1 3.55 Accounting
              Institution Degree 2nd GPA 2nd Major 2nd Institution 2nd
1 Universitas Gadjah Mada       <NA>      NA      <NA>            <NA>
     Job Role      YoE Experience
1 ux designer 3.809856          0
                                                                                                                Skill & Tools
1 {"Project Management","Robotic Control System Design","Skill name","Testing and Validation","User-Centric Problem-Solving"}
                                                                                                             CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_2-bb00a244-a503-43e1-b4ec-b6de442cc081.pdf
  Linkedin Link                  Portfolio Link Instagram Link Twitter Link
1          <NA> https://www.reallygreatsite.com           <NA>         <NA>
    State Status Availability  dob  pob  ktp                address
1 applied        open_to_work <NA> <NA> <NA> Anywhere St., Any City
               hp gender                             dcp current_max
1 +62811111111111 female tiga-ratus-dua-q6pwyu4bweuw9exx           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1           0          0          0           1655                  35
  Calculated YoE
1              3
'data.frame':	1 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 07:17:13"
 $ Role               : chr "Painting Quality Intern"
 $ Email              : chr "<EMAIL>"
 $ User ID            :integer64 88 
 $ Fullname           : chr "Estelle Darcy"
 $ Kota - Kab         : chr "Anywhere St., Any City"
 $ Provinces          : chr NA
 $ Degree             : chr "S1"
 $ GPA                : num 3.55
 $ Major              : chr "Accounting"
 $ Institution        : chr "Universitas Gadjah Mada"
 $ Degree 2nd         : chr NA
 $ GPA 2nd            : num NA
 $ Major 2nd          : chr NA
 $ Institution 2nd    : chr NA
 $ Job Role           : chr "ux designer"
 $ YoE                : num 3.81
 $ Experience         : int 0
 $ Skill & Tools      : 'pq__varchar' chr "{\"Project Management\",\"Robotic Control System Design\",\"Skill name\",\"Testing and Validation\",\"User-Cent"| __truncated__
 $ CV Upload          : chr "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_2-bb00a244-a503-43e1-b4ec-b6de442cc081.pdf"
 $ Linkedin Link      : chr NA
 $ Portfolio Link     : chr "https://www.reallygreatsite.com"
 $ Instagram Link     : chr NA
 $ Twitter Link       : chr NA
 $ State              : chr "applied"
 $ Status Availability: 'pq_status_availability' chr "open_to_work"
 $ dob                : POSIXct, format: NA
 $ pob                : chr NA
 $ ktp                : chr NA
 $ address            : chr "Anywhere St., Any City"
 $ hp                 : chr "+62811111111111"
 $ gender             : chr "female"
 $ dcp                : chr "tiga-ratus-dua-q6pwyu4bweuw9exx"
 $ current_max        :integer64 0 
 $ current_min        :integer64 0 
 $ expect_max         :integer64 0 
 $ expect_min         :integer64 0 
 $ job_vacancy_id     :integer64 1655 
 $ user_job_vacancy_id:integer64 35 
 $ Calculated YoE     : int 3
          Applied Date User ID            Fullname Job Role ID
1  2024-12-18 04:57:29     103     Putri Rosalinda           2
2  2024-12-23 02:21:15      84           tigaratus           2
3  2024-12-22 07:20:48      86       tigaratus sat           3
4  2024-12-23 01:31:08     100       Estelle Darcy          87
5  2024-12-23 07:17:13      88       Estelle Darcy          87
6  2024-12-18 04:45:39      97 Hasna Nazhifa Juned         774
7  2024-12-18 04:57:29     103     Putri Rosalinda         780
8  2024-12-18 04:57:29     103     Putri Rosalinda         780
9  2024-12-21 06:29:13      76    Rachelle Beaudry         850
10 2024-12-21 06:29:13      76    Rachelle Beaudry         851
11 2024-12-23 01:31:08     100       Estelle Darcy         851
12 2024-12-23 07:17:13      88       Estelle Darcy         851
13 2024-12-21 06:29:13      76    Rachelle Beaudry         852
14 2024-12-21 06:29:13      76    Rachelle Beaudry         852
15 2024-12-21 06:29:13      76    Rachelle Beaudry         852
16 2024-12-21 06:29:13      76    Rachelle Beaudry         853
17 2024-12-21 06:29:13      76    Rachelle Beaudry         853
18 2024-12-21 06:29:13      76    Rachelle Beaudry         853
19 2024-12-21 06:29:13      76    Rachelle Beaudry         854
20 2024-12-21 06:29:13      76    Rachelle Beaudry         854
21 2024-12-21 06:29:13      76    Rachelle Beaudry         854
22 2024-12-21 06:29:13      76    Rachelle Beaudry         855
23 2024-12-21 06:29:13      76    Rachelle Beaudry         856
24 2024-12-21 06:29:13      76    Rachelle Beaudry         857
25 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
26 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
27 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         860
28 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         861
29 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         862
30 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         863
31 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         864
32 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         865
33 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         866
34 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         867
35 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         868
36 2024-12-18 04:57:29     103     Putri Rosalinda         869
37 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
38 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
39 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         872
                                                                   Job Role
1                                                         digital marketing
2                                                         digital marketing
3                                                             fraud analyst
4                                                               ux designer
5                                                               ux designer
6                                                                  job name
7                                                          junior recruiter
8                                                          junior recruiter
9                                                               ux engineer
10                                                       system ux engineer
11                                                       system ux engineer
12                                                       system ux engineer
13                                                        junior accountant
14                                                        junior accountant
15                                                        junior accountant
16                                                               accountant
17                                                               accountant
18                                                               accountant
19                                                     accounting executive
20                                                     accounting executive
21                                                     accounting executive
22                                                    engineering executive
23                                                        graduate engineer
24                                                         project engineer
25                                            scorer for psychological test
26                                            scorer for psychological test
27                                                                 assessor
28                                 talent acquisition & document specialist
29                                                              interviewer
30                                                      freelance recruiter
31 consultant & project coordinator for recruitment, selection & assessment
32                                                    freelance facilitator
33                                                freelance project manager
34                                freelance trainer assistant (facilitator)
35                                                   recruitment specialist
36                                                       people recruitment
37                                         consultant & project coordinator
38                                         consultant & project coordinator
39                                              analyst & project assistant
     industry                                    company_name work_type
1  Technology                                          kucing full_time
2  Technology                                          Lalala full_time
3     Finance                                          asdsad full_time
4        <NA>                                        Morcelle full_time
5        <NA>                                        Morcelle full_time
6        <NA>                     Analyst & Project Assistant full_time
7        <NA>            PT Go Online Destinations (Pegipegi) full_time
8        <NA>            PT Go Online Destinations (Pegipegi) full_time
9        <NA>                                        Morcelle full_time
10       <NA>                             XarrowAI Industries full_time
11       <NA>                             XarrowAI Industries full_time
12       <NA>                             XarrowAI Industries full_time
13       <NA>                              Arowwai Industries full_time
14       <NA>                              Arowwai Industries full_time
15       <NA>                              Arowwai Industries full_time
16       <NA>                                    Salford & Co full_time
17       <NA>                                    Salford & Co full_time
18       <NA>                                    Salford & Co full_time
19       <NA>                            Borcelle Corporation full_time
20       <NA>                            Borcelle Corporation full_time
21       <NA>                            Borcelle Corporation full_time
22       <NA>                           Borcelle Technologies full_time
23       <NA>                              Arowwai Industries full_time
24       <NA>                                    Salford & Co full_time
25       <NA>                             ASTRA INTERNATIONAL freelance
26       <NA>                            ASTRA DAIHATSU MOTOR freelance
27       <NA>                         PT ENIGMA PUTRA MANDIRI freelance
28       <NA>                PT TENRIAWARU ELIT INTERNASIONAL full_time
29       <NA>                                        STAFFINC freelance
30       <NA>                         PT KILAT JAYA INDONESIA freelance
31       <NA>                     PT TJITRA SELARAS SAMPOERNO full_time
32       <NA> PT INSAN BARU INDONESIA (THE NEW YOU INSTITUTE) freelance
33       <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) freelance
34       <NA>      PT SAMALA SERASI UNGGUL (RUMAH SIAP KERJA) freelance
35       <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) full_time
36       <NA>                                  Social Connect full_time
37       <NA>                             Tjitra & associates full_time
38       <NA>                             Tjitra & associates full_time
39       <NA>                             Tjitra & associates full_time
               ends_at           starts_at       MoE
1  2024-09-18 04:51:45 2024-02-18 04:51:41  7.100001
2                 <NA> 2023-04-05 09:46:31 20.929944
3                 <NA> 2022-01-22 07:17:58 35.533383
4                 <NA> 2022-12-31 17:00:00 24.086577
5                 <NA> 2022-12-31 17:00:00 24.086577
6  2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
7  2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
8  2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
9                 <NA> 2022-12-31 17:00:00 24.086577
10 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
11 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
12 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
13 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
14 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
15 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
16 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
17 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
18 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
19                <NA> 2022-12-31 17:00:00 24.086577
20                <NA> 2022-12-31 17:00:00 24.086577
21                <NA> 2022-12-31 17:00:00 24.086577
22                <NA> 2022-12-31 17:00:00 24.086577
23 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
24 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
25                <NA> 2019-04-30 17:00:00 68.786577
26                <NA> 2019-12-31 17:00:00 60.619910
27 2022-11-30 17:00:00 2022-10-31 17:00:00  1.000000
28 2024-09-30 17:00:00 2023-01-31 17:00:00 20.266667
29 2021-11-30 17:00:00 2021-10-31 17:00:00  1.000000
30 2023-10-31 17:00:00 2023-09-30 17:00:00  1.033333
31 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
32 2024-01-31 17:00:00 2023-11-30 17:00:00  2.066667
33 2024-09-30 17:00:00 2024-04-30 17:00:00  5.100000
34                <NA> 2024-04-30 17:00:00  7.886577
35                <NA> 2024-09-30 17:00:00  2.786577
36 2022-11-30 17:00:00 2020-07-31 17:00:00 28.400000
37 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
38 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
39 2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
'data.frame':	39 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-18 04:57:29" "2024-12-23 02:21:15" ...
 $ User ID     :integer64 103 84 86 100 88 97 103 103 ... 
 $ Fullname    : chr  "Putri Rosalinda" "tigaratus" "tigaratus sat" "Estelle Darcy" ...
 $ Job Role ID :integer64 2 2 3 87 87 *********** ... 
 $ Job Role    : chr  "digital marketing" "digital marketing" "fraud analyst" "ux designer" ...
 $ industry    : chr  "Technology" "Technology" "Finance" NA ...
 $ company_name: chr  "kucing" "Lalala" "asdsad" "Morcelle" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2024-09-18 04:51:45" NA ...
 $ starts_at   : POSIXct, format: "2024-02-18 04:51:41" "2023-04-05 09:46:31" ...
 $ MoE         : num  7.1 20.9 35.5 24.1 24.1 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
'data.frame':	1 obs. of  14 variables:
 $ Candidate          : chr "Estelle Darcy"
 $ Education          : chr "S1"
 $ Institution        : chr "Universitas Gadjah Mada"
 $ Experience         : num 3
 $ GPA                : num 3.55
 $ Domisili           : chr "Anywhere St., Any City"
 $ Expected_Salary    :integer64 0 
 $ Industry           : chr "{\"\"}"
 $ Skillntools        : 'pq__varchar' chr "{\"Project Management\",\"Robotic Control System Design\",\"Skill name\",\"Testing and Validation\",\"User-Cent"| __truncated__
 $ Major              : chr "Accounting"
 $ user_job_vacancy_id:integer64 35 
 $ edu_matched        : logi TRUE
 $ major_match        : logi FALSE
 $ edu_major_weight   : num 0.5
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] "S1"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
Warning: `data_frame()` was deprecated in tibble 1.1.0.
ℹ Please use `tibble()` instead.
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          1
8 S3          2


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0.2


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 2


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0.4



$Education_Major
$Education_Major$MrU_Education_Major
[1] 1.4


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0.2

$weight_gpa
[1] 2

$weight_domisili
[1] 0

$weight_es
[1] 0

$weight_industry
[1] 0

$weight_skill
[1] 0.4

$weight_education_major
[1] 1.4

[1] "Scaled value"
$weight_we
[1] 5

$weight_gpa
[1] 50

$weight_skill
[1] 10

$weight_education_major
[1] 35

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Test New Create List Matching Criteria"
[1] "Done New Calculation"
[1] "Test New Code Done"
      Candidate Education             Institution Experience  GPA
1 Estelle Darcy        S1 Universitas Gadjah Mada          3 3.55
                Domisili Expected_Salary Industry
1 Anywhere St., Any City               0     {""}
                                                                                                                  Skillntools
1 {"Project Management","Robotic Control System Design","Skill name","Testing and Validation","User-Centric Problem-Solving"}
       Major user_job_vacancy_id edu_matched major_match edu_major_weight
1 Accounting                  35        TRUE       FALSE              0.5
  MrU_Score_Edu MrU_Score_WE MrU_Score_GPA MrU_Score_Domisili MrU_Score_ES
1            17            0             0                  0            0
  MrU_Score_Industry MrU_Score_Skillntools Match_Item_Edu Unmatch_Item_Edu
1                  0                     0       S1, TRUE             NULL
  Match_Item_WE Unmatch_Item_WE Match_Item_GPA            Unmatch_Item_GPA
1          NULL            NULL           NULL Invalid baseline GPA, FALSE
                      Match_Item_Domisili         Unmatch_Item_Domisili
1 Kota Jakarta Utara - DKI Jakarta, FALSE Anywhere St., Any City, FALSE
  Match_Item_ES Unmatch_Item_ES Match_Item_Industry Unmatch_Item_Industry
1          0, 1            NULL                NULL                  NULL
            Match_Item_Skillntools
1 [], FALSE, ActiveCampaign, FALSE
                                                                                                                                Unmatch_Item_Skillntools
1 Project Management, FALSE, Robotic Control System Design, FALSE, Skill name, FALSE, Testing and Validation, FALSE, User-Centric Problem-Solving, FALSE
  Match_Item_Major                             Unmatch_Item_Major Total_Score
1             NULL Teknik Sipil, FALSE, Teknik Informatika, FALSE          17
                                                                                                                                                                                                                                                                                                                                                 matching_criteria
1 0, TRUE, Kota Jakarta Utara - DKI Jakarta, FALSE, Anywhere St., Any City, FALSE, Invalid baseline GPA, FALSE, [], FALSE, ActiveCampaign, FALSE, Project Management, FALSE, Robotic Control System Design, FALSE, Skill name, FALSE, Testing and Validation, FALSE, User-Centric Problem-Solving, FALSE, S1, TRUE, Teknik Sipil, FALSE, Teknik Informatika, FALSE
[1] "Cal MrU Done"
[1] "Scale MrC"
# A tibble: 4 × 4
  level       value scale_factor new_value
  <chr>       <dbl>        <dbl>     <dbl>
1 0-3 tahun       0          1.5         0
2 3-6 tahun       0          1.5         0
3 6-11 tahun      0          1.5         0
4 11-99 tahun     0          1.5         0
[1] "Scale MrC Done"
[1] "Start MrC New Code"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

Warning: There was 1 warning in `mutate()`.
ℹ In argument: `MrC_results = list(calculate_mrc_scores(cur_data(),
  matchmaking_config))`.
ℹ In row 1.
Caused by warning:
! `cur_data()` was deprecated in dplyr 1.1.0.
ℹ Please use `pick()` instead.
[1] "Done MrC New Code"
[1] "Cal MrC Done"
[1] "Finalized Table"
Rows: 1
Columns: 5
$ user_job_vacancy_id <int64> 35
$ main_score          <dbl> 17
$ additional_score    <dbl> 0
$ matching_criteria   <named list> [[[[0, TRUE]], []], [[], []], [[["Kota Jakarta U…
$ additional_values   <list> [[[], []], [[], []], [[], []], [[], []], [[], []],…
# A tibble: 1 × 5
  user_job_vacancy_id main_score additional_score matching_criteria
              <int64>      <dbl>            <dbl> <named list>     
1                  35         17                0 <named list [8]> 
# ℹ 1 more variable: additional_values <list>
[1] "Finalized Done"
[1] "Write for DB"
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 1657
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Test Config Match Rate"
$ id                               <int64> 1657
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "Python"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Engineering"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Ilmu Komputer"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$name
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] TRUE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$name
[1] "D1"

$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0

$Education$MrC_Education[[2]]$disabled
[1] TRUE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$name
[1] "D2"

$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0

$Education$MrC_Education[[3]]$disabled
[1] TRUE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$name
[1] "D3"

$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0

$Education$MrC_Education[[4]]$disabled
[1] TRUE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$name
[1] "D4"

$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0

$Education$MrC_Education[[5]]$disabled
[1] TRUE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$name
[1] "S1"

$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$name
[1] "S2"

$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$name
[1] "S3"

$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3

$Education$MrU_Education$required
[1] TRUE

$Education$MrU_Education$minimum_config
$Education$MrU_Education$minimum_config$key
[1] "name"

$Education$MrU_Education$minimum_config$value
[1] "S1"




$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "Python"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$name
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$name
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$name
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$name
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2

$Working_Experience$MrU_WE$required
[1] TRUE

$Working_Experience$MrU_WE$minimum_config
$Working_Experience$MrU_WE$minimum_config$key
[1] "name"

$Working_Experience$MrU_WE$minimum_config$value
[1] "0-2 tahun"




[1] "JSON Check"
List of 8
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Major             :List of 2
  ..$ MrC_Major:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Ilmu Komputer"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Major:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Domisili          :List of 2
  ..$ MrC_Domisil:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Kabupaten Aceh Barat - Aceh"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Domisil:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Industry          :List of 2
  ..$ MrC_Industry: list()
  ..$ MrU_Industry:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 5
  .. .. ..$ name       : chr "SMA/SMK"
  .. .. ..$ level      : chr "SMA/SMK"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 5
  .. .. ..$ name       : chr "D1"
  .. .. ..$ level      : chr "D1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 5
  .. .. ..$ name       : chr "D2"
  .. .. ..$ level      : chr "D2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 5
  .. .. ..$ name       : chr "D3"
  .. .. ..$ level      : chr "D3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 3
  .. ..$ :List of 5
  .. .. ..$ name       : chr "D4"
  .. .. ..$ level      : chr "D4"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 4
  .. ..$ :List of 5
  .. .. ..$ name       : chr "S1"
  .. .. ..$ level      : chr "S1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 5
  .. ..$ :List of 5
  .. .. ..$ name       : chr "S2"
  .. .. ..$ level      : chr "S2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 6
  .. ..$ :List of 5
  .. .. ..$ name       : chr "S3"
  .. .. ..$ level      : chr "S3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 7
  ..$ MrU_Education:List of 3
  .. ..$ weight        : int 3
  .. ..$ required      : logi TRUE
  .. ..$ minimum_config:List of 2
  .. .. ..$ key  : chr "name"
  .. .. ..$ value: chr "S1"
 $ Skillntools       :List of 2
  ..$ MrC_Skillntools:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Python"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Skillntools:List of 2
  .. ..$ weight  : int 3
  .. ..$ required: logi TRUE
 $ Expected_Salary   :List of 2
  ..$ MrC_ES:List of 3
  .. ..$ :List of 3
  .. .. ..$ name : chr "< 1.0"
  .. .. ..$ level: chr "< 1.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 3
  .. .. ..$ name : chr "1.0 - 2.0"
  .. .. ..$ level: chr "1.0 - 2.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 3
  .. .. ..$ name : chr "> 2.0"
  .. .. ..$ level: chr "> 2.0"
  .. .. ..$ value: int 0
  ..$ MrU_ES:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 5
  .. .. ..$ name       : chr "0-2 tahun"
  .. .. ..$ level      : chr "0-2 tahun"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 5
  .. .. ..$ name       : chr "2-5 tahun"
  .. .. ..$ level      : chr "2-5 tahun"
  .. .. ..$ value      : int 1
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 5
  .. .. ..$ name       : chr "7-12 tahun"
  .. .. ..$ level      : chr "7-12 tahun"
  .. .. ..$ value      : int 2
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 5
  .. .. ..$ name       : chr "10-15 tahun"
  .. .. ..$ level      : chr "10-15 tahun"
  .. .. ..$ value      : int 3
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 3
  ..$ MrU_WE:List of 3
  .. ..$ weight        : int 2
  .. ..$ required      : logi TRUE
  .. ..$ minimum_config:List of 2
  .. .. ..$ key  : chr "name"
  .. .. ..$ value: chr "0-2 tahun"
NULL
[1] "JSON Check Class"
[1] "integer"
Checking path: GPA$MrU_GPA$weight 
Retrieved value: 0 
All values are zero, assigning NA

Result: FALSE 
base_line_gpa value: NA 
[1] "Print GPA Base Line"
[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                   Role                     Email User ID
1 2024-12-23 01:32:25 Test Config <NAME_EMAIL>      84
2 2024-12-23 02:22:00 Test Config <NAME_EMAIL>      86
3 2024-12-23 07:17:24 Test Config <NAME_EMAIL>      88
       Fullname                  Kota - Kab Provinces Degree  GPA      Major
1     tigaratus Kabupaten Aceh Barat - Aceh      <NA>     S1 3.55 Accounting
2 tigaratus sat Kabupaten Aceh Barat - Aceh      <NA>     S1 3.55       <NA>
3 Estelle Darcy      Anywhere St., Any City      <NA>     S1 3.55 Accounting
              Institution Degree 2nd GPA 2nd Major 2nd Institution 2nd
1       Aarhus University       <NA>      NA      <NA>            <NA>
2 Universitas Gadjah Mada       <NA>      NA      <NA>            <NA>
3 Universitas Gadjah Mada       <NA>      NA      <NA>            <NA>
           Job Role      YoE Experience
1 digital marketing 1.720279          0
2     fraud analyst 2.920562          0
3       ux designer 3.809865          0
                                                                                                                Skill & Tools
1                                                                                                                    {Python}
2                                                                                             {"Google Spreadsheet",Python,R}
3 {"Project Management","Robotic Control System Design","Skill name","Testing and Validation","User-Centric Problem-Solving"}
                                                                                                             CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf
2 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-b3bdddbb-6c08-4f5a-9788-592cbec3ecef.pdf
3 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_2-bb00a244-a503-43e1-b4ec-b6de442cc081.pdf
  Linkedin Link                  Portfolio Link Instagram Link Twitter Link
1          <NA>                            <NA>           <NA>         <NA>
2          <NA>                            <NA>           <NA>         <NA>
3          <NA> https://www.reallygreatsite.com           <NA>         <NA>
    State Status Availability  dob  pob  ktp                address
1 applied        open_to_work <NA> <NA> <NA>                   <NA>
2 applied        open_to_work <NA> <NA> <NA>                   <NA>
3 applied        open_to_work <NA> <NA> <NA> Anywhere St., Any City
               hp gender                             dcp current_max
1 +62121121121212   male      tigaratus-p18dsjrj5bhuym65           0
2 +62213123131231   male  tigaratus-sat-qahu71ufhjgyoi9a           0
3 +62811111111111 female tiga-ratus-dua-q6pwyu4bweuw9exx           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1           0          0          0           1657                  32
2           0          0          0           1657                  34
3           0          0          0           1657                  36
  Calculated YoE
1              1
2              2
3              3
'data.frame':	3 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 01:32:25" "2024-12-23 02:22:00" ...
 $ Role               : chr  "Test Config Match Rate" "Test Config Match Rate" "Test Config Match Rate"
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 84 86 88 
 $ Fullname           : chr  "tigaratus" "tigaratus sat" "Estelle Darcy"
 $ Kota - Kab         : chr  "Kabupaten Aceh Barat - Aceh" "Kabupaten Aceh Barat - Aceh" "Anywhere St., Any City"
 $ Provinces          : chr  NA NA NA
 $ Degree             : chr  "S1" "S1" "S1"
 $ GPA                : num  3.55 3.55 3.55
 $ Major              : chr  "Accounting" NA "Accounting"
 $ Institution        : chr  "Aarhus University" "Universitas Gadjah Mada" "Universitas Gadjah Mada"
 $ Degree 2nd         : chr  NA NA NA
 $ GPA 2nd            : num  NA NA NA
 $ Major 2nd          : chr  NA NA NA
 $ Institution 2nd    : chr  NA NA NA
 $ Job Role           : chr  "digital marketing" "fraud analyst" "ux designer"
 $ YoE                : num  1.72 2.92 3.81
 $ Experience         : int  0 0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{Python}" "{\"Google Spreadsheet\",Python,R}" "{\"Project Management\",\"Robotic Control System Design\",\"Skill name\",\"Testing and Validation\",\"User-Cent"| __truncated__
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-b3bdddbb-6c08-4f5a-9788-592cbec3ecef.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_2-bb00a244-a503-43e1-b4ec-b6de442cc081.pdf"
 $ Linkedin Link      : chr  NA NA NA
 $ Portfolio Link     : chr  NA NA "https://www.reallygreatsite.com"
 $ Instagram Link     : chr  NA NA NA
 $ Twitter Link       : chr  NA NA NA
 $ State              : chr  "applied" "applied" "applied"
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: NA NA ...
 $ pob                : chr  NA NA NA
 $ ktp                : chr  NA NA NA
 $ address            : chr  NA NA "Anywhere St., Any City"
 $ hp                 : chr  "+62121121121212" "+62213123131231" "+62811111111111"
 $ gender             : chr  "male" "male" "female"
 $ dcp                : chr  "tigaratus-p18dsjrj5bhuym65" "tigaratus-sat-qahu71ufhjgyoi9a" "tiga-ratus-dua-q6pwyu4bweuw9exx"
 $ current_max        :integer64 0 0 0 
 $ current_min        :integer64 0 0 0 
 $ expect_max         :integer64 0 0 0 
 $ expect_min         :integer64 0 0 0 
 $ job_vacancy_id     :integer64 1657 1657 1657 
 $ user_job_vacancy_id:integer64 32 34 36 
 $ Calculated YoE     : int  1 2 3
         Applied Date User ID      Fullname Job Role ID           Job Role
1 2024-12-23 01:32:25      84     tigaratus           2  digital marketing
2 2024-12-23 02:22:00      86 tigaratus sat           3      fraud analyst
3 2024-12-23 07:17:24      88 Estelle Darcy          87        ux designer
4 2024-12-23 07:17:24      88 Estelle Darcy         851 system ux engineer
    industry        company_name work_type             ends_at
1 Technology              Lalala full_time                <NA>
2    Finance              asdsad full_time                <NA>
3       <NA>            Morcelle full_time                <NA>
4       <NA> XarrowAI Industries full_time 2022-11-30 17:00:00
            starts_at      MoE
1 2023-04-05 09:46:31 20.93006
2 2022-01-22 07:17:58 35.53350
3 2022-12-31 17:00:00 24.08669
4 2021-01-31 17:00:00 22.26667
'data.frame':	4 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 01:32:25" "2024-12-23 02:22:00" ...
 $ User ID     :integer64 84 86 88 88 
 $ Fullname    : chr  "tigaratus" "tigaratus sat" "Estelle Darcy" "Estelle Darcy"
 $ Job Role ID :integer64 2 3 87 851 
 $ Job Role    : chr  "digital marketing" "fraud analyst" "ux designer" "system ux engineer"
 $ industry    : chr  "Technology" "Finance" NA NA
 $ company_name: chr  "Lalala" "asdsad" "Morcelle" "XarrowAI Industries"
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time"
 $ ends_at     : POSIXct, format: NA NA ...
 $ starts_at   : POSIXct, format: "2023-04-05 09:46:31" "2022-01-22 07:17:58" ...
 $ MoE         : num  20.9 35.5 24.1 22.3
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	3 obs. of  14 variables:
 $ Candidate          : chr  "tigaratus" "tigaratus sat" "Estelle Darcy"
 $ Education          : chr  "S1" "S1" "S1"
 $ Institution        : chr  "Aarhus University" "Universitas Gadjah Mada" "Universitas Gadjah Mada"
 $ Experience         : num  1 2 3
 $ GPA                : num  3.55 3.55 3.55
 $ Domisili           : chr  "Kabupaten Aceh Barat - Aceh" "Kabupaten Aceh Barat - Aceh" "Anywhere St., Any City"
 $ Expected_Salary    :integer64 0 0 0 
 $ Industry           : chr  "{\"Technology\"}" "{\"Finance\"}" "{\"\"}"
 $ Skillntools        : 'pq__varchar' chr  "{Python}" "{\"Google Spreadsheet\",Python,R}" "{\"Project Management\",\"Robotic Control System Design\",\"Skill name\",\"Testing and Validation\",\"User-Cent"| __truncated__
 $ Major              : chr  "Accounting" NA "Accounting"
 $ user_job_vacancy_id:integer64 32 34 36 
 $ edu_matched        : logi  TRUE TRUE TRUE
 $ major_match        : logi  FALSE FALSE FALSE
 $ edu_major_weight   : logi  NA NA NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] "S1"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] "0-3 tahun"
[1] "0-3 tahun"
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          1
8 S3          2


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       1
3 6-11 tahun      2
4 11-99 tahun     3
5 0-99 tahun      4


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 3

$weight_we
[1] 2

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_education
[1] 30

$weight_we
[1] 20

$weight_domisili
[1] 10

$weight_es
[1] 10

$weight_skill
[1] 30

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
