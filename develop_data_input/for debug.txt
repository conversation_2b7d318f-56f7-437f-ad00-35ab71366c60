str candidate before Calculation

'data.frame':	17 obs. of  19 variables:
 $ Candidate          : chr  "<PERSON><PERSON>" "<PERSON><PERSON><PERSON>" "Imelda Putri Azzahra" "test" ...
 $ Education          : chr  "D3" "S1" "S3" NA ...
 $ Institution        : chr  "Aalborg University" "Universitas Indonesia" "Stikes Mitra Ria Husada" "Aberystwyth University" ...
 $ Experience         : num  0 3 0 0 2 8 0 1 4 1 ...
 $ GPA                : num  4 3.55 4 0 3.5 4 0 99 98 99 ...
 $ Domisili           : chr  "jakarta" "123 Anywhere St., Any City" NA NA ...
 $ Expected_Salary    :integer64 100000 1 10000000 1 1 1 1000 1 ... 
 $ Industry           : chr  "{\"Technology\"}" "{\"\"}" "{\"Technology\"}" "{\"Finance\"}" ...
 $ Skillntools        : 'pq__varchar' chr  "{\"Microsoft Excel\",Python}" "{Automation,\"Efficiency Improvement\",\"Project Management\",\"Robotic Control\",\"System Optimization\",Testi"| __truncated__ "{<PERSON><PERSON>,\"layanan pelanggan\",\"Microsoft Excel\",\"Ms. Access\",Tableau,Ubersuggest}" "{Git}" ...
 $ Major              : chr  "Teknik Metalurgi" "Administrasi" "Administrasi" "Administrasi Asuransi dan Aktuaria" ...
 $ user_job_vacancy_id:integer64 2 113 3 12 1 4 6 5 ... 
 $ dob                : POSIXct, format: NA NA ...
 $ age                : num  NA NA NA NA NA NA NA NA NA NA ...
 $ toefl_score        : num  NA 555 700 1 NA 450 67 430 450 400 ...
 $ edu_matched        : logi  TRUE FALSE FALSE NA FALSE FALSE ...
 $ major_match        : logi  NA NA NA NA NA NA ...
 $ edu_major_weight   : logi  NA NA NA NA NA NA ...
 $ age_level          : num  0 0 0 0 0 0 0 0 0 0 ...
 $ toefl_level        : num  0 3 0 1 0 3 1 2 3 2 ...


base_line_age -> 30

str candidate before transform the Age

'data.frame':	17 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-13 03:09:43" "2024-12-31 06:15:24" ...
 $ Role               : chr  "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" ...
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>" ...
 $ User ID            :integer64 38 37 40 3 36 43 45 44 ... 
 $ Fullname           : chr  "Putri Rosalinda" "Estelle Darcy" "Imelda Putri Azzahra" "test" ...
 $ Kota - Kab         : chr  "jakarta" "123 Anywhere St., Any City" NA NA ...
 $ Provinces          : chr  NA NA NA NA ...
 $ Degree             : chr  "D3" "S1" "S3" NA ...
 $ GPA                : num  4 3.55 4 0 3.5 4 0 99 98 99 ...
 $ Major              : chr  "Teknik Metalurgi" "Administrasi" "Administrasi" "Administrasi Asuransi dan Aktuaria" ...
 $ Institution        : chr  "Aalborg University" "Universitas Indonesia" "Stikes Mitra Ria Husada" "Aberystwyth University" ...
 $ Degree 2nd         : chr  NA NA NA NA ...
 $ GPA 2nd            : num  NA NA NA NA NA NA NA NA NA NA ...
 $ Major 2nd          : chr  NA NA NA NA ...
 $ Institution 2nd    : chr  NA NA NA NA ...
 $ Job Role           : chr  "digital marketing" "ux engineer" "data science" "fraud analyst" ...
 $ YoE                : num  0.1319 3.8317 0.0822 0.6353 2.5056 ...
 $ Experience         : int  0 0 0 0 0 0 0 0 0 0 ...
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Microsoft Excel\",Python}" "{Automation,\"Efficiency Improvement\",\"Project Management\",\"Robotic Control\",\"System Optimization\",Testi"| __truncated__ "{Canva,\"layanan pelanggan\",\"Microsoft Excel\",\"Ms. Access\",Tableau,Ubersuggest}" "{Git}" ...
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Surat_Penawaran_Rekrutmen_Mandiri__POS_I"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-4862907d-64b4-4308-b595-2de1d29f3ddb.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FAQ_Toyota_Internship_Program_Batch_1_20"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/sample193c6b73d1e76431faaa9f02c70e088e3-"| __truncated__ ...
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/aksjas" NA "" NA ...
 $ Portfolio Link     : chr  "https://aksjaks.co" "https://www.reallygreatsite.com" "" NA ...
 $ Instagram Link     : chr  NA NA NA NA ...
 $ Twitter Link       : chr  NA NA NA NA ...
 $ State              : chr  "rejected" "applied" "applied" "rejected" ...
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work" "open_to_work" ...
 $ dob                : POSIXct, format: "1996-02-01 02:59:04" "2000-12-01 09:58:01" ...
 $ pob                : chr  NA NA NA NA ...
 $ ktp                : chr  NA NA NA NA ...
 $ address            : chr  "Jakarta" "123 Anywhere St., Any City" NA NA ...
 $ hp                 : chr  "+6281213363001" "+62123456781234" "+628197608102" "+6211111111" ...
 $ gender             : chr  "female" "female" "female" "male" ...
 $ dcp                : chr  NA NA NA NA ...
 $ current_max        :integer64 0 0 0 0 0 0 0 0 ... 
 $ current_min        :integer64 NA NA NA NA NA NA NA NA ... 
 $ expect_max         :integer64 20000000 2 30000000 2 2 2 2000 2 ... 
 $ expect_min         :integer64 100000 1 10000000 1 1 1 1000 1 ... 
 $ job_vacancy_id     :integer64 8 8 8 8 8 8 8 8 ... 
 $ user_job_vacancy_id:integer64 2 113 3 12 1 4 6 5 ... 
 $ Calculated YoE     : int  0 3 0 0 2 8 0 1 4 1 ...


potential issues in this mutate calculation age from dob

candidates <- candidates |>
  select(Fullname, Degree, Institution, `Calculated YoE`, GPA, `Kota - Kab`, expect_min, industry, `Skill & Tools`, Major,
         user_job_vacancy_id,
         #<---Pos Enhance--->#
         dob) |>
  rename(Candidate = Fullname,
         Education = Degree,
         Experience = `Calculated YoE`,
         GPA = GPA,
         Domisili = `Kota - Kab`,
         #Expected_Salary_Min = expect_min,
         #Expected_Salary_Max = expect_max,
         Expected_Salary = expect_min,
         Industry = industry,
         Skillntools = `Skill & Tools`
  ) |>
  mutate(Experience = floor(Experience),
         dob = dmy_hm(dob),  # Convert dob to datetime using lubridate #<---Pos Enhance--->#
         age = as.numeric(difftime(Sys.Date(), dob, units = "weeks")) / 52.25,  # Calculate age in years #<---Pos Enhance--->#
         age = floor(age) #<---Pos Enhance--->#
         )