[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)