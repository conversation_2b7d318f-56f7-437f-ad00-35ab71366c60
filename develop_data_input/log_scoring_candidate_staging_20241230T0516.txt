
Attaching package: ‘dplyr’

The following objects are masked from ‘package:stats’:

    filter, lag

The following objects are masked from ‘package:base’:

    intersect, setdiff, setequal, union


Attaching package: ‘lubridate’

The following objects are masked from ‘package:base’:

    date, intersect, setdiff, union


Attaching package: ‘zoo’

The following objects are masked from ‘package:base’:

    as.Date, as.Date.numeric

Running plumber API at http://0.0.0.0:5656
Running swagger Docs at http://127.0.0.1:5656/__docs__/

Attaching package: ‘jsonlite’

The following object is masked from ‘package:purrr’:

    flatten


Attaching package: ‘stringdist’

The following object is masked from ‘package:tidyr’:

    extract

[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 1655
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Painting Quality Intern"
$ id                               <int64> 1655
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "[], ActiveCampaign"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kota Jakarta Utara - DKI Jakarta"
$ job_level                        <chr> NA
$ job_type                         <chr> "intern"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Ilmu Komputer, Teknik Komputer"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 2



$Major
$Major$weight
[1] 0

$Major$baseline
$Major$baseline[[1]]
[1] "Teknik Sipil"

$Major$baseline[[2]]
[1] "Teknik Informatika"



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 1


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 1


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0.4



$Education_Major
$Education_Major$conditions
$Education_Major$conditions[[1]]
$Education_Major$conditions[[1]]$weight
[1] 0

$Education_Major$conditions[[1]]$major_relevance
[1] "any"

$Education_Major$conditions[[1]]$candidate_education
$Education_Major$conditions[[1]]$candidate_education[[1]]
[1] "D3"



$Education_Major$conditions[[2]]
$Education_Major$conditions[[2]]$weight
[1] 0.5

$Education_Major$conditions[[2]]$major_relevance
[1] "not_related"

$Education_Major$conditions[[2]]$candidate_education
$Education_Major$conditions[[2]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[2]]$candidate_education[[2]]
[1] "D4"



$Education_Major$conditions[[3]]
$Education_Major$conditions[[3]]$weight
[1] 1

$Education_Major$conditions[[3]]$major_relevance
[1] "related"

$Education_Major$conditions[[3]]$candidate_education
$Education_Major$conditions[[3]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[3]]$candidate_education[[2]]
[1] "D4"




$Education_Major$MrU_Education_Major
$Education_Major$MrU_Education_Major$weight
[1] 1.4



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3


$Working_Experience$MrC_WE[[5]]
$Working_Experience$MrC_WE[[5]]$level
[1] "0-15 tahun"

$Working_Experience$MrC_WE[[5]]$value
[1] 0



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0.2



[1] "JSON Check"
List of 9
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 1
  .. ..$ weight: int 2
 $ Major             :List of 2
  ..$ weight  : int 0
  ..$ baseline:List of 2
  .. ..$ : chr "Teknik Sipil"
  .. ..$ : chr "Teknik Informatika"
 $ Domisili          :List of 1
  ..$ MrU_Domisil:List of 1
  .. ..$ weight: int 0
 $ Industry          :List of 1
  ..$ MrU_Industry:List of 1
  .. ..$ weight: int 0
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 2
  .. .. ..$ level: chr "SMA/SMK"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D3"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D4"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "S1"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "S2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S3"
  .. .. ..$ value: int 0
  ..$ MrU_Education:List of 1
  .. ..$ weight: int 0
 $ Skillntools       :List of 1
  ..$ MrU_Skillntools:List of 1
  .. ..$ weight: num 0.4
 $ Education_Major   :List of 2
  ..$ conditions         :List of 3
  .. ..$ :List of 3
  .. .. ..$ weight             : int 0
  .. .. ..$ major_relevance    : chr "any"
  .. .. ..$ candidate_education:List of 1
  .. .. .. ..$ : chr "D3"
  .. ..$ :List of 3
  .. .. ..$ weight             : num 0.5
  .. .. ..$ major_relevance    : chr "not_related"
  .. .. ..$ candidate_education:List of 2
  .. .. .. ..$ : chr "S1"
  .. .. .. ..$ : chr "D4"
  .. ..$ :List of 3
  .. .. ..$ weight             : int 1
  .. .. ..$ major_relevance    : chr "related"
  .. .. ..$ candidate_education:List of 2
  .. .. .. ..$ : chr "S1"
  .. .. .. ..$ : chr "D4"
  ..$ MrU_Education_Major:List of 1
  .. ..$ weight: num 1.4
 $ Expected_Salary   :List of 1
  ..$ MrU_ES:List of 1
  .. ..$ weight: int 0
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2 tahun"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2-5 tahun"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "7-12 tahun"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "10-15 tahun"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-15 tahun"
  .. .. ..$ value: int 0
  ..$ MrU_WE:List of 1
  .. ..$ weight: num 0.2
NULL
[1] "JSON Check Class"
[1] "integer"
[1] "Run the tests"

Test 1:
Success: TRUE 
Value: 2.9-3.2 
Error: None 
Message: Successfully found non-zero value 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] "2.9-3.2"
[[1]]
[1] "Teknik Sipil"

[[2]]
[1] "Teknik Informatika"

[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                    Role                   Email User ID
1 2024-12-30 03:49:28 Painting <NAME_EMAIL>     108
       Fullname                       Kota - Kab Provinces Degree  GPA
1 BENJAMIN SHAH Kota Jakarta Pusat - DKI Jakarta      <NA>     S1 3.55
                  Major                          Institution Degree 2nd GPA 2nd
1 Automotive Technology University Of Engineering Excellence       <NA>      NA
  Major 2nd Institution 2nd              Job Role     YoE Experience
1      <NA>            <NA> mechatronics engineer 4.66699          0
                                                                                                                                                                                                           Skill & Tools
1 {Automation,"Client Collaboration","Efficiency Improvement","Feasibility Studies","Manufacturing Processes","Project Management","Robotic Control System","Skill name","Technical Expertise","Testing and Validation"}
                                                                                                             CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_4-50f47473-c7cd-440e-bbb5-bb793a93feaf.pdf
  Linkedin Link Portfolio Link Instagram Link Twitter Link   State
1          <NA>           <NA>           <NA>         <NA> applied
  Status Availability  dob  pob  ktp                    address            hp
1        open_to_work <NA> <NA> <NA> 123 Anywhere St., Any City +62**********
  gender  dcp current_max current_min expect_max expect_min job_vacancy_id
1   male <NA>           0           0          0          0           1655
  user_job_vacancy_id Calculated YoE
1                 116              4
'data.frame':	1 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-30 03:49:28"
 $ Role               : chr "Painting Quality Intern"
 $ Email              : chr "<EMAIL>"
 $ User ID            :integer64 108 
 $ Fullname           : chr "BENJAMIN SHAH"
 $ Kota - Kab         : chr "Kota Jakarta Pusat - DKI Jakarta"
 $ Provinces          : chr NA
 $ Degree             : chr "S1"
 $ GPA                : num 3.55
 $ Major              : chr "Automotive Technology"
 $ Institution        : chr "University Of Engineering Excellence"
 $ Degree 2nd         : chr NA
 $ GPA 2nd            : num NA
 $ Major 2nd          : chr NA
 $ Institution 2nd    : chr NA
 $ Job Role           : chr "mechatronics engineer"
 $ YoE                : num 4.67
 $ Experience         : int 0
 $ Skill & Tools      : 'pq__varchar' chr "{Automation,\"Client Collaboration\",\"Efficiency Improvement\",\"Feasibility Studies\",\"Manufacturing Process"| __truncated__
 $ CV Upload          : chr "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/cv_dummy_4-50f47473-c7cd-440e-bbb5-bb793a93feaf.pdf"
 $ Linkedin Link      : chr NA
 $ Portfolio Link     : chr NA
 $ Instagram Link     : chr NA
 $ Twitter Link       : chr NA
 $ State              : chr "applied"
 $ Status Availability: 'pq_status_availability' chr "open_to_work"
 $ dob                : POSIXct, format: NA
 $ pob                : chr NA
 $ ktp                : chr NA
 $ address            : chr "123 Anywhere St., Any City"
 $ hp                 : chr "+62**********"
 $ gender             : chr "male"
 $ dcp                : chr NA
 $ current_max        :integer64 0 
 $ current_min        :integer64 0 
 $ expect_max         :integer64 0 
 $ expect_min         :integer64 0 
 $ job_vacancy_id     :integer64 1655 
 $ user_job_vacancy_id:integer64 116 
 $ Calculated YoE     : int 4
          Applied Date User ID            Fullname Job Role ID
1  2024-12-23 10:26:27      99                jang           1
2  2024-12-23 09:18:17      94               fajar           1
3  2024-12-23 02:21:15      84           tigaratus           2
4  2024-12-24 01:20:25      90     tiga ratus lima           2
5  2024-12-18 04:57:29     103     Putri Rosalinda           2
6  2024-12-22 07:20:48      86      tigaratus satu           3
7  2024-12-23 01:31:08     100       Estelle Darcy          87
8  2024-12-24 02:53:36     105         Le Minerale          87
9  2024-12-23 08:16:11      89            Jane Doe          87
10 2024-12-23 07:17:13      88       Estelle Darcy          87
11 2024-12-30 03:49:28     108       BENJAMIN SHAH         643
12 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         774
13 2024-12-18 04:57:29     103     Putri Rosalinda         780
14 2024-12-18 04:57:29     103     Putri Rosalinda         780
15 2024-12-21 06:29:13      76    Rachelle Beaudry         850
16 2024-12-23 01:31:08     100       Estelle Darcy         851
17 2024-12-21 06:29:13      76    Rachelle Beaudry         851
18 2024-12-23 07:17:13      88       Estelle Darcy         851
19 2024-12-24 02:53:36     105         Le Minerale         851
20 2024-12-23 08:16:11      89            Jane Doe         851
21 2024-12-21 06:29:13      76    Rachelle Beaudry         852
22 2024-12-21 06:29:13      76    Rachelle Beaudry         852
23 2024-12-21 06:29:13      76    Rachelle Beaudry         852
24 2024-12-21 06:29:13      76    Rachelle Beaudry         853
25 2024-12-21 06:29:13      76    Rachelle Beaudry         853
26 2024-12-21 06:29:13      76    Rachelle Beaudry         853
27 2024-12-21 06:29:13      76    Rachelle Beaudry         854
28 2024-12-21 06:29:13      76    Rachelle Beaudry         854
29 2024-12-21 06:29:13      76    Rachelle Beaudry         854
30 2024-12-21 06:29:13      76    Rachelle Beaudry         855
31 2024-12-21 06:29:13      76    Rachelle Beaudry         856
32 2024-12-21 06:29:13      76    Rachelle Beaudry         857
33 2024-12-23 08:16:11      89            Jane Doe         858
34 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
35 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
36 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         860
37 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         861
38 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         862
39 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         863
40 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         864
41 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         865
42 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         866
43 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         867
44 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         868
45 2024-12-18 04:57:29     103     Putri Rosalinda         869
46 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
47 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
48 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         872
49 2024-12-30 03:49:28     108       BENJAMIN SHAH         875
50 2024-12-30 03:49:28     108       BENJAMIN SHAH         876
                                                                   Job Role
1                                                              data science
2                                                              data science
3                                                         digital marketing
4                                                         digital marketing
5                                                         digital marketing
6                                                             fraud analyst
7                                                               ux designer
8                                                               ux designer
9                                                               ux designer
10                                                              ux designer
11                                                          system engineer
12                                                                 job name
13                                                         junior recruiter
14                                                         junior recruiter
15                                                              ux engineer
16                                                       system ux engineer
17                                                       system ux engineer
18                                                       system ux engineer
19                                                       system ux engineer
20                                                       system ux engineer
21                                                        junior accountant
22                                                        junior accountant
23                                                        junior accountant
24                                                               accountant
25                                                               accountant
26                                                               accountant
27                                                     accounting executive
28                                                     accounting executive
29                                                     accounting executive
30                                                    engineering executive
31                                                        graduate engineer
32                                                         project engineer
33                                                          instrument tech
34                                            scorer for psychological test
35                                            scorer for psychological test
36                                                                 assessor
37                                 talent acquisition & document specialist
38                                                              interviewer
39                                                      freelance recruiter
40 consultant & project coordinator for recruitment, selection & assessment
41                                                    freelance facilitator
42                                                freelance project manager
43                                freelance trainer assistant (facilitator)
44                                                   recruitment specialist
45                                                       people recruitment
46                                         consultant & project coordinator
47                                         consultant & project coordinator
48                                              analyst & project assistant
49                                                    mechatronics engineer
50                                                  junior project engineer
            industry                                    company_name work_type
1         Technology                                         rakamin full_time
2         Technology                                         Rakamin full_time
3         Technology                                          Lalala full_time
4  Digital Marketing                                            asdd full_time
5         Technology                                          kucing full_time
6            Finance                                          asdsad full_time
7               <NA>                                        Morcelle full_time
8               <NA>                                        Morcelle full_time
9               <NA>                                        Morcelle full_time
10              <NA>                                        Morcelle full_time
11              <NA>                              Arrowai Industries full_time
12              <NA>                     Analyst & Project Assistant full_time
13              <NA>            PT Go Online Destinations (Pegipegi) full_time
14              <NA>            PT Go Online Destinations (Pegipegi) full_time
15              <NA>                                        Morcelle full_time
16              <NA>                             XarrowAI Industries full_time
17              <NA>                             XarrowAI Industries full_time
18              <NA>                             XarrowAI Industries full_time
19              <NA>                             XarrowAI Industries full_time
20              <NA>                             XarrowAI Industries full_time
21              <NA>                              Arowwai Industries full_time
22              <NA>                              Arowwai Industries full_time
23              <NA>                              Arowwai Industries full_time
24              <NA>                                    Salford & Co full_time
25              <NA>                                    Salford & Co full_time
26              <NA>                                    Salford & Co full_time
27              <NA>                            Borcelle Corporation full_time
28              <NA>                            Borcelle Corporation full_time
29              <NA>                            Borcelle Corporation full_time
30              <NA>                           Borcelle Technologies full_time
31              <NA>                              Arowwai Industries full_time
32              <NA>                                    Salford & Co full_time
33              <NA>                                Morcelle Program full_time
34              <NA>                             ASTRA INTERNATIONAL freelance
35              <NA>                            ASTRA DAIHATSU MOTOR freelance
36              <NA>                         PT ENIGMA PUTRA MANDIRI freelance
37              <NA>                PT TENRIAWARU ELIT INTERNASIONAL full_time
38              <NA>                                        STAFFINC freelance
39              <NA>                         PT KILAT JAYA INDONESIA freelance
40              <NA>                     PT TJITRA SELARAS SAMPOERNO full_time
41              <NA> PT INSAN BARU INDONESIA (THE NEW YOU INSTITUTE) freelance
42              <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) freelance
43              <NA>      PT SAMALA SERASI UNGGUL (RUMAH SIAP KERJA) freelance
44              <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) full_time
45              <NA>                                  Social Connect full_time
46              <NA>                             Tjitra & associates full_time
47              <NA>                             Tjitra & associates full_time
48              <NA>                             Tjitra & associates full_time
49              <NA>                           Borcelle Technologies full_time
50              <NA>                      Salford & Co Manufacturing full_time
               ends_at           starts_at       MoE
1                 <NA> 2021-02-06 01:13:03 47.436969
2                 <NA> 2021-02-05 04:51:39 47.465242
3                 <NA> 2023-04-05 09:46:31 21.158416
4  2023-02-24 01:18:23 2022-01-24 01:18:19 13.200002
5  2024-09-18 04:51:45 2024-02-18 04:51:41  7.100001
6                 <NA> 2022-01-22 07:17:58 35.761855
7                 <NA> 2022-12-31 17:00:00 24.315049
8                 <NA> 2022-12-31 17:00:00 24.315049
9                 <NA> 2022-12-31 17:00:00 24.315049
10                <NA> 2022-12-31 17:00:00 24.315049
11 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
12 2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
13 2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
14 2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
15                <NA> 2022-12-31 17:00:00 24.315049
16 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
17 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
18 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
19 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
20 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
21 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
22 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
23 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
24 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
25 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
26 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
27                <NA> 2022-12-31 17:00:00 24.315049
28                <NA> 2022-12-31 17:00:00 24.315049
29                <NA> 2022-12-31 17:00:00 24.315049
30                <NA> 2022-12-31 17:00:00 24.315049
31 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
32 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
33                <NA> 2023-12-31 17:00:00 12.148382
34                <NA> 2019-04-30 17:00:00 69.015049
35                <NA> 2019-12-31 17:00:00 60.848382
36 2022-11-30 17:00:00 2022-10-31 17:00:00  1.000000
37 2024-09-30 17:00:00 2023-01-31 17:00:00 20.266667
38 2021-11-30 17:00:00 2021-10-31 17:00:00  1.000000
39 2023-10-31 17:00:00 2023-09-30 17:00:00  1.033333
40 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
41 2024-01-31 17:00:00 2023-11-30 17:00:00  2.066667
42 2024-09-30 17:00:00 2024-04-30 17:00:00  5.100000
43                <NA> 2024-04-30 17:00:00  8.115049
44                <NA> 2024-09-30 17:00:00  3.015049
45 2022-11-30 17:00:00 2020-07-31 17:00:00 28.400000
46 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
47 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
48 2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
49                <NA> 2022-12-31 17:00:00 24.315049
50 2020-12-31 17:00:00 2020-02-29 17:00:00 10.200000
'data.frame':	50 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 10:26:27" "2024-12-23 09:18:17" ...
 $ User ID     :integer64 99 94 84 90 103 86 100 105 ... 
 $ Fullname    : chr  "jang" "fajar" "tigaratus" "tiga ratus lima" ...
 $ Job Role ID :integer64 1 1 2 2 2 3 87 87 ... 
 $ Job Role    : chr  "data science" "data science" "digital marketing" "digital marketing" ...
 $ industry    : chr  "Technology" "Technology" "Technology" "Digital Marketing" ...
 $ company_name: chr  "rakamin" "Rakamin" "Lalala" "asdd" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: NA NA ...
 $ starts_at   : POSIXct, format: "2021-02-06 01:13:03" "2021-02-05 04:51:39" ...
 $ MoE         : num  47.4 47.5 21.2 13.2 7.1 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
[1] "TOEFL Score Data"
[1] "df_toefl is NULL or empty. Skipping the join."
<simpleError in `$<-.data.frame`(`*tmp*`, toefl_level, value = list()): replacement has 0 rows, data has 1>
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 13
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "IT Infrastructure"
$ id                               <int64> 13
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Software"
$ `Job Role Name`                  <chr> "it infrastructure"
$ `Tools and Competencies Mastery` <chr> "Amazon Web Services (AWS)"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> "Technology"
$ Domicile                         <chr> "Kota Jakarta Selatan - DKI Jakarta"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> "<p>Able to code</p>"
$ max_age                          <int> NA
$ Major                            <chr> "Teknik Informatika"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[[1]]
[1] "Technology"

[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Teknik Informatika"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE


$Domisili$MrC_Domisili
$Domisili$MrC_Domisili[[1]]
$Domisili$MrC_Domisili[[1]]$name
[1] "Kota Jakarta Selatan - DKI Jakarta"

$Domisili$MrC_Domisili[[1]]$value
[1] 0

$Domisili$MrC_Domisili[[1]]$order_level
[1] 0



$Domisili$MrU_Domisili
$Domisili$MrU_Domisili$weight
[1] 0

$Domisili$MrU_Domisili$required
[1] FALSE



$Industry
$Industry$MrC_Industry
$Industry$MrC_Industry[[1]]
$Industry$MrC_Industry[[1]]$name
[1] "Technology"

$Industry$MrC_Industry[[1]]$value
[1] 0

$Industry$MrC_Industry[[1]]$order_level
[1] 0



$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] TRUE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0

$Education$MrC_Education[[2]]$disabled
[1] TRUE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0

$Education$MrC_Education[[3]]$disabled
[1] TRUE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0

$Education$MrC_Education[[4]]$disabled
[1] TRUE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0

$Education$MrC_Education[[5]]$disabled
[1] TRUE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3

$Education$MrU_Education$required
[1] TRUE

$Education$MrU_Education$minimum_config
$Education$MrU_Education$minimum_config$key
[1] "name"

$Education$MrU_Education$minimum_config$value
[1] "S1"




$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "Amazon Web Services (AWS)"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2

$Working_Experience$MrU_WE$required
[1] TRUE



[1] "JSON Check"
List of 8
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Major             :List of 2
  ..$ MrC_Major:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Teknik Informatika"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Major:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Domisili          :List of 3
  ..$ MrU_Domisil :List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
  ..$ MrC_Domisili:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Kota Jakarta Selatan - DKI Jakarta"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Domisili:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi FALSE
 $ Industry          :List of 2
  ..$ MrC_Industry:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Technology"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Industry:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 4
  .. .. ..$ level      : chr "SMA/SMK"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 3
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D4"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 5
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 6
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 7
  ..$ MrU_Education:List of 3
  .. ..$ weight        : int 3
  .. ..$ required      : logi TRUE
  .. ..$ minimum_config:List of 2
  .. .. ..$ key  : chr "name"
  .. .. ..$ value: chr "S1"
 $ Skillntools       :List of 2
  ..$ MrC_Skillntools:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Amazon Web Services (AWS)"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Skillntools:List of 2
  .. ..$ weight  : int 3
  .. ..$ required: logi TRUE
 $ Expected_Salary   :List of 2
  ..$ MrC_ES:List of 3
  .. ..$ :List of 2
  .. .. ..$ name : chr "< 1.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "1.0 - 2.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "> 2.0"
  .. .. ..$ value: int 0
  ..$ MrU_ES:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "0-2 tahun"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "2-5 tahun"
  .. .. ..$ value      : int 1
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "7-12 tahun"
  .. .. ..$ value      : int 2
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "10-15 tahun"
  .. .. ..$ value      : int 3
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 3
  ..$ MrU_WE:List of 2
  .. ..$ weight  : int 2
  .. ..$ required: logi TRUE
NULL
[1] "JSON Check Class"
[1] "integer"
Info: All values are zero
[1] "Run the tests"

Test 1:
Success: FALSE 
Value: NA 
Error: None 
Message: All values are zero 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] NA
[1] NA
[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date              Role                      Email User ID
1 2024-12-19 10:14:53 IT Infrastructure   <EMAIL>      36
2 2024-12-27 13:23:55 IT Infrastructure    <EMAIL>      43
3 2024-12-27 13:24:14 IT Infrastructure    <EMAIL>      44
4 2024-12-27 13:24:56 IT Infrastructure    <EMAIL>      47
5 2024-12-27 13:25:16 IT Infrastructure    <EMAIL>      48
6 2024-12-27 13:24:33 IT Infrastructure    <EMAIL>      46
7 2024-12-19 10:12:38 <NAME_EMAIL>      63
8 2024-12-19 10:32:35 IT Infrastructure    <EMAIL>      62
           Fullname                          Kota - Kab Provinces Degree   GPA
1          John Doe                       New York, USA      <NA>     S1   3.5
2       Luna Thomas           San Francisco, California      <NA>     S1   4.0
3       Nadia Omara              Anywhere St., Any City      <NA>     S1  99.0
4     BENJAMIN SHAH Kabupaten Banggai - Sulawesi Tengah      <NA>     S1  98.0
5      ARIF BUDIMAN         Kalimantan Utara, Indonesia      <NA>     S1  99.0
6 Sebastian Bennett          123 Anywhere St., Any City      <NA>     S1 100.0
7           student                               Medan      <NA>     S1   0.0
8  Rachelle Beaudry Kabupaten Banggai - Sulawesi Tengah      <NA>     S2   0.0
                 Major                          Institution Degree 2nd GPA 2nd
1     Computer Science             University Of Technology       <NA>      NA
2         Administrasi             Universitas Cenderawasih       <NA>      NA
3              Ekonomi               Universitas Hasanuddin       <NA>      NA
4  Manajemen Pemasaran            Universitas Sam Ratulangi       <NA>      NA
5                Hukum           Universitas Borneo Tarakan       <NA>      NA
6        Ilmu Komputer               Universitas Mulawarman       <NA>      NA
7  Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
8 Financial Management University Of Finance And Management         S1       0
   Major 2nd Institution 2nd                 Job Role      YoE Experience
1       <NA>            <NA>        software engineer 2.502673          0
2       <NA>            <NA>          product manager 8.669797          0
3       <NA>            <NA>              ux designer 1.998564          0
4       <NA>            <NA>    mechatronics engineer 4.667057          0
5       <NA>            <NA>              ux engineer 1.998564          0
6       <NA>            <NA> junior digital marketing 5.001303          0
7       <NA>            <NA>                     <NA> 0.000000          0
8 Accounting    City College     accounting executive 4.669797          0
                                                                                                                                                                                                    Skill & Tools
1                                                                                                                                                                         {CSS,HTML,java,Javascript,MySQL,Spring}
2                               {"Cross-functional Team Leadership","Customer Feedback Analysis","Financial Data Integration","Market analysis","Payment Processing","Product Strategy","User Experience Design"}
3                                                                                                                                                     {Automation,"Process Improvement","Skill name","UX Design"}
4                                                                                                    {Automation,"Control Systems","Feasibility Studies",Mechatronics,"Project Management",Robotics,"Skill name"}
5                                                                                                                                                         {"Automation Systems","Project Management","UX Design"}
6                                                                                                                                                                                                   {"Tool name"}
7                                                                                                                                                                                                            <NA>
8 {"Audit Compliance","Cost Control","Cross-functional Collaboration","Financial Forecasting","Financial Reporting","Financial Statements","Internal Audits","Month-End Close","Process Automation","Skill name"}
                                                                                                                                        CV Upload
1               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be78-9dbf1765f6d2.pdf
2                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf
3                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-9cc01668-1617-4499-96bf-a16f247d2162.pdf
4                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_4-69a5af64-5a61-45da-a4e1-20493492218b.pdf
5                            https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-4a9c1324-6c58-46e7-858f-d6969baed5ef.pdf
6                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_3-3667fdc1-4e01-4bba-bd0c-138aa1379645.pdf
7 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-28ac1ae1-5999-4bd4-89ad-071ba03eb36b.pdf
8                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_7-6c390dc6-50ac-4ea3-bb35-02399d723844.pdf
  Linkedin Link          Portfolio Link Instagram Link Twitter Link
1          <NA>                    <NA>           <NA>         <NA>
2               https://www.enhancv.com           <NA>         <NA>
3          <NA>                                   <NA>         <NA>
4          <NA>                                   <NA>         <NA>
5                                                 <NA>         <NA>
6          <NA>                    <NA>           <NA>         <NA>
7          <NA>                    <NA>           <NA>         <NA>
8          <NA>                                   <NA>         <NA>
                    State Status Availability                 dob  pob  ktp
1 assessment_psikotes_1_1        open_to_work 2000-12-06 02:55:13 <NA> <NA>
2                 applied        open_to_work 2000-12-04 03:13:38 <NA> <NA>
3                rejected        open_to_work 2000-12-01 03:48:25 <NA> <NA>
4                rejected        open_to_work 2002-12-09 05:55:40 <NA> <NA>
5                   hired        open_to_work 2001-12-06 05:40:22 <NA> <NA>
6                   hired        open_to_work 2000-12-01 05:14:52 <NA> <NA>
7 assessment_psikotes_1_1        open_to_work 2024-12-01 10:12:13 <NA> <NA>
8                rejected        open_to_work 2001-12-05 08:26:12 <NA> <NA>
                      address              hp gender  dcp current_max
1               New York, USA     +********** female <NA>           0
2   San Francisco, California +**********1111 female <NA>           0
3      Anywhere St., Any City   +**********11 female <NA>           0
4  123 Anywhere St., Any City   +62**********   male <NA>           0
5 Kalimantan Utara, Indonesia +62184147817481   male <NA>           0
6  123 Anywhere St., Any City     +**********   male <NA>           0
7                       Medan    +62618218532 female <NA>           0
8  123 Anywhere St., Any City +6288********** female <NA>           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1        <NA>          2          1             13                  28
2        <NA>          2          1             13                  69
3        <NA>          2          1             13                  70
4        <NA>          2          1             13                  72
5        <NA>          2          1             13                  73
6        <NA>          2          1             13                  71
7        <NA>          2          1             13                  27
8        <NA>          2          1             13                  29
  Calculated YoE
1              2
2              8
3              1
4              4
5              1
6              4
7              0
8              4
'data.frame':	8 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-19 10:14:53" "2024-12-27 13:23:55" ...
 $ Role               : chr  "IT Infrastructure" "IT Infrastructure" "IT Infrastructure" "IT Infrastructure" ...
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>" ...
 $ User ID            :integer64 36 43 44 47 48 46 63 62 
 $ Fullname           : chr  "John Doe" "Luna Thomas" "Nadia Omara" "BENJAMIN SHAH" ...
 $ Kota - Kab         : chr  "New York, USA" "San Francisco, California" "Anywhere St., Any City" "Kabupaten Banggai - Sulawesi Tengah" ...
 $ Provinces          : chr  NA NA NA NA ...
 $ Degree             : chr  "S1" "S1" "S1" "S1" ...
 $ GPA                : num  3.5 4 99 98 99 100 0 0
 $ Major              : chr  "Computer Science" "Administrasi" "Ekonomi" "Manajemen Pemasaran" ...
 $ Institution        : chr  "University Of Technology" "Universitas Cenderawasih" "Universitas Hasanuddin" "Universitas Sam Ratulangi" ...
 $ Degree 2nd         : chr  NA NA NA NA ...
 $ GPA 2nd            : num  NA NA NA NA NA NA NA 0
 $ Major 2nd          : chr  NA NA NA NA ...
 $ Institution 2nd    : chr  NA NA NA NA ...
 $ Job Role           : chr  "software engineer" "product manager" "ux designer" "mechatronics engineer" ...
 $ YoE                : num  2.5 8.67 2 4.67 2 ...
 $ Experience         : int  0 0 0 0 0 0 0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{CSS,HTML,java,Javascript,MySQL,Spring}" "{\"Cross-functional Team Leadership\",\"Customer Feedback Analysis\",\"Financial Data Integration\",\"Market an"| __truncated__ "{Automation,\"Process Improvement\",\"Skill name\",\"UX Design\"}" "{Automation,\"Control Systems\",\"Feasibility Studies\",Mechatronics,\"Project Management\",Robotics,\"Skill name\"}" ...
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be7"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-9cc01668-1617-4499-96bf-a16f247d2162.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_4-69a5af64-5a61-45da-a4e1-20493492218b.pdf" ...
 $ Linkedin Link      : chr  NA "" NA NA ...
 $ Portfolio Link     : chr  NA "https://www.enhancv.com" "" "" ...
 $ Instagram Link     : chr  NA NA NA NA ...
 $ Twitter Link       : chr  NA NA NA NA ...
 $ State              : chr  "assessment_psikotes_1_1" "applied" "rejected" "rejected" ...
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work" "open_to_work" ...
 $ dob                : POSIXct, format: "2000-12-06 02:55:13" "2000-12-04 03:13:38" ...
 $ pob                : chr  NA NA NA NA ...
 $ ktp                : chr  NA NA NA NA ...
 $ address            : chr  "New York, USA" "San Francisco, California" "Anywhere St., Any City" "123 Anywhere St., Any City" ...
 $ hp                 : chr  "+**********" "+**********1111" "+**********11" "+62**********" ...
 $ gender             : chr  "female" "female" "female" "male" ...
 $ dcp                : chr  NA NA NA NA ...
 $ current_max        :integer64 0 0 0 0 0 0 0 0 
 $ current_min        :integer64 NA NA NA NA NA NA NA NA 
 $ expect_max         :integer64 2 2 2 2 2 2 2 2 
 $ expect_min         :integer64 1 1 1 1 1 1 1 1 
 $ job_vacancy_id     :integer64 13 13 13 13 13 13 13 13 
 $ user_job_vacancy_id:integer64 28 69 70 72 73 71 27 29 
 $ Calculated YoE     : int  2 8 1 4 1 4 0 4
          Applied Date User ID          Fullname Job Role ID
1  2024-12-19 10:14:53      36          John Doe          17
2  2024-12-27 13:23:55      43       Luna Thomas         205
3  2024-12-27 13:23:55      43       Luna Thomas         865
4  2024-12-27 13:23:55      43       Luna Thomas          78
5  2024-12-27 13:24:14      44       Nadia Omara          87
6  2024-12-27 13:24:56      47     BENJAMIN SHAH         868
7  2024-12-27 13:24:56      47     BENJAMIN SHAH         869
8  2024-12-27 13:24:56      47     BENJAMIN SHAH         643
9  2024-12-27 13:25:16      48      ARIF BUDIMAN         860
10 2024-12-27 13:24:33      46 Sebastian Bennett         866
11 2024-12-19 10:32:35      62  Rachelle Beaudry         872
12 2024-12-19 10:32:35      62  Rachelle Beaudry         873
13 2024-12-19 10:32:35      62  Rachelle Beaudry         874
                    Job Role industry               company_name work_type
1          software engineer     <NA>        Tech Solutions Inc. full_time
2  associate product manager     <NA>                     Stripe full_time
3     senior product manager     <NA>                      Plaid full_time
4            product manager     <NA>                      Plaid full_time
5                ux designer     <NA>                   Morcelle full_time
6      mechatronics engineer     <NA>      Borcelle Technologies full_time
7    junior project engineer     <NA> Salford & Co Manufacturing full_time
8            system engineer     <NA>         Arrowai Industries full_time
9                ux engineer     <NA>                   Morcelle full_time
10  junior digital marketing     <NA>            Ingoude Company full_time
11         junior accountant     <NA>         Arowwai Industries full_time
12                accountant     <NA>               Salford & Co full_time
13      accounting executive     <NA>       Borcelle Corporation full_time
               ends_at           starts_at      MoE
1                 <NA> 2022-06-30 17:00:00 30.44919
2  2018-04-30 17:00:00 2016-02-29 17:00:00 26.36667
3  2020-11-30 17:00:00 2018-05-31 17:00:00 30.46667
4                 <NA> 2020-12-31 17:00:00 48.64919
5                 <NA> 2022-12-31 17:00:00 24.31586
6                 <NA> 2022-12-31 17:00:00 24.31586
7  2020-12-31 17:00:00 2020-02-29 17:00:00 10.20000
8  2022-11-30 17:00:00 2021-01-31 17:00:00 22.26667
9                 <NA> 2022-12-31 17:00:00 24.31586
10                <NA> 2019-12-31 17:00:00 60.84919
11 2020-12-31 17:00:00 2020-01-31 17:00:00 11.16667
12 2022-11-30 17:00:00 2021-02-28 17:00:00 21.33333
13                <NA> 2022-12-31 17:00:00 24.31586
'data.frame':	13 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-19 10:14:53" "2024-12-27 13:23:55" ...
 $ User ID     :integer64 36 43 43 43 44 47 47 47 ... 
 $ Fullname    : chr  "John Doe" "Luna Thomas" "Luna Thomas" "Luna Thomas" ...
 $ Job Role ID :integer64 17 205 865 78 87 *********** ... 
 $ Job Role    : chr  "software engineer" "associate product manager" "senior product manager" "product manager" ...
 $ industry    : chr  NA NA NA NA ...
 $ company_name: chr  "Tech Solutions Inc." "Stripe" "Plaid" "Plaid" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: NA "2018-04-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2022-06-30 17:00:00" "2016-02-29 17:00:00" ...
 $ MoE         : num  30.4 26.4 30.5 48.6 24.3 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning: There was 1 warning in `mutate()`.
ℹ In argument: `dob = dmy_hm(dob)`.
Caused by warning:
! All formats failed to parse. No formats found.
[1] "TOEFL Score Data"
[1] "df_toefl is NULL or empty. Skipping the join."
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
<simpleError in `$<-.data.frame`(`*tmp*`, toefl_level, value = list()): replacement has 0 rows, data has 8>
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 14
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "IT Infrastructure (Flexible flow, mu…
$ id                               <int64> 14
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Software"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "Amazon Web Services (AWS)"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> "Technology"
$ Domicile                         <chr> "Kota Jakarta Selatan - DKI Jakarta"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> "<p>-</p>"
$ max_age                          <int> NA
$ Major                            <chr> "Teknik Informatika"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[[1]]
[1] "Technology"

[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Teknik Informatika"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE


$Domisili$MrC_Domisili
$Domisili$MrC_Domisili[[1]]
$Domisili$MrC_Domisili[[1]]$name
[1] "Kota Jakarta Selatan - DKI Jakarta"

$Domisili$MrC_Domisili[[1]]$value
[1] 0

$Domisili$MrC_Domisili[[1]]$order_level
[1] 0



$Domisili$MrU_Domisili
$Domisili$MrU_Domisili$weight
[1] 0

$Domisili$MrU_Domisili$required
[1] FALSE



$Industry
$Industry$MrC_Industry
$Industry$MrC_Industry[[1]]
$Industry$MrC_Industry[[1]]$name
[1] "Technology"

$Industry$MrC_Industry[[1]]$value
[1] 0

$Industry$MrC_Industry[[1]]$order_level
[1] 0



$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] TRUE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0

$Education$MrC_Education[[2]]$disabled
[1] TRUE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0

$Education$MrC_Education[[3]]$disabled
[1] TRUE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0

$Education$MrC_Education[[4]]$disabled
[1] TRUE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0

$Education$MrC_Education[[5]]$disabled
[1] TRUE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3

$Education$MrU_Education$required
[1] TRUE

$Education$MrU_Education$minimum_config
$Education$MrU_Education$minimum_config$key
[1] "name"

$Education$MrU_Education$minimum_config$value
[1] "S1"




$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "Amazon Web Services (AWS)"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2

$Working_Experience$MrU_WE$required
[1] TRUE



[1] "JSON Check"
List of 8
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Major             :List of 2
  ..$ MrC_Major:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Teknik Informatika"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Major:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Domisili          :List of 3
  ..$ MrU_Domisil :List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
  ..$ MrC_Domisili:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Kota Jakarta Selatan - DKI Jakarta"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Domisili:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi FALSE
 $ Industry          :List of 2
  ..$ MrC_Industry:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Technology"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Industry:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 4
  .. .. ..$ level      : chr "SMA/SMK"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 3
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D4"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi TRUE
  .. .. ..$ order_level: int 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 5
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 6
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 7
  ..$ MrU_Education:List of 3
  .. ..$ weight        : int 3
  .. ..$ required      : logi TRUE
  .. ..$ minimum_config:List of 2
  .. .. ..$ key  : chr "name"
  .. .. ..$ value: chr "S1"
 $ Skillntools       :List of 2
  ..$ MrC_Skillntools:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Amazon Web Services (AWS)"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Skillntools:List of 2
  .. ..$ weight  : int 3
  .. ..$ required: logi TRUE
 $ Expected_Salary   :List of 2
  ..$ MrC_ES:List of 3
  .. ..$ :List of 2
  .. .. ..$ name : chr "< 1.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "1.0 - 2.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "> 2.0"
  .. .. ..$ value: int 0
  ..$ MrU_ES:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "0-2 tahun"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "2-5 tahun"
  .. .. ..$ value      : int 1
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "7-12 tahun"
  .. .. ..$ value      : int 2
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "10-15 tahun"
  .. .. ..$ value      : int 3
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 3
  ..$ MrU_WE:List of 2
  .. ..$ weight  : int 2
  .. ..$ required: logi TRUE
NULL
[1] "JSON Check Class"
[1] "integer"
Info: All values are zero
[1] "Run the tests"

Test 1:
Success: FALSE 
Value: NA 
Error: None 
Message: All values are zero 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] NA
[1] NA
[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                                                   Role
1 2024-12-19 10:44:54 IT Infrastructure (Flexible flow, multiple assessment)
2 2024-12-19 11:06:24 IT Infrastructure (Flexible flow, multiple assessment)
3 2024-12-19 15:51:38 IT Infrastructure (Flexible flow, multiple assessment)
                       Email User ID         Fullname
1    <EMAIL>      62 Rachelle Beaudry
2 <EMAIL>      64          ANDI M.
3 <EMAIL>      65        BUDIYANTO
                             Kota - Kab Provinces Degree  GPA
1   Kabupaten Banggai - Sulawesi Tengah      <NA>     S2 0.00
2                    Pati - Jawa Tengah      <NA>     S1 4.00
3 Kabupaten Banjar - Kalimantan Selatan      <NA>     S1 3.55
                              Major                          Institution
1              Financial Management University Of Finance And Management
2               Process Engineering               Engineering University
3 Agribisnis Perikanan dan Kelautan              Universitas Gadjah Mada
  Degree 2nd GPA 2nd  Major 2nd Institution 2nd             Job Role      YoE
1         S1       0 Accounting    City College accounting executive 4.669806
2       <NA>      NA       <NA>            <NA>          ux designer 3.828710
3       <NA>      NA       <NA>            <NA>          ux designer 3.828710
  Experience
1          0
2          0
3          0
                                                                                                                                                                                                    Skill & Tools
1 {"Audit Compliance","Cost Control","Cross-functional Collaboration","Financial Forecasting","Financial Reporting","Financial Statements","Internal Audits","Month-End Close","Process Automation","Skill name"}
2                                                                                                                                         {"Project Management","Skill name","System Design",Testing,"UX Design"}
3                                                                    {"Automation Systems","Project Management","Robotic Control System","Skill name","Technical Expertise","Testing and Validation","UX Design"}
                                                                                                                   CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_7-6c390dc6-50ac-4ea3-bb35-02399d723844.pdf
2       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-457df4ce-a165-4c40-ab0e-3ddbdcf23543.pdf
3       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-1ac87e96-38ac-41bb-a4b4-432befa58317.pdf
  Linkedin Link Portfolio Link Instagram Link Twitter Link
1          <NA>                          <NA>         <NA>
2                                        <NA>         <NA>
3                                        <NA>         <NA>
                    State Status Availability                 dob  pob  ktp
1 assessment_psikotes_1_1        open_to_work 2001-12-05 08:26:12 <NA> <NA>
2         medical_checkup        open_to_work 1982-12-30 17:00:00 <NA> <NA>
3 assessment_psikotes_1_1        open_to_work 2000-12-04 15:51:17 <NA> <NA>
                      address              hp gender  dcp current_max
1  123 Anywhere St., Any City +6288********** female <NA>           0
2          Pati - Jawa Tengah   +629183681631   male <NA>           0
3 Kalimantan Utara, Indonesia    +62813132131   male <NA>           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1        <NA>          2          1             14                  30
2        <NA>          2          1             14                  31
3        <NA>          2          1             14                  34
  Calculated YoE
1              4
2              3
3              3
'data.frame':	3 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-19 10:44:54" "2024-12-19 11:06:24" ...
 $ Role               : chr  "IT Infrastructure (Flexible flow, multiple assessment)" "IT Infrastructure (Flexible flow, multiple assessment)" "IT Infrastructure (Flexible flow, multiple assessment)"
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 62 64 65 
 $ Fullname           : chr  "Rachelle Beaudry" "ANDI M." "BUDIYANTO"
 $ Kota - Kab         : chr  "Kabupaten Banggai - Sulawesi Tengah" "Pati - Jawa Tengah" "Kabupaten Banjar - Kalimantan Selatan"
 $ Provinces          : chr  NA NA NA
 $ Degree             : chr  "S2" "S1" "S1"
 $ GPA                : num  0 4 3.55
 $ Major              : chr  "Financial Management" "Process Engineering" "Agribisnis Perikanan dan Kelautan"
 $ Institution        : chr  "University Of Finance And Management" "Engineering University" "Universitas Gadjah Mada"
 $ Degree 2nd         : chr  "S1" NA NA
 $ GPA 2nd            : num  0 NA NA
 $ Major 2nd          : chr  "Accounting" NA NA
 $ Institution 2nd    : chr  "City College" NA NA
 $ Job Role           : chr  "accounting executive" "ux designer" "ux designer"
 $ YoE                : num  4.67 3.83 3.83
 $ Experience         : int  0 0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Audit Compliance\",\"Cost Control\",\"Cross-functional Collaboration\",\"Financial Forecasting\",\"Financial"| __truncated__ "{\"Project Management\",\"Skill name\",\"System Design\",Testing,\"UX Design\"}" "{\"Automation Systems\",\"Project Management\",\"Robotic Control System\",\"Skill name\",\"Technical Expertise\"| __truncated__
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_7-6c390dc6-50ac-4ea3-bb35-02399d723844.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-457df4ce-a165-4c40-ab0e-3ddbdcf23543.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-1ac87e96-38ac-41bb-a4b4-432befa58317.pdf"
 $ Linkedin Link      : chr  NA "" ""
 $ Portfolio Link     : chr  "" "" ""
 $ Instagram Link     : chr  NA NA NA
 $ Twitter Link       : chr  NA NA NA
 $ State              : chr  "assessment_psikotes_1_1" "medical_checkup" "assessment_psikotes_1_1"
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: "2001-12-05 08:26:12" "1982-12-30 17:00:00" ...
 $ pob                : chr  NA NA NA
 $ ktp                : chr  NA NA NA
 $ address            : chr  "123 Anywhere St., Any City" "Pati - Jawa Tengah" "Kalimantan Utara, Indonesia"
 $ hp                 : chr  "+6288**********" "+629183681631" "+62813132131"
 $ gender             : chr  "female" "male" "male"
 $ dcp                : chr  NA NA NA
 $ current_max        :integer64 0 0 0 
 $ current_min        :integer64 NA NA NA 
 $ expect_max         :integer64 2 2 2 
 $ expect_min         :integer64 1 1 1 
 $ job_vacancy_id     :integer64 14 14 14 
 $ user_job_vacancy_id:integer64 30 31 34 
 $ Calculated YoE     : int  4 3 3
         Applied Date User ID         Fullname Job Role ID             Job Role
1 2024-12-19 10:44:54      62 Rachelle Beaudry         872    junior accountant
2 2024-12-19 10:44:54      62 Rachelle Beaudry         873           accountant
3 2024-12-19 10:44:54      62 Rachelle Beaudry         874 accounting executive
4 2024-12-19 11:06:24      64          ANDI M.          87          ux designer
5 2024-12-19 11:06:24      64          ANDI M.         859   system ux engineer
6 2024-12-19 15:51:38      65        BUDIYANTO          87          ux designer
7 2024-12-19 15:51:38      65        BUDIYANTO         859   system ux engineer
  industry         company_name work_type             ends_at
1     <NA>   Arowwai Industries full_time 2020-12-31 17:00:00
2     <NA>         Salford & Co full_time 2022-11-30 17:00:00
3     <NA> Borcelle Corporation full_time                <NA>
4     <NA>             Morcelle full_time                <NA>
5     <NA>  XarrowAI Industries full_time 2022-11-30 17:00:00
6     <NA>             Morcelle full_time                <NA>
7     <NA>  XarrowAI Industries full_time 2022-11-30 17:00:00
            starts_at      MoE
1 2020-01-31 17:00:00 11.16667
2 2021-02-28 17:00:00 21.33333
3 2022-12-31 17:00:00 24.31597
4 2022-12-31 17:00:00 24.31597
5 2021-01-31 17:00:00 22.26667
6 2022-12-31 17:00:00 24.31597
7 2021-01-31 17:00:00 22.26667
'data.frame':	7 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-19 10:44:54" "2024-12-19 10:44:54" ...
 $ User ID     :integer64 62 62 62 64 64 65 65 
 $ Fullname    : chr  "Rachelle Beaudry" "Rachelle Beaudry" "Rachelle Beaudry" "ANDI M." ...
 $ Job Role ID :integer64 *********** 87 859 87 859 
 $ Job Role    : chr  "junior accountant" "accountant" "accounting executive" "ux designer" ...
 $ industry    : chr  NA NA NA NA ...
 $ company_name: chr  "Arowwai Industries" "Salford & Co" "Borcelle Corporation" "Morcelle" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2020-12-31 17:00:00" "2022-11-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2020-01-31 17:00:00" "2021-02-28 17:00:00" ...
 $ MoE         : num  11.2 21.3 24.3 24.3 22.3 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning: There was 1 warning in `mutate()`.
ℹ In argument: `dob = dmy_hm(dob)`.
Caused by warning:
! All formats failed to parse. No formats found.
[1] "TOEFL Score Data"
[1] "df_toefl is NULL or empty. Skipping the join."
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
<simpleError in `$<-.data.frame`(`*tmp*`, toefl_level, value = list()): replacement has 0 rows, data has 3>
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 8
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Supervisor Kantor Cabang di wilayah …
$ id                               <int64> 8
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Product"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "Collaborative Working, Reporting a…
$ `Education Level`                <chr> "D3"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "contract"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Administrasi Bisnis, Akuntansi, Ekon…
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$Age
$Age$MrC_Age
$Age$MrC_Age[[1]]
$Age$MrC_Age[[1]]$level
[1] "21 ≤ x < 24"

$Age$MrC_Age[[1]]$value
[1] 5


$Age$MrC_Age[[2]]
$Age$MrC_Age[[2]]$level
[1] "18 ≤ x < 21"

$Age$MrC_Age[[2]]$value
[1] 4


$Age$MrC_Age[[3]]
$Age$MrC_Age[[3]]$level
[1] "24 ≤ x < 27"

$Age$MrC_Age[[3]]$value
[1] 3


$Age$MrC_Age[[4]]
$Age$MrC_Age[[4]]$level
[1] "27 ≤ x < 30"

$Age$MrC_Age[[4]]$value
[1] 2


$Age$MrC_Age[[5]]
$Age$MrC_Age[[5]]$level
[1] "x = 30"

$Age$MrC_Age[[5]]$value
[1] 1



$Age$MrU_Age
$Age$MrU_Age$weight
[1] 0.4


$Age$baseline
[1] 30


$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 1.6



$TOEFL
$TOEFL$baseline
[1] 380

$TOEFL$MrC_TOEFL
$TOEFL$MrC_TOEFL[[1]]
$TOEFL$MrC_TOEFL[[1]]$level
[1] "621-677"

$TOEFL$MrC_TOEFL[[1]]$value
[1] 5


$TOEFL$MrC_TOEFL[[2]]
$TOEFL$MrC_TOEFL[[2]]$level
[1] "561-620"

$TOEFL$MrC_TOEFL[[2]]$value
[1] 4


$TOEFL$MrC_TOEFL[[3]]
$TOEFL$MrC_TOEFL[[3]]$level
[1] "441-560"

$TOEFL$MrC_TOEFL[[3]]$value
[1] 3


$TOEFL$MrC_TOEFL[[4]]
$TOEFL$MrC_TOEFL[[4]]$level
[1] "381-440"

$TOEFL$MrC_TOEFL[[4]]$value
[1] 2


$TOEFL$MrC_TOEFL[[5]]
$TOEFL$MrC_TOEFL[[5]]$level
[1] "≤ 380"

$TOEFL$MrC_TOEFL[[5]]$value
[1] 1



$TOEFL$MrU_TOEFL
$TOEFL$MrU_TOEFL$weight
[1] 0.6



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 1.4



[1] "JSON Check"
List of 9
 $ Age               :List of 3
  ..$ MrC_Age :List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "21 ≤ x < 24"
  .. .. ..$ value: int 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "18 ≤ x < 21"
  .. .. ..$ value: int 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "24 ≤ x < 27"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "27 ≤ x < 30"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "x = 30"
  .. .. ..$ value: int 1
  ..$ MrU_Age :List of 1
  .. ..$ weight: num 0.4
  ..$ baseline: int 30
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 1
  .. ..$ weight: num 1.6
 $ TOEFL             :List of 3
  ..$ baseline : int 380
  ..$ MrC_TOEFL:List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "621-677"
  .. .. ..$ value: int 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "561-620"
  .. .. ..$ value: int 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "441-560"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "381-440"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "≤ 380"
  .. .. ..$ value: int 1
  ..$ MrU_TOEFL:List of 1
  .. ..$ weight: num 0.6
 $ Domisili          :List of 1
  ..$ MrU_Domisil:List of 1
  .. ..$ weight: int 0
 $ Industry          :List of 1
  ..$ MrU_Industry:List of 1
  .. ..$ weight: int 0
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 2
  .. .. ..$ level: chr "SMA/SMK"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D3"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D4"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S3"
  .. .. ..$ value: int 0
  ..$ MrU_Education:List of 1
  .. ..$ weight: int 0
 $ Skillntools       :List of 1
  ..$ MrU_Skillntools:List of 1
  .. ..$ weight: int 0
 $ Expected_Salary   :List of 1
  ..$ MrU_ES:List of 1
  .. ..$ weight: int 0
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2 tahun"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2-5 tahun"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "7-12 tahun"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "10-15 tahun"
  .. .. ..$ value: int 3
  ..$ MrU_WE:List of 1
  .. ..$ weight: num 1.4
NULL
[1] "JSON Check Class"
[1] "numeric"
[1] "Run the tests"

Test 1:
Success: TRUE 
Value: 2.9-3.2 
Error: None 
Message: Successfully found non-zero value 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] "2.9-3.2"
[1] NA
[1] 30
[1] 380
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
          Applied Date
1  2024-12-13 03:09:43
2  2024-12-14 18:06:43
3  2024-12-17 15:35:33
4  2024-12-13 02:55:54
5  2024-12-15 15:56:59
6  2024-12-16 04:38:38
7  2024-12-16 04:26:55
8  2024-12-16 06:01:16
9  2024-12-16 05:43:41
10 2024-12-16 05:20:31
11 2024-12-27 08:06:36
12 2024-12-30 03:41:59
                                                                                         Role
1  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
2  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
3  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
4  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
5  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
6  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
7  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
8  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
9  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
10 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
11 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
12 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
                        Email User ID             Fullname
1  <EMAIL>      38      Putri Rosalinda
2          <EMAIL>      40 Imelda Putri Azzahra
3     <EMAIL>       3                 test
4    <EMAIL>      36             John Doe
5     <EMAIL>      43          Luna Thomas
6    <EMAIL>      45                 test
7     <EMAIL>      44          Nadia Omara
8     <EMAIL>      47        BENJAMIN SHAH
9     <EMAIL>      48         ARIF BUDIMAN
10    <EMAIL>      46    Sebastian Bennett
11 <EMAIL>      64              ANDI M.
12 <EMAIL>      65            BUDIYANTO
                              Kota - Kab Provinces Degree    GPA
1                                jakarta      <NA>     D3   4.00
2                                   <NA>      <NA>     S3   4.00
3                                   <NA>      <NA>   <NA>   0.00
4                          New York, USA      <NA>     S1   3.50
5              San Francisco, California      <NA>     S1   4.00
6                                   <NA>      <NA>     D3   0.00
7                 Anywhere St., Any City      <NA>     S1  99.00
8    Kabupaten Banggai - Sulawesi Tengah      <NA>     S1  98.00
9            Kalimantan Utara, Indonesia      <NA>     S1  99.00
10            123 Anywhere St., Any City      <NA>     S1 100.00
11                    Pati - Jawa Tengah      <NA>     S1   4.00
12 Kabupaten Banjar - Kalimantan Selatan      <NA>     S1   3.55
                                Major                Institution Degree 2nd
1                    Teknik Metalurgi         Aalborg University       <NA>
2                        Administrasi    Stikes Mitra Ria Husada       <NA>
3  Administrasi Asuransi dan Aktuaria     Aberystwyth University       <NA>
4                    Computer Science   University Of Technology       <NA>
5                        Administrasi   Universitas Cenderawasih       <NA>
6                        Administrasi           Aalto University       <NA>
7                             Ekonomi     Universitas Hasanuddin       <NA>
8                 Manajemen Pemasaran  Universitas Sam Ratulangi       <NA>
9                               Hukum Universitas Borneo Tarakan       <NA>
10                      Ilmu Komputer     Universitas Mulawarman       <NA>
11                Process Engineering     Engineering University       <NA>
12  Agribisnis Perikanan dan Kelautan    Universitas Gadjah Mada       <NA>
   GPA 2nd Major 2nd Institution 2nd                 Job Role          YoE
1       NA      <NA>            <NA>        digital marketing 1.290057e-01
2       NA      <NA>            <NA>             data science 8.219188e-02
3       NA      <NA>            <NA>            fraud analyst 6.323243e-01
4       NA      <NA>            <NA>        software engineer 2.502692e+00
5       NA      <NA>            <NA>          product manager 8.669816e+00
6       NA      <NA>            <NA>             data science 4.030315e-08
7       NA      <NA>            <NA>              ux designer 1.998583e+00
8       NA      <NA>            <NA>    mechatronics engineer 4.667076e+00
9       NA      <NA>            <NA>              ux engineer 1.998583e+00
10      NA      <NA>            <NA> junior digital marketing 5.001322e+00
11      NA      <NA>            <NA>              ux designer 3.828720e+00
12      NA      <NA>            <NA>              ux designer 3.828720e+00
   Experience
1           0
2           0
3           0
4           0
5           0
6           0
7           0
8           0
9           0
10          0
11          0
12          0
                                                                                                                                                                       Skill & Tools
1                                                                                                                                                         {"Microsoft Excel",Python}
2                                                                                                     {Canva,"layanan pelanggan","Microsoft Excel","Ms. Access",Tableau,Ubersuggest}
3                                                                                                                                                                              {Git}
4                                                                                                                                            {CSS,HTML,java,Javascript,MySQL,Spring}
5  {"Cross-functional Team Leadership","Customer Feedback Analysis","Financial Data Integration","Market analysis","Payment Processing","Product Strategy","User Experience Design"}
6                                                                                                                                                                              {Git}
7                                                                                                                        {Automation,"Process Improvement","Skill name","UX Design"}
8                                                                       {Automation,"Control Systems","Feasibility Studies",Mechatronics,"Project Management",Robotics,"Skill name"}
9                                                                                                                            {"Automation Systems","Project Management","UX Design"}
10                                                                                                                                                                     {"Tool name"}
11                                                                                                           {"Project Management","Skill name","System Design",Testing,"UX Design"}
12                                      {"Automation Systems","Project Management","Robotic Control System","Skill name","Technical Expertise","Testing and Validation","UX Design"}
                                                                                                                                                                 CV Upload
1  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Surat_Penawaran_Rekrutmen_Mandiri__POS_Indonesia_2024_1-cb732516-ca7a-4d84-b8ec-55bb148dab32.pdf
2               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FAQ_Toyota_Internship_Program_Batch_1_2025-2b6f728c-1b48-4669-986e-fd80bb2638dc.pdf
3                  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/sample193c6b73d1e76431faaa9f02c70e088e3-2eb47de6-c42a-4b21-af48-e56a29fe79cb.pdf
4                                        https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be78-9dbf1765f6d2.pdf
5                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf
6                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/dummy-2a85a707-f6a7-49b7-9da4-688fe8889a20.pdf
7                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-9cc01668-1617-4499-96bf-a16f247d2162.pdf
8                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_4-69a5af64-5a61-45da-a4e1-20493492218b.pdf
9                                                     https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-4a9c1324-6c58-46e7-858f-d6969baed5ef.pdf
10                                              https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_3-3667fdc1-4e01-4bba-bd0c-138aa1379645.pdf
11                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-457df4ce-a165-4c40-ab0e-3ddbdcf23543.pdf
12                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-1ac87e96-38ac-41bb-a4b4-432befa58317.pdf
                        Linkedin Link          Portfolio Link Instagram Link
1  https://www.linkedin.com/in/aksjas      https://aksjaks.co           <NA>
2                                                                       <NA>
3                                <NA>                    <NA>           <NA>
4                                <NA>                    <NA>           <NA>
5                                     https://www.enhancv.com           <NA>
6                                <NA>                    <NA>           <NA>
7                                <NA>                                   <NA>
8                                <NA>                                   <NA>
9                                                                       <NA>
10                               <NA>                    <NA>           <NA>
11                                                                      <NA>
12                                                                      <NA>
   Twitter Link                State Status Availability                 dob
1          <NA>             rejected        open_to_work 1996-02-01 02:59:04
2          <NA>              applied        open_to_work 1999-02-01 17:59:01
3          <NA>             rejected        open_to_work 2024-12-04 09:27:04
4          <NA>             rejected        open_to_work 2000-12-06 02:55:13
5          <NA>              applied        open_to_work 2000-12-04 03:13:38
6          <NA>              applied        open_to_work 2024-12-05 04:33:29
7          <NA>              applied        open_to_work 2000-12-01 03:48:25
8          <NA>     wawancara_online        open_to_work 2002-12-09 05:55:40
9          <NA>      medical_checkup        open_to_work 2001-12-06 05:40:22
10         <NA>             offering        open_to_work 2000-12-01 05:14:52
11         <NA> tes_potensi_online_2        open_to_work 1982-12-30 17:00:00
12         <NA>              applied        open_to_work 2000-12-04 15:51:17
    pob  ktp                     address              hp gender  dcp
1  <NA> <NA>                     Jakarta  +6281213363001 female <NA>
2  <NA> <NA>                        <NA>   +628197608102 female <NA>
3  <NA> <NA>                        <NA>     +**********   male <NA>
4  <NA> <NA>               New York, USA     +********** female <NA>
5  <NA> <NA>   San Francisco, California +**********1111 female <NA>
6  <NA> <NA>                        <NA>     +**********   male <NA>
7  <NA> <NA>      Anywhere St., Any City   +**********11 female <NA>
8  <NA> <NA>  123 Anywhere St., Any City   +62**********   male <NA>
9  <NA> <NA> Kalimantan Utara, Indonesia +62184147817481   male <NA>
10 <NA> <NA>  123 Anywhere St., Any City     +**********   male <NA>
11 <NA> <NA>          Pati - Jawa Tengah   +629183681631   male <NA>
12 <NA> <NA> Kalimantan Utara, Indonesia    +62813132131   male <NA>
   current_max current_min expect_max expect_min job_vacancy_id
1            0        <NA>   ********     100000              8
2            0        <NA>   30000000   10000000              8
3            0        <NA>          2          1              8
4            0        <NA>          2          1              8
5            0        <NA>          2          1              8
6            0        <NA>       2000       1000              8
7            0        <NA>          2          1              8
8            0        <NA>          2          1              8
9            0        <NA>          2          1              8
10           0        <NA>          2          1              8
11           0        <NA>          2          1              8
12           0        <NA>          2          1              8
   user_job_vacancy_id Calculated YoE
1                    2              0
2                    3              0
3                   12              0
4                    1              2
5                    4              8
6                    6              0
7                    5              1
8                    9              4
9                    8              1
10                   7              4
11                  67              3
12                 108              3
'data.frame':	12 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-13 03:09:43" "2024-12-14 18:06:43" ...
 $ Role               : chr  "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" ...
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>" ...
 $ User ID            :integer64 38 40 3 36 43 45 44 47 ... 
 $ Fullname           : chr  "Putri Rosalinda" "Imelda Putri Azzahra" "test" "John Doe" ...
 $ Kota - Kab         : chr  "jakarta" NA NA "New York, USA" ...
 $ Provinces          : chr  NA NA NA NA ...
 $ Degree             : chr  "D3" "S3" NA "S1" ...
 $ GPA                : num  4 4 0 3.5 4 0 99 98 99 100 ...
 $ Major              : chr  "Teknik Metalurgi" "Administrasi" "Administrasi Asuransi dan Aktuaria" "Computer Science" ...
 $ Institution        : chr  "Aalborg University" "Stikes Mitra Ria Husada" "Aberystwyth University" "University Of Technology" ...
 $ Degree 2nd         : chr  NA NA NA NA ...
 $ GPA 2nd            : num  NA NA NA NA NA NA NA NA NA NA ...
 $ Major 2nd          : chr  NA NA NA NA ...
 $ Institution 2nd    : chr  NA NA NA NA ...
 $ Job Role           : chr  "digital marketing" "data science" "fraud analyst" "software engineer" ...
 $ YoE                : num  0.129 0.0822 0.6323 2.5027 8.6698 ...
 $ Experience         : int  0 0 0 0 0 0 0 0 0 0 ...
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Microsoft Excel\",Python}" "{Canva,\"layanan pelanggan\",\"Microsoft Excel\",\"Ms. Access\",Tableau,Ubersuggest}" "{Git}" "{CSS,HTML,java,Javascript,MySQL,Spring}" ...
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Surat_Penawaran_Rekrutmen_Mandiri__POS_I"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FAQ_Toyota_Internship_Program_Batch_1_20"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/sample193c6b73d1e76431faaa9f02c70e088e3-"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be7"| __truncated__ ...
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/aksjas" "" NA NA ...
 $ Portfolio Link     : chr  "https://aksjaks.co" "" NA NA ...
 $ Instagram Link     : chr  NA NA NA NA ...
 $ Twitter Link       : chr  NA NA NA NA ...
 $ State              : chr  "rejected" "applied" "rejected" "rejected" ...
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work" "open_to_work" ...
 $ dob                : POSIXct, format: "1996-02-01 02:59:04" "1999-02-01 17:59:01" ...
 $ pob                : chr  NA NA NA NA ...
 $ ktp                : chr  NA NA NA NA ...
 $ address            : chr  "Jakarta" NA NA "New York, USA" ...
 $ hp                 : chr  "+6281213363001" "+628197608102" "+**********" "+**********" ...
 $ gender             : chr  "female" "female" "male" "female" ...
 $ dcp                : chr  NA NA NA NA ...
 $ current_max        :integer64 0 0 0 0 0 0 0 0 ... 
 $ current_min        :integer64 NA NA NA NA NA NA NA NA ... 
 $ expect_max         :integer64 ******** 30000000 2 2 2 2000 2 2 ... 
 $ expect_min         :integer64 100000 10000000 1 1 1 1000 1 1 ... 
 $ job_vacancy_id     :integer64 8 8 8 8 8 8 8 8 ... 
 $ user_job_vacancy_id:integer64 2 3 12 1 4 6 5 9 ... 
 $ Calculated YoE     : int  0 0 0 2 8 0 1 4 1 4 ...
          Applied Date User ID             Fullname Job Role ID
1  2024-12-13 03:09:43      38      Putri Rosalinda           2
2  2024-12-14 18:06:43      40 Imelda Putri Azzahra           1
3  2024-12-17 15:35:33       3                 test           3
4  2024-12-13 02:55:54      36             John Doe          17
5  2024-12-15 15:56:59      43          Luna Thomas         205
6  2024-12-15 15:56:59      43          Luna Thomas         865
7  2024-12-15 15:56:59      43          Luna Thomas          78
8  2024-12-16 04:38:38      45                 test           1
9  2024-12-16 04:26:55      44          Nadia Omara          87
10 2024-12-16 06:01:16      47        BENJAMIN SHAH         868
11 2024-12-16 06:01:16      47        BENJAMIN SHAH         869
12 2024-12-16 06:01:16      47        BENJAMIN SHAH         643
13 2024-12-16 05:43:41      48         ARIF BUDIMAN         860
14 2024-12-16 05:20:31      46    Sebastian Bennett         866
15 2024-12-27 08:06:36      64              ANDI M.          87
16 2024-12-27 08:06:36      64              ANDI M.         859
17 2024-12-30 03:41:59      65            BUDIYANTO          87
18 2024-12-30 03:41:59      65            BUDIYANTO         859
                    Job Role   industry               company_name work_type
1          digital marketing Technology                     jajaja full_time
2               data science Technology                        abc full_time
3              fraud analyst    Finance                          a  contract
4          software engineer       <NA>        Tech Solutions Inc. full_time
5  associate product manager       <NA>                     Stripe full_time
6     senior product manager       <NA>                      Plaid full_time
7            product manager       <NA>                      Plaid full_time
8               data science Technology                       test part_time
9                ux designer       <NA>                   Morcelle full_time
10     mechatronics engineer       <NA>      Borcelle Technologies full_time
11   junior project engineer       <NA> Salford & Co Manufacturing full_time
12           system engineer       <NA>         Arrowai Industries full_time
13               ux engineer       <NA>                   Morcelle full_time
14  junior digital marketing       <NA>            Ingoude Company full_time
15               ux designer       <NA>                   Morcelle full_time
16        system ux engineer       <NA>        XarrowAI Industries full_time
17               ux designer       <NA>                   Morcelle full_time
18        system ux engineer       <NA>        XarrowAI Industries full_time
               ends_at           starts_at          MoE
1                 <NA> 2024-11-13 02:29:42 1.569569e+00
2  2024-12-14 17:53:20 2024-11-14 17:53:16 1.000001e+00
3                 <NA> 2024-05-13 09:25:28 7.693279e+00
4                 <NA> 2022-06-30 17:00:00 3.044942e+01
5  2018-04-30 17:00:00 2016-02-29 17:00:00 2.636667e+01
6  2020-11-30 17:00:00 2018-05-31 17:00:00 3.046667e+01
7                 <NA> 2020-12-31 17:00:00 4.864942e+01
8  2024-02-16 04:33:09 2024-02-16 04:33:07 4.903549e-07
9                 <NA> 2022-12-31 17:00:00 2.431609e+01
10                <NA> 2022-12-31 17:00:00 2.431609e+01
11 2020-12-31 17:00:00 2020-02-29 17:00:00 1.020000e+01
12 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
13                <NA> 2022-12-31 17:00:00 2.431609e+01
14                <NA> 2019-12-31 17:00:00 6.084942e+01
15                <NA> 2022-12-31 17:00:00 2.431609e+01
16 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
17                <NA> 2022-12-31 17:00:00 2.431609e+01
18 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
'data.frame':	18 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-13 03:09:43" "2024-12-14 18:06:43" ...
 $ User ID     :integer64 38 40 3 36 43 43 43 45 ... 
 $ Fullname    : chr  "Putri Rosalinda" "Imelda Putri Azzahra" "test" "John Doe" ...
 $ Job Role ID :integer64 2 1 3 17 205 865 78 1 ... 
 $ Job Role    : chr  "digital marketing" "data science" "fraud analyst" "software engineer" ...
 $ industry    : chr  "Technology" "Technology" "Finance" NA ...
 $ company_name: chr  "jajaja" "abc" "a" "Tech Solutions Inc." ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "contract" "full_time" ...
 $ ends_at     : POSIXct, format: NA "2024-12-14 17:53:20" ...
 $ starts_at   : POSIXct, format: "2024-11-13 02:29:42" "2024-11-14 17:53:16" ...
 $ MoE         : num  1.57 1 7.69 30.45 26.37 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning: There was 1 warning in `mutate()`.
ℹ In argument: `dob = dmy_hm(dob)`.
Caused by warning:
! All formats failed to parse. No formats found.
[1] "TOEFL Score Data"
<simpleError in `$<-.data.frame`(`*tmp*`, "TOEFL Score", value = numeric(0)): replacement has 0 rows, data has 10>
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 12
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Data Science"
$ id                               <int64> 12
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> "data science"
$ `Tools and Competencies Mastery` <chr> "A/B Testing tools"
$ `Education Level`                <chr> NA
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Bolaang Mongondow Timur - …
$ job_level                        <chr> NA
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Pendidikan Dokter"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Pendidikan Dokter"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE


$Domisili$MrC_Domisili
$Domisili$MrC_Domisili[[1]]
$Domisili$MrC_Domisili[[1]]$name
[1] "Kabupaten Bolaang Mongondow Timur - Sulawesi Utara"

$Domisili$MrC_Domisili[[1]]$value
[1] 0

$Domisili$MrC_Domisili[[1]]$order_level
[1] 0



$Domisili$MrU_Domisili
$Domisili$MrU_Domisili$weight
[1] 0

$Domisili$MrU_Domisili$required
[1] FALSE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] FALSE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0

$Education$MrC_Education[[2]]$disabled
[1] FALSE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0

$Education$MrC_Education[[3]]$disabled
[1] FALSE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0

$Education$MrC_Education[[4]]$disabled
[1] FALSE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0

$Education$MrC_Education[[5]]$disabled
[1] FALSE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 1

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 2

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0

$Education$MrU_Education$required
[1] FALSE



$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "A/B Testing tools"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0

$Working_Experience$MrU_WE$required
[1] FALSE



[1] "JSON Check"
List of 8
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Major             :List of 2
  ..$ MrC_Major:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Pendidikan Dokter"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Major:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi TRUE
 $ Domisili          :List of 3
  ..$ MrU_Domisil :List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
  ..$ MrC_Domisili:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "Kabupaten Bolaang Mongondow Timur - Sulawesi Utara"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Domisili:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi FALSE
 $ Industry          :List of 2
  ..$ MrC_Industry: list()
  ..$ MrU_Industry:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 4
  .. .. ..$ level      : chr "SMA/SMK"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D2"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D3"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 3
  .. ..$ :List of 4
  .. .. ..$ level      : chr "D4"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S1"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 5
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S2"
  .. .. ..$ value      : int 1
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 6
  .. ..$ :List of 4
  .. .. ..$ level      : chr "S3"
  .. .. ..$ value      : int 2
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 7
  ..$ MrU_Education:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi FALSE
 $ Skillntools       :List of 2
  ..$ MrC_Skillntools:List of 1
  .. ..$ :List of 3
  .. .. ..$ name       : chr "A/B Testing tools"
  .. .. ..$ value      : int 0
  .. .. ..$ order_level: int 0
  ..$ MrU_Skillntools:List of 2
  .. ..$ weight  : int 3
  .. ..$ required: logi TRUE
 $ Expected_Salary   :List of 2
  ..$ MrC_ES:List of 3
  .. ..$ :List of 2
  .. .. ..$ name : chr "< 1.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "1.0 - 2.0"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ name : chr "> 2.0"
  .. .. ..$ value: int 0
  ..$ MrU_ES:List of 2
  .. ..$ weight  : int 1
  .. ..$ required: logi TRUE
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 4
  .. .. ..$ level      : chr "0-2 tahun"
  .. .. ..$ value      : int 0
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 0
  .. ..$ :List of 4
  .. .. ..$ level      : chr "2-5 tahun"
  .. .. ..$ value      : int 1
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 1
  .. ..$ :List of 4
  .. .. ..$ level      : chr "7-12 tahun"
  .. .. ..$ value      : int 2
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 2
  .. ..$ :List of 4
  .. .. ..$ level      : chr "10-15 tahun"
  .. .. ..$ value      : int 3
  .. .. ..$ disabled   : logi FALSE
  .. .. ..$ order_level: int 3
  ..$ MrU_WE:List of 2
  .. ..$ weight  : int 0
  .. ..$ required: logi FALSE
NULL
[1] "JSON Check Class"
[1] "integer"
Info: All values are zero
[1] "Run the tests"

Test 1:
Success: FALSE 
Value: NA 
Error: None 
Message: All values are zero 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] NA
[1] NA
[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
          Applied Date         Role                      Email User ID
1  2024-12-17 18:01:24 Data Science   <EMAIL>      37
2  2024-12-17 17:53:47 Data Science   <EMAIL>      36
3  2024-12-17 16:25:22 Data Science    <EMAIL>      43
4  2024-12-27 14:19:27 <NAME_EMAIL>     100
5  2024-12-17 18:18:42 Data Science    <EMAIL>      44
6  2024-12-17 18:23:01 Data Science    <EMAIL>      47
7  2024-12-17 18:25:22 Data Science    <EMAIL>      48
8  2024-12-17 19:37:50 Data Science    <EMAIL>      50
9  2024-12-17 20:05:31 <NAME_EMAIL>      55
10 2024-12-18 01:31:20 Data Science   <EMAIL>      56
11 2024-12-18 01:37:51 <NAME_EMAIL>      57
12 2024-12-18 01:51:51 <NAME_EMAIL>      60
13 2024-12-18 02:29:31 Data Science   <EMAIL>      61
14 2024-12-19 13:56:56 <NAME_EMAIL>      63
15 2024-12-18 08:26:29 Data Science    <EMAIL>      62
16 2024-12-19 13:36:36 <NAME_EMAIL>      64
17 2024-12-27 10:31:10 <NAME_EMAIL>      98
18 2024-12-27 14:07:09 <NAME_EMAIL>      99
           Fullname                            Kota - Kab Provinces Degree  GPA
1     Estelle Darcy            123 Anywhere St., Any City      <NA>     S1 99.0
2          John Doe                         New York, USA      <NA>     S1  3.5
3       Luna Thomas             San Francisco, California      <NA>     S1  4.0
4        studentdig           Kabupaten Aceh Barat - Aceh      <NA>     S1  3.3
5       Nadia Omara                Anywhere St., Any City      <NA>     S1 99.0
6     BENJAMIN SHAH   Kabupaten Banggai - Sulawesi Tengah      <NA>     S1 98.0
7      ARIF BUDIMAN           Kalimantan Utara, Indonesia      <NA>     S1 99.0
8      ARIF BUDIMAN Kabupaten Bulungan - Kalimantan Utara      <NA>     S1 99.0
9       Arif Rahman                               jakarta      <NA>     S1  3.5
10     ARIF BUDIMAN           Kabupaten Aceh Barat - Aceh      <NA>     S1  0.0
11                s                                 Medan      <NA>     S1  0.0
12                s                                 Medan      <NA>     S1  0.0
13     ARIF BUDIMAN           Kabupaten Aceh Barat - Aceh      <NA>     S1  0.0
14          student                                 Medan      <NA>     S1  0.0
15 Rachelle Beaudry   Kabupaten Banggai - Sulawesi Tengah      <NA>     S2  0.0
16          ANDI M.                    Pati - Jawa Tengah      <NA>     S1  4.0
17       studentdig                                 Medan      <NA>     S1  0.0
18       studentdig                                 Medan      <NA>     S1  3.3
                  Major                          Institution Degree 2nd GPA 2nd
1          Administrasi                Universitas Indonesia       <NA>      NA
2      Computer Science             University Of Technology       <NA>      NA
3          Administrasi             Universitas Cenderawasih       <NA>      NA
4   Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
5               Ekonomi               Universitas Hasanuddin       <NA>      NA
6   Manajemen Pemasaran            Universitas Sam Ratulangi       <NA>      NA
7                 Hukum           Universitas Borneo Tarakan       <NA>      NA
8               Ekonomi                Universitas Halmahera       <NA>      NA
9    Teknik Informatika                Universitas Indonesia       <NA>      NA
10  Process Engineering               Engineering University       <NA>      NA
11  Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
12  Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
13  Process Engineering               Engineering University       <NA>      NA
14  Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
15 Financial Management University Of Finance And Management         S1       0
16  Process Engineering               Engineering University       <NA>      NA
17  Ekonomi Pembangunan           Universitas Sumatera Utara       <NA>      NA
18  Ekonomi Pembangunan           Universitas Sumatera Utara         S1       0
             Major 2nd            Institution 2nd               Job Role
1                 <NA>                       <NA>            ux engineer
2                 <NA>                       <NA>      software engineer
3                 <NA>                       <NA>        product manager
4                 <NA>                       <NA>                   <NA>
5                 <NA>                       <NA>            ux designer
6                 <NA>                       <NA>  mechatronics engineer
7                 <NA>                       <NA>            ux engineer
8                 <NA>                       <NA>            ux designer
9                 <NA>                       <NA>    front end developer
10                <NA>                       <NA>            ux engineer
11                <NA>                       <NA>                   <NA>
12                <NA>                       <NA>                   <NA>
13                <NA>                       <NA>            ux designer
14                <NA>                       <NA>                   <NA>
15          Accounting               City College   accounting executive
16                <NA>                       <NA>            ux designer
17                <NA>                       <NA>                   <NA>
18 Ekonomi Pembangunan Universitas Sumatera Utara quality assurance test
           YoE Experience
1  3.*********          0
2  2.*********          0
3  8.*********          0
4  0.*********          0
5  1.*********          0
6  4.*********          0
7  1.*********          0
8  3.*********          0
9  3.*********          0
10 3.*********          0
11 0.*********          0
12 0.*********          0
13 3.*********          0
14 0.*********          0
15 4.*********          0
16 3.*********          0
17 0.*********          0
18 0.*********          0
                                                                                                                                                                                                     Skill & Tools
1                                                                                {Automation,"Efficiency Improvement","Project Management","Robotic Control","System Optimization",Testing,"UX Design",Validation}
2                                                                                                                                                                          {CSS,HTML,java,Javascript,MySQL,Spring}
3                                {"Cross-functional Team Leadership","Customer Feedback Analysis","Financial Data Integration","Market analysis","Payment Processing","Product Strategy","User Experience Design"}
4                                                                                                                                                                                                             <NA>
5                                                                                                                                                      {Automation,"Process Improvement","Skill name","UX Design"}
6                                                                                                     {Automation,"Control Systems","Feasibility Studies",Mechatronics,"Project Management",Robotics,"Skill name"}
7                                                                                                                                                          {"Automation Systems","Project Management","UX Design"}
8                                                                                                                                        {"Project Management","Robotic Control Systems","Skill name","UX Design"}
9                                                                                                                                                                              {CSS,HTML,JavaScript,Node.js,React}
10                                                                                                                                       {"Project Management","Robotic Control Systems","Skill name","UX Design"}
11                                                                                                                                                                                                            <NA>
12                                                                                                                                                                                                            <NA>
13                                                                            {Automation,"Process Improvement","Robotic Control Systems","Skill name","System Optimization","Testing and Validation","UX Design"}
14                                                                                                                                                                                                            <NA>
15 {"Audit Compliance","Cost Control","Cross-functional Collaboration","Financial Forecasting","Financial Reporting","Financial Statements","Internal Audits","Month-End Close","Process Automation","Skill name"}
16                                                                                                                                         {"Project Management","Skill name","System Design",Testing,"UX Design"}
17                                                                                                                                                                                                            <NA>
18                                                                                                                                                  {"API Testing",Postman,"Selenium Web Driver","Web Automation"}
                                                                                                                                         CV Upload
1                       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-4862907d-64b4-4308-b595-2de1d29f3ddb.pdf
2                https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be78-9dbf1765f6d2.pdf
3                       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf
4  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-14d9b2e6-7ae2-43ca-8db9-f0a793bc7619.pdf
5                       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-9cc01668-1617-4499-96bf-a16f247d2162.pdf
6                       https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_4-69a5af64-5a61-45da-a4e1-20493492218b.pdf
7                             https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-4a9c1324-6c58-46e7-858f-d6969baed5ef.pdf
8                             https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-981fe9b0-e1ce-4a6a-9679-f044823064d0.pdf
9                https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-ab1497d6-2b49-48ec-aadd-42d8c6f2a5bc.pdf
10                            https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-84789329-5537-49d3-ad30-5fe78480a508.pdf
11 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-4a3ad227-65ce-4108-8fb2-a07caca1f38e.pdf
12 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-2aa7575e-5769-4814-a3ae-ad972e205451.pdf
13                            https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-e8c5cee0-e0fb-44e1-bdcf-f1d4a28caa73.pdf
14 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-28ac1ae1-5999-4bd4-89ad-071ba03eb36b.pdf
15                      https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_7-6c390dc6-50ac-4ea3-bb35-02399d723844.pdf
16                            https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-457df4ce-a165-4c40-ab0e-3ddbdcf23543.pdf
17 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-daf70615-ead0-4951-866a-588794ace079.pdf
18 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-ea2cc548-d80a-4a87-9593-2cec16bf53d7.pdf
   Linkedin Link                  Portfolio Link Instagram Link Twitter Link
1           <NA> https://www.reallygreatsite.com           <NA>         <NA>
2           <NA>                            <NA>           <NA>         <NA>
3                        https://www.enhancv.com           <NA>         <NA>
4           <NA>                            <NA>           <NA>         <NA>
5           <NA>                                           <NA>         <NA>
6           <NA>                                           <NA>         <NA>
7                                                          <NA>         <NA>
8                                                          <NA>         <NA>
9           <NA>                            <NA>           <NA>         <NA>
10                                                         <NA>         <NA>
11          <NA>                            <NA>           <NA>         <NA>
12          <NA>                            <NA>           <NA>         <NA>
13                                                         <NA>         <NA>
14          <NA>                            <NA>           <NA>         <NA>
15          <NA>                                           <NA>         <NA>
16                                                         <NA>         <NA>
17          <NA>                            <NA>           <NA>         <NA>
18          <NA>                            <NA>           <NA>         <NA>
                     State Status Availability                 dob  pob  ktp
1                 rejected        open_to_work 2000-12-01 09:58:01 <NA> <NA>
2     tes_potensi_online_1        open_to_work 2000-12-06 02:55:13 <NA> <NA>
3     tes_potensi_online_1        open_to_work 2000-12-04 03:13:38 <NA> <NA>
4  assessment_psikotes_2.1        open_to_work 2024-12-01 14:19:07 <NA> <NA>
5                 rejected        open_to_work 2000-12-01 03:48:25 <NA> <NA>
6     tes_potensi_online_1        open_to_work 2002-12-09 05:55:40 <NA> <NA>
7     tes_potensi_online_1        open_to_work 2001-12-06 05:40:22 <NA> <NA>
8     tes_potensi_online_1        open_to_work 2002-04-01 07:15:25 <NA> <NA>
9     tes_potensi_online_1        open_to_work 2024-11-30 20:04:31 <NA> <NA>
10    tes_potensi_online_1        open_to_work 2000-12-02 01:31:05 <NA> <NA>
11    tes_potensi_online_1        open_to_work 2024-12-01 01:36:57 <NA> <NA>
12    tes_potensi_online_1        open_to_work 2024-12-01 01:51:20 <NA> <NA>
13    tes_potensi_online_1        open_to_work 2024-12-12 02:29:23 <NA> <NA>
14    tes_potensi_online_1        open_to_work 2024-12-01 10:12:13 <NA> <NA>
15                   hired        open_to_work 2001-12-05 08:26:12 <NA> <NA>
16                   hired        open_to_work 1982-12-30 17:00:00 <NA> <NA>
17 assessment_psikotes_2.1        open_to_work 2024-12-01 10:30:27 <NA> <NA>
18 assessment_psikotes_2.1        open_to_work 2024-12-01 14:06:17 <NA> <NA>
                       address              hp gender  dcp current_max
1   123 Anywhere St., Any City +62123456781234 female <NA>           0
2                New York, USA     +********** female <NA>           0
3    San Francisco, California +**********1111 female <NA>           0
4                         <NA> +62123412341234   male <NA>           0
5       Anywhere St., Any City   +**********11 female <NA>           0
6   123 Anywhere St., Any City   +62**********   male <NA>           0
7  Kalimantan Utara, Indonesia +62184147817481   male <NA>           0
8  Kalimantan Utara, Indonesia +62718942146141   male <NA>           0
9                      Jakarta   +628123456789 female <NA>           0
10 Kalimantan Utara, Indonesia +62418471894141   male <NA>           0
11                       Medan    +62618218532 female <NA>           0
12                       Medan    +62618218532 female <NA>           0
13 Kalimantan Utara, Indonesia  +6249275892525   male <NA>           0
14                       Medan    +62618218532 female <NA>           0
15  123 Anywhere St., Any City +6288********** female <NA>           0
16          Pati - Jawa Tengah   +629183681631   male <NA>           0
17                       Medan    +62618218532   male <NA>           0
18                       Medan    +62618218532   male <NA>           0
   current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1         <NA>          2          1             12                  16
2         <NA>          2          1             12                  15
3         <NA>          2          1             12                  13
4         <NA>          2          1             12                  75
5         <NA>          2          1             12                  17
6         <NA>          2          1             12                  18
7         <NA>          2          1             12                  19
8         <NA>          2          1             12                  20
9         <NA>          2          1             12                  21
10        <NA>          2          1             12                  22
11        <NA>          2          1             12                  23
12        <NA>          2          1             12                  24
13        <NA>          2          1             12                  25
14        <NA>          2          1             12                  33
15        <NA>          2          1             12                  26
16        <NA>          2          1             12                  32
17        <NA>          2          1             12                  68
18        <NA>          2          1             12                  74
   Calculated YoE
1               3
2               2
3               8
4               0
5               1
6               4
7               1
8               3
9               3
10              3
11              0
12              0
13              3
14              0
15              4
16              3
17              0
18              0
'data.frame':	18 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-17 18:01:24" "2024-12-17 17:53:47" ...
 $ Role               : chr  "Data Science" "Data Science" "Data Science" "Data Science" ...
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>" ...
 $ User ID            :integer64 37 36 43 100 44 47 48 50 ... 
 $ Fullname           : chr  "Estelle Darcy" "John Doe" "Luna Thomas" "studentdig" ...
 $ Kota - Kab         : chr  "123 Anywhere St., Any City" "New York, USA" "San Francisco, California" "Kabupaten Aceh Barat - Aceh" ...
 $ Provinces          : chr  NA NA NA NA ...
 $ Degree             : chr  "S1" "S1" "S1" "S1" ...
 $ GPA                : num  99 3.5 4 3.3 99 98 99 99 3.5 0 ...
 $ Major              : chr  "Administrasi" "Computer Science" "Administrasi" "Ekonomi Pembangunan" ...
 $ Institution        : chr  "Universitas Indonesia" "University Of Technology" "Universitas Cenderawasih" "Universitas Sumatera Utara" ...
 $ Degree 2nd         : chr  NA NA NA NA ...
 $ GPA 2nd            : num  NA NA NA NA NA NA NA NA NA NA ...
 $ Major 2nd          : chr  NA NA NA NA ...
 $ Institution 2nd    : chr  NA NA NA NA ...
 $ Job Role           : chr  "ux engineer" "software engineer" "product manager" NA ...
 $ YoE                : num  3.83 2.5 8.67 0 2 ...
 $ Experience         : int  0 0 0 0 0 0 0 0 0 0 ...
 $ Skill & Tools      : 'pq__varchar' chr  "{Automation,\"Efficiency Improvement\",\"Project Management\",\"Robotic Control\",\"System Optimization\",Testi"| __truncated__ "{CSS,HTML,java,Javascript,MySQL,Spring}" "{\"Cross-functional Team Leadership\",\"Customer Feedback Analysis\",\"Financial Data Integration\",\"Market an"| __truncated__ NA ...
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-4862907d-64b4-4308-b595-2de1d29f3ddb.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be7"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FORM_KOSONG_TRANSKRIP_SEMENTARA-14d9b2e6"| __truncated__ ...
 $ Linkedin Link      : chr  NA NA "" NA ...
 $ Portfolio Link     : chr  "https://www.reallygreatsite.com" NA "https://www.enhancv.com" NA ...
 $ Instagram Link     : chr  NA NA NA NA ...
 $ Twitter Link       : chr  NA NA NA NA ...
 $ State              : chr  "rejected" "tes_potensi_online_1" "tes_potensi_online_1" "assessment_psikotes_2.1" ...
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work" "open_to_work" ...
 $ dob                : POSIXct, format: "2000-12-01 09:58:01" "2000-12-06 02:55:13" ...
 $ pob                : chr  NA NA NA NA ...
 $ ktp                : chr  NA NA NA NA ...
 $ address            : chr  "123 Anywhere St., Any City" "New York, USA" "San Francisco, California" NA ...
 $ hp                 : chr  "+62123456781234" "+**********" "+**********1111" "+62123412341234" ...
 $ gender             : chr  "female" "female" "female" "male" ...
 $ dcp                : chr  NA NA NA NA ...
 $ current_max        :integer64 0 0 0 0 0 0 0 0 ... 
 $ current_min        :integer64 NA NA NA NA NA NA NA NA ... 
 $ expect_max         :integer64 2 2 2 2 2 2 2 2 ... 
 $ expect_min         :integer64 1 1 1 1 1 1 1 1 ... 
 $ job_vacancy_id     :integer64 12 12 12 12 12 12 12 12 ... 
 $ user_job_vacancy_id:integer64 16 15 13 75 17 18 19 20 ... 
 $ Calculated YoE     : int  3 2 8 0 1 4 1 3 3 3 ...
          Applied Date User ID         Fullname Job Role ID
1  2024-12-17 18:01:24      37    Estelle Darcy         859
2  2024-12-17 18:01:24      37    Estelle Darcy         860
3  2024-12-17 17:53:47      36         John Doe          17
4  2024-12-17 16:25:22      43      Luna Thomas         205
5  2024-12-17 16:25:22      43      Luna Thomas         865
6  2024-12-17 16:25:22      43      Luna Thomas          78
7  2024-12-17 18:18:42      44      Nadia Omara          87
8  2024-12-17 18:23:01      47    BENJAMIN SHAH         868
9  2024-12-17 18:23:01      47    BENJAMIN SHAH         869
10 2024-12-17 18:23:01      47    BENJAMIN SHAH         643
11 2024-12-17 18:25:22      48     ARIF BUDIMAN         860
12 2024-12-17 19:37:50      50     ARIF BUDIMAN          87
13 2024-12-17 19:37:50      50     ARIF BUDIMAN         859
14 2024-12-17 20:05:31      55      Arif Rahman          62
15 2024-12-17 20:05:31      55      Arif Rahman          59
16 2024-12-18 01:31:20      56     ARIF BUDIMAN         859
17 2024-12-18 01:31:20      56     ARIF BUDIMAN         860
18 2024-12-18 02:29:31      61     ARIF BUDIMAN          87
19 2024-12-18 02:29:31      61     ARIF BUDIMAN         859
20 2024-12-18 08:26:29      62 Rachelle Beaudry         872
21 2024-12-18 08:26:29      62 Rachelle Beaudry         873
22 2024-12-18 08:26:29      62 Rachelle Beaudry         874
23 2024-12-19 13:36:36      64          ANDI M.          87
24 2024-12-19 13:36:36      64          ANDI M.         859
25 2024-12-27 14:07:09      99       studentdig         905
                    Job Role industry               company_name work_type
1         system ux engineer     <NA>        XarrowAI Industries full_time
2                ux engineer     <NA>                   Morcelle full_time
3          software engineer     <NA>        Tech Solutions Inc. full_time
4  associate product manager     <NA>                     Stripe full_time
5     senior product manager     <NA>                      Plaid full_time
6            product manager     <NA>                      Plaid full_time
7                ux designer     <NA>                   Morcelle full_time
8      mechatronics engineer     <NA>      Borcelle Technologies full_time
9    junior project engineer     <NA> Salford & Co Manufacturing full_time
10           system engineer     <NA>         Arrowai Industries full_time
11               ux engineer     <NA>                   Morcelle full_time
12               ux designer     <NA>                   Morcelle full_time
13        system ux engineer     <NA>        XarrowAI Industries full_time
14             web developer     <NA>                  Freelance freelance
15       front end developer     <NA>     PT Teknologi Informasi full_time
16        system ux engineer     <NA>        XarrowAI Industries full_time
17               ux engineer     <NA>                   Morcelle full_time
18               ux designer     <NA>                   Morcelle full_time
19        system ux engineer     <NA>        XarrowAI Industries full_time
20         junior accountant     <NA>         Arowwai Industries full_time
21                accountant     <NA>               Salford & Co full_time
22      accounting executive     <NA>       Borcelle Corporation full_time
23               ux designer     <NA>                   Morcelle full_time
24        system ux engineer     <NA>        XarrowAI Industries full_time
25    quality assurance test     <NA>                    Jubelio full_time
               ends_at           starts_at         MoE
1  2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
2                 <NA> 2022-12-31 17:00:00 24.********
3                 <NA> 2022-06-30 17:00:00 30.********
4  2018-04-30 17:00:00 2016-02-29 17:00:00 26.********
5  2020-11-30 17:00:00 2018-05-31 17:00:00 30.********
6                 <NA> 2020-12-31 17:00:00 48.********
7                 <NA> 2022-12-31 17:00:00 24.********
8                 <NA> 2022-12-31 17:00:00 24.********
9  2020-12-31 17:00:00 2020-02-29 17:00:00 10.********
10 2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
11                <NA> 2022-12-31 17:00:00 24.********
12                <NA> 2022-12-31 17:00:00 24.********
13 2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
14 2022-07-30 17:00:00 2020-12-31 17:00:00 19.********
15                <NA> 2022-07-31 17:00:00 29.41620623
16 2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
17                <NA> 2022-12-31 17:00:00 24.********
18                <NA> 2022-12-31 17:00:00 24.********
19 2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
20 2020-12-31 17:00:00 2020-01-31 17:00:00 11.16666667
21 2022-11-30 17:00:00 2021-02-28 17:00:00 21.33333333
22                <NA> 2022-12-31 17:00:00 24.********
23                <NA> 2022-12-31 17:00:00 24.********
24 2022-11-30 17:00:00 2021-01-31 17:00:00 22.********
25                <NA> 2024-12-27 14:05:01  0.08692343
'data.frame':	25 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-17 18:01:24" "2024-12-17 18:01:24" ...
 $ User ID     :integer64 37 37 36 43 43 43 44 47 ... 
 $ Fullname    : chr  "Estelle Darcy" "Estelle Darcy" "John Doe" "Luna Thomas" ...
 $ Job Role ID :integer64 859 860 17 205 865 78 87 868 ... 
 $ Job Role    : chr  "system ux engineer" "ux engineer" "software engineer" "associate product manager" ...
 $ industry    : chr  NA NA NA NA ...
 $ company_name: chr  "XarrowAI Industries" "Morcelle" "Tech Solutions Inc." "Stripe" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2022-11-30 17:00:00" NA ...
 $ starts_at   : POSIXct, format: "2021-01-31 17:00:00" "2022-12-31 17:00:00" ...
 $ MoE         : num  22.3 24.3 30.4 26.4 30.5 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning: There was 1 warning in `mutate()`.
ℹ In argument: `dob = dmy_hm(dob)`.
Caused by warning:
! All formats failed to parse. No formats found.
[1] "TOEFL Score Data"
[1] "df_toefl is NULL or empty. Skipping the join."
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
<simpleError in `$<-.data.frame`(`*tmp*`, toefl_level, value = list()): replacement has 0 rows, data has 18>
