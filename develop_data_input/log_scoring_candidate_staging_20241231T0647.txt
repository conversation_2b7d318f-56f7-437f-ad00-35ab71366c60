
Attaching package: ‘dplyr’

The following objects are masked from ‘package:stats’:

    filter, lag

The following objects are masked from ‘package:base’:

    intersect, setdiff, setequal, union


Attaching package: ‘lubridate’

The following objects are masked from ‘package:base’:

    date, intersect, setdiff, union


Attaching package: ‘zoo’

The following objects are masked from ‘package:base’:

    as.Date, as.Date.numeric

Running plumber API at http://0.0.0.0:5656
Running swagger Docs at http://127.0.0.1:5656/__docs__/

Attaching package: ‘jsonlite’

The following object is masked from ‘package:purrr’:

    flatten


Attaching package: ‘stringdist’

The following object is masked from ‘package:tidyr’:

    extract

[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN public.university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 8
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Supervisor Kantor Cabang di wilayah …
$ id                               <int64> 8
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Product"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "Collaborative Working, Reporting a…
$ `Education Level`                <chr> "D3"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "contract"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Administrasi Bisnis, Akuntansi, Ekon…
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$Age
$Age$MrC_Age
$Age$MrC_Age[[1]]
$Age$MrC_Age[[1]]$level
[1] "21 ≤ x < 24"

$Age$MrC_Age[[1]]$value
[1] 5


$Age$MrC_Age[[2]]
$Age$MrC_Age[[2]]$level
[1] "18 ≤ x < 21"

$Age$MrC_Age[[2]]$value
[1] 4


$Age$MrC_Age[[3]]
$Age$MrC_Age[[3]]$level
[1] "24 ≤ x < 27"

$Age$MrC_Age[[3]]$value
[1] 3


$Age$MrC_Age[[4]]
$Age$MrC_Age[[4]]$level
[1] "27 ≤ x < 30"

$Age$MrC_Age[[4]]$value
[1] 2


$Age$MrC_Age[[5]]
$Age$MrC_Age[[5]]$level
[1] "x = 30"

$Age$MrC_Age[[5]]$value
[1] 1



$Age$MrU_Age
$Age$MrU_Age$weight
[1] 0.4


$Age$baseline
[1] 30


$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 1.6



$TOEFL
$TOEFL$baseline
[1] 380

$TOEFL$MrC_TOEFL
$TOEFL$MrC_TOEFL[[1]]
$TOEFL$MrC_TOEFL[[1]]$level
[1] "621-677"

$TOEFL$MrC_TOEFL[[1]]$value
[1] 5


$TOEFL$MrC_TOEFL[[2]]
$TOEFL$MrC_TOEFL[[2]]$level
[1] "561-620"

$TOEFL$MrC_TOEFL[[2]]$value
[1] 4


$TOEFL$MrC_TOEFL[[3]]
$TOEFL$MrC_TOEFL[[3]]$level
[1] "441-560"

$TOEFL$MrC_TOEFL[[3]]$value
[1] 3


$TOEFL$MrC_TOEFL[[4]]
$TOEFL$MrC_TOEFL[[4]]$level
[1] "381-440"

$TOEFL$MrC_TOEFL[[4]]$value
[1] 2


$TOEFL$MrC_TOEFL[[5]]
$TOEFL$MrC_TOEFL[[5]]$level
[1] "≤ 380"

$TOEFL$MrC_TOEFL[[5]]$value
[1] 1



$TOEFL$MrU_TOEFL
$TOEFL$MrU_TOEFL$weight
[1] 0.6



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 1.4



[1] "JSON Check"
List of 9
 $ Age               :List of 3
  ..$ MrC_Age :List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "21 ≤ x < 24"
  .. .. ..$ value: int 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "18 ≤ x < 21"
  .. .. ..$ value: int 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "24 ≤ x < 27"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "27 ≤ x < 30"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "x = 30"
  .. .. ..$ value: int 1
  ..$ MrU_Age :List of 1
  .. ..$ weight: num 0.4
  ..$ baseline: int 30
 $ GPA               :List of 2
  ..$ MrC_GPA:List of 6
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.5-2.7"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.7-2.9"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2.9-3.2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.2-3.5"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "3.5-4"
  .. .. ..$ value: int 0
  ..$ MrU_GPA:List of 1
  .. ..$ weight: num 1.6
 $ TOEFL             :List of 3
  ..$ baseline : int 380
  ..$ MrC_TOEFL:List of 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "621-677"
  .. .. ..$ value: int 5
  .. ..$ :List of 2
  .. .. ..$ level: chr "561-620"
  .. .. ..$ value: int 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "441-560"
  .. .. ..$ value: int 3
  .. ..$ :List of 2
  .. .. ..$ level: chr "381-440"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "≤ 380"
  .. .. ..$ value: int 1
  ..$ MrU_TOEFL:List of 1
  .. ..$ weight: num 0.6
 $ Domisili          :List of 1
  ..$ MrU_Domisil:List of 1
  .. ..$ weight: int 0
 $ Industry          :List of 1
  ..$ MrU_Industry:List of 1
  .. ..$ weight: int 0
 $ Education         :List of 2
  ..$ MrC_Education:List of 8
  .. ..$ :List of 2
  .. .. ..$ level: chr "SMA/SMK"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D3"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "D4"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S1"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S2"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "S3"
  .. .. ..$ value: int 0
  ..$ MrU_Education:List of 1
  .. ..$ weight: int 0
 $ Skillntools       :List of 1
  ..$ MrU_Skillntools:List of 1
  .. ..$ weight: int 0
 $ Expected_Salary   :List of 1
  ..$ MrU_ES:List of 1
  .. ..$ weight: int 0
 $ Working_Experience:List of 2
  ..$ MrC_WE:List of 4
  .. ..$ :List of 2
  .. .. ..$ level: chr "0-2 tahun"
  .. .. ..$ value: int 0
  .. ..$ :List of 2
  .. .. ..$ level: chr "2-5 tahun"
  .. .. ..$ value: int 1
  .. ..$ :List of 2
  .. .. ..$ level: chr "7-12 tahun"
  .. .. ..$ value: int 2
  .. ..$ :List of 2
  .. .. ..$ level: chr "10-15 tahun"
  .. .. ..$ value: int 3
  ..$ MrU_WE:List of 1
  .. ..$ weight: num 1.4
NULL
[1] "JSON Check Class"
[1] "numeric"
[1] "Run the tests"

Test 1:
Success: TRUE 
Value: 2.9-3.2 
Error: None 
Message: Successfully found non-zero value 

Test 2:
Success: FALSE 
Value: NA 
Error: None 
Message: Path not found in data structure 

Test 3:
Success: FALSE 
Value: NA 
Error: Invalid input: data_list must be a valid list 
Message: None 
[1] "Print GPA Base Line"
[1] "2.9-3.2"
[1] NA
[1] 30
[1] 380
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
          Applied Date
1  2024-12-13 03:09:43
2  2024-12-31 06:15:24
3  2024-12-14 18:06:43
4  2024-12-17 15:35:33
5  2024-12-13 02:55:54
6  2024-12-15 15:56:59
7  2024-12-16 04:38:38
8  2024-12-16 04:26:55
9  2024-12-16 06:01:16
10 2024-12-16 05:43:41
11 2024-12-16 05:20:31
12 2024-12-30 20:12:10
13 2024-12-31 04:36:23
14 2024-12-27 08:06:36
15 2024-12-31 05:17:42
16 2024-12-30 03:41:59
17 2024-12-31 04:00:11
                                                                                         Role
1  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
2  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
3  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
4  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
5  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
6  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
7  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
8  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
9  Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
10 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
11 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
12 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
13 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
14 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
15 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
16 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
17 Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)
                        Email User ID             Fullname
1  <EMAIL>      38      Putri Rosalinda
2    <EMAIL>      37        Estelle Darcy
3          <EMAIL>      40 Imelda Putri Azzahra
4     <EMAIL>       3                 test
5    <EMAIL>      36             John Doe
6     <EMAIL>      43          Luna Thomas
7    <EMAIL>      45                 test
8     <EMAIL>      44          Nadia Omara
9     <EMAIL>      47        BENJAMIN SHAH
10    <EMAIL>      48         ARIF BUDIMAN
11    <EMAIL>      46    Sebastian Bennett
12    <EMAIL>      50         ARIF BUDIMAN
13   <EMAIL>      51                 test
14 <EMAIL>      64              ANDI M.
15   <EMAIL>      61         ARIF BUDIMAN
16 <EMAIL>      65            BUDIYANTO
17   <EMAIL>      49                 test
                              Kota - Kab Provinces Degree    GPA
1                                jakarta      <NA>     D3   4.00
2             123 Anywhere St., Any City      <NA>     S1   3.55
3                                   <NA>      <NA>     S3   4.00
4                                   <NA>      <NA>   <NA>   0.00
5                          New York, USA      <NA>     S1   3.50
6              San Francisco, California      <NA>     S1   4.00
7                                   <NA>      <NA>     D3   0.00
8                 Anywhere St., Any City      <NA>     S1  99.00
9    Kabupaten Banggai - Sulawesi Tengah      <NA>     S1  98.00
10           Kalimantan Utara, Indonesia      <NA>     S1  99.00
11            123 Anywhere St., Any City      <NA>     S1 100.00
12 Kabupaten Bulungan - Kalimantan Utara      <NA>     S1   3.55
13           Kabupaten Aceh Besar - Aceh      <NA>     D3   3.55
14                    Pati - Jawa Tengah      <NA>     S1   4.00
15           Kabupaten Aceh Barat - Aceh      <NA>     S1   3.55
16 Kabupaten Banjar - Kalimantan Selatan      <NA>     S1   3.55
17           Kabupaten Aceh Barat - Aceh      <NA>     S1   3.55
                                Major                Institution Degree 2nd
1                    Teknik Metalurgi         Aalborg University       <NA>
2                        Administrasi      Universitas Indonesia       <NA>
3                        Administrasi    Stikes Mitra Ria Husada       <NA>
4  Administrasi Asuransi dan Aktuaria     Aberystwyth University       <NA>
5                    Computer Science   University Of Technology       <NA>
6                        Administrasi   Universitas Cenderawasih       <NA>
7                        Administrasi           Aalto University       <NA>
8                             Ekonomi     Universitas Hasanuddin       <NA>
9                 Manajemen Pemasaran  Universitas Sam Ratulangi       <NA>
10                              Hukum Universitas Borneo Tarakan       <NA>
11                      Ilmu Komputer     Universitas Mulawarman       <NA>
12                            Ekonomi      Universitas Halmahera       <NA>
13          Administriasi Perkantoran         Aalborg University       <NA>
14                Process Engineering     Engineering University       <NA>
15                Process Engineering     Engineering University       <NA>
16  Agribisnis Perikanan dan Kelautan    Universitas Gadjah Mada       <NA>
17  Agribisnis Perikanan dan Kelautan    Universitas Gadjah Mada       <NA>
   GPA 2nd Major 2nd Institution 2nd                 Job Role          YoE
1       NA      <NA>            <NA>        digital marketing 1.319452e-01
2       NA      <NA>            <NA>              ux engineer 3.831659e+00
3       NA      <NA>            <NA>             data science 8.219188e-02
4       NA      <NA>            <NA>            fraud analyst 6.352638e-01
5       NA      <NA>            <NA>        software engineer 2.505632e+00
6       NA      <NA>            <NA>          product manager 8.672755e+00
7       NA      <NA>            <NA>             data science 4.030315e-08
8       NA      <NA>            <NA>              ux designer 2.001522e+00
9       NA      <NA>            <NA>    mechatronics engineer 4.670015e+00
10      NA      <NA>            <NA>              ux engineer 2.001522e+00
11      NA      <NA>            <NA> junior digital marketing 5.004262e+00
12      NA      <NA>            <NA>              ux designer 3.831659e+00
13      NA      <NA>            <NA>        digital marketing 1.536552e+00
14      NA      <NA>            <NA>              ux designer 3.831659e+00
15      NA      <NA>            <NA>              ux designer 3.831659e+00
16      NA      <NA>            <NA>              ux designer 3.831659e+00
17      NA      <NA>            <NA>             data science 1.252055e+00
   Experience
1           0
2           0
3           0
4           0
5           0
6           0
7           0
8           0
9           0
10          0
11          0
12          0
13          0
14          0
15          0
16          0
17          0
                                                                                                                                                                       Skill & Tools
1                                                                                                                                                         {"Microsoft Excel",Python}
2                                                  {Automation,"Efficiency Improvement","Project Management","Robotic Control","System Optimization",Testing,"UX Design",Validation}
3                                                                                                     {Canva,"layanan pelanggan","Microsoft Excel","Ms. Access",Tableau,Ubersuggest}
4                                                                                                                                                                              {Git}
5                                                                                                                                            {CSS,HTML,java,Javascript,MySQL,Spring}
6  {"Cross-functional Team Leadership","Customer Feedback Analysis","Financial Data Integration","Market analysis","Payment Processing","Product Strategy","User Experience Design"}
7                                                                                                                                                                              {Git}
8                                                                                                                        {Automation,"Process Improvement","Skill name","UX Design"}
9                                                                       {Automation,"Control Systems","Feasibility Studies",Mechatronics,"Project Management",Robotics,"Skill name"}
10                                                                                                                           {"Automation Systems","Project Management","UX Design"}
11                                                                                                                                                                     {"Tool name"}
12                                                                                                         {"Project Management","Robotic Control Systems","Skill name","UX Design"}
13                                                                                                                                                                               {R}
14                                                                                                           {"Project Management","Skill name","System Design",Testing,"UX Design"}
15                                              {Automation,"Process Improvement","Robotic Control Systems","Skill name","System Optimization","Testing and Validation","UX Design"}
16                                      {"Automation Systems","Project Management","Robotic Control System","Skill name","Technical Expertise","Testing and Validation","UX Design"}
17                                                                                                                                                                               {R}
                                                                                                                                                                 CV Upload
1  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Surat_Penawaran_Rekrutmen_Mandiri__POS_Indonesia_2024_1-cb732516-ca7a-4d84-b8ec-55bb148dab32.pdf
2                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-4862907d-64b4-4308-b595-2de1d29f3ddb.pdf
3               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FAQ_Toyota_Internship_Program_Batch_1_2025-2b6f728c-1b48-4669-986e-fd80bb2638dc.pdf
4                  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/sample193c6b73d1e76431faaa9f02c70e088e3-2eb47de6-c42a-4b21-af48-e56a29fe79cb.pdf
5                                        https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/20241121ZAPReport-39620822-8212-4977-be78-9dbf1765f6d2.pdf
6                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_1-27f398ca-6207-496d-aca6-3769a969611d.pdf
7                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/dummy-2a85a707-f6a7-49b7-9da4-688fe8889a20.pdf
8                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-9cc01668-1617-4499-96bf-a16f247d2162.pdf
9                                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_4-69a5af64-5a61-45da-a4e1-20493492218b.pdf
10                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-4a9c1324-6c58-46e7-858f-d6969baed5ef.pdf
11                                              https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_3-3667fdc1-4e01-4bba-bd0c-138aa1379645.pdf
12                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-981fe9b0-e1ce-4a6a-9679-f044823064d0.pdf
13                                                   https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/dummy-2550e4e4-e845-470e-8d3a-b4f7cd731c6f.pdf
14                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-457df4ce-a165-4c40-ab0e-3ddbdcf23543.pdf
15                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-e8c5cee0-e0fb-44e1-bdcf-f1d4a28caa73.pdf
16                                                    https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_8-1ac87e96-38ac-41bb-a4b4-432befa58317.pdf
17                https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Nota_All_Group__Program_Reward_MSJD_2023-4d4e5f86-7dab-4bc1-9b39-f12db080d6cb.pdf
                        Linkedin Link                  Portfolio Link
1  https://www.linkedin.com/in/aksjas              https://aksjaks.co
2                                <NA> https://www.reallygreatsite.com
3                                                                    
4                                <NA>                            <NA>
5                                <NA>                            <NA>
6                                             https://www.enhancv.com
7                                <NA>                            <NA>
8                                <NA>                                
9                                <NA>                                
10                                                                   
11                               <NA>                            <NA>
12                                                                   
13                               <NA>                            <NA>
14                                                                   
15                                                                   
16                                                                   
17                               <NA>                            <NA>
   Instagram Link Twitter Link                State Status Availability
1            <NA>         <NA>             rejected        open_to_work
2            <NA>         <NA>              applied        open_to_work
3            <NA>         <NA>              applied        open_to_work
4            <NA>         <NA>             rejected        open_to_work
5            <NA>         <NA>             rejected        open_to_work
6            <NA>         <NA>              applied        open_to_work
7            <NA>         <NA>              applied        open_to_work
8            <NA>         <NA>              applied        open_to_work
9            <NA>         <NA>     wawancara_online        open_to_work
10           <NA>         <NA>      medical_checkup        open_to_work
11           <NA>         <NA>             offering        open_to_work
12           <NA>         <NA>              applied        open_to_work
13           <NA>         <NA>              applied        open_to_work
14           <NA>         <NA> tes_potensi_online_2        open_to_work
15           <NA>         <NA>              applied        open_to_work
16           <NA>         <NA>              applied        open_to_work
17           <NA>         <NA>              applied        open_to_work
                   dob  pob  ktp                     address              hp
1  1996-02-01 02:59:04 <NA> <NA>                     Jakarta  +6281213363001
2  2000-12-01 09:58:01 <NA> <NA>  123 Anywhere St., Any City +62123456781234
3  1999-02-01 17:59:01 <NA> <NA>                        <NA>   +628197608102
4  2024-12-04 09:27:04 <NA> <NA>                        <NA>     +**********
5  2000-12-06 02:55:13 <NA> <NA>               New York, USA     +1234567890
6  2000-12-04 03:13:38 <NA> <NA>   San Francisco, California +**********1111
7  2024-12-05 04:33:29 <NA> <NA>                        <NA>     +6282151216
8  2000-12-01 03:48:25 <NA> <NA>      Anywhere St., Any City   +**********11
9  2002-12-09 05:55:40 <NA> <NA>  123 Anywhere St., Any City   +621234567890
10 2001-12-06 05:40:22 <NA> <NA> Kalimantan Utara, Indonesia +62184147817481
11 2000-12-01 05:14:52 <NA> <NA>  123 Anywhere St., Any City     +1234567890
12 2002-04-01 07:15:25 <NA> <NA> Kalimantan Utara, Indonesia +62718942146141
13 2024-12-04 08:25:04 <NA> <NA>                        <NA>     +6282352362
14 1982-12-30 17:00:00 <NA> <NA>          Pati - Jawa Tengah   +629183681631
15 2024-12-12 02:29:23 <NA> <NA> Kalimantan Utara, Indonesia  +6249275892525
16 2000-12-04 15:51:17 <NA> <NA> Kalimantan Utara, Indonesia    +62813132131
17 1999-01-06 03:55:11 <NA> <NA>                        <NA>    +62832632626
   gender  dcp current_max current_min expect_max expect_min job_vacancy_id
1  female <NA>           0        <NA>   20000000     100000              8
2  female <NA>           0        <NA>          2          1              8
3  female <NA>           0        <NA>   30000000   10000000              8
4    male <NA>           0        <NA>          2          1              8
5  female <NA>           0        <NA>          2          1              8
6  female <NA>           0        <NA>          2          1              8
7    male <NA>           0        <NA>       2000       1000              8
8  female <NA>           0        <NA>          2          1              8
9    male <NA>           0        <NA>          2          1              8
10   male <NA>           0        <NA>          2          1              8
11   male <NA>           0        <NA>          2          1              8
12   male <NA>           0        <NA>          2          1              8
13   male <NA>           0        <NA>       2000       1000              8
14   male <NA>           0        <NA>          2          1              8
15   male <NA>           0        <NA>          2          1              8
16   male <NA>           0        <NA>          2          1              8
17 female <NA>           0        <NA>          2          1              8
   user_job_vacancy_id Calculated YoE
1                    2              0
2                  113              3
3                    3              0
4                   12              0
5                    1              2
6                    4              8
7                    6              0
8                    5              1
9                    9              4
10                   8              1
11                   7              4
12                 109              3
13                 111              1
14                  67              3
15                 112              3
16                 108              3
17                 110              1
'data.frame':	17 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-13 03:09:43" "2024-12-31 06:15:24" ...
 $ Role               : chr  "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" "Supervisor Kantor Cabang di wilayah Regional VI (Kalimantan, Sulawesi, Kep. Maluku, Papua)" ...
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>" ...
 $ User ID            :integer64 38 37 40 3 36 43 45 44 ... 
 $ Fullname           : chr  "Putri Rosalinda" "Estelle Darcy" "Imelda Putri Azzahra" "test" ...
 $ Kota - Kab         : chr  "jakarta" "123 Anywhere St., Any City" NA NA ...
 $ Provinces          : chr  NA NA NA NA ...
 $ Degree             : chr  "D3" "S1" "S3" NA ...
 $ GPA                : num  4 3.55 4 0 3.5 4 0 99 98 99 ...
 $ Major              : chr  "Teknik Metalurgi" "Administrasi" "Administrasi" "Administrasi Asuransi dan Aktuaria" ...
 $ Institution        : chr  "Aalborg University" "Universitas Indonesia" "Stikes Mitra Ria Husada" "Aberystwyth University" ...
 $ Degree 2nd         : chr  NA NA NA NA ...
 $ GPA 2nd            : num  NA NA NA NA NA NA NA NA NA NA ...
 $ Major 2nd          : chr  NA NA NA NA ...
 $ Institution 2nd    : chr  NA NA NA NA ...
 $ Job Role           : chr  "digital marketing" "ux engineer" "data science" "fraud analyst" ...
 $ YoE                : num  0.1319 3.8317 0.0822 0.6353 2.5056 ...
 $ Experience         : int  0 0 0 0 0 0 0 0 0 0 ...
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Microsoft Excel\",Python}" "{Automation,\"Efficiency Improvement\",\"Project Management\",\"Robotic Control\",\"System Optimization\",Testi"| __truncated__ "{Canva,\"layanan pelanggan\",\"Microsoft Excel\",\"Ms. Access\",Tableau,Ubersuggest}" "{Git}" ...
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/Surat_Penawaran_Rekrutmen_Mandiri__POS_I"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/cv_dummy_2-4862907d-64b4-4308-b595-2de1d29f3ddb.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/FAQ_Toyota_Internship_Program_Batch_1_20"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/posindonesia/files/sample193c6b73d1e76431faaa9f02c70e088e3-"| __truncated__ ...
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/aksjas" NA "" NA ...
 $ Portfolio Link     : chr  "https://aksjaks.co" "https://www.reallygreatsite.com" "" NA ...
 $ Instagram Link     : chr  NA NA NA NA ...
 $ Twitter Link       : chr  NA NA NA NA ...
 $ State              : chr  "rejected" "applied" "applied" "rejected" ...
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work" "open_to_work" ...
 $ dob                : POSIXct, format: "1996-02-01 02:59:04" "2000-12-01 09:58:01" ...
 $ pob                : chr  NA NA NA NA ...
 $ ktp                : chr  NA NA NA NA ...
 $ address            : chr  "Jakarta" "123 Anywhere St., Any City" NA NA ...
 $ hp                 : chr  "+6281213363001" "+62123456781234" "+628197608102" "+**********" ...
 $ gender             : chr  "female" "female" "female" "male" ...
 $ dcp                : chr  NA NA NA NA ...
 $ current_max        :integer64 0 0 0 0 0 0 0 0 ... 
 $ current_min        :integer64 NA NA NA NA NA NA NA NA ... 
 $ expect_max         :integer64 20000000 2 30000000 2 2 2 2000 2 ... 
 $ expect_min         :integer64 100000 1 10000000 1 1 1 1000 1 ... 
 $ job_vacancy_id     :integer64 8 8 8 8 8 8 8 8 ... 
 $ user_job_vacancy_id:integer64 2 113 3 12 1 4 6 5 ... 
 $ Calculated YoE     : int  0 3 0 0 2 8 0 1 4 1 ...
          Applied Date User ID             Fullname Job Role ID
1  2024-12-13 03:09:43      38      Putri Rosalinda           2
2  2024-12-31 06:15:24      37        Estelle Darcy         859
3  2024-12-31 06:15:24      37        Estelle Darcy         860
4  2024-12-14 18:06:43      40 Imelda Putri Azzahra           1
5  2024-12-17 15:35:33       3                 test           3
6  2024-12-13 02:55:54      36             John Doe          17
7  2024-12-15 15:56:59      43          Luna Thomas         205
8  2024-12-15 15:56:59      43          Luna Thomas         865
9  2024-12-15 15:56:59      43          Luna Thomas          78
10 2024-12-16 04:38:38      45                 test           1
11 2024-12-16 04:26:55      44          Nadia Omara          87
12 2024-12-16 06:01:16      47        BENJAMIN SHAH         868
13 2024-12-16 06:01:16      47        BENJAMIN SHAH         869
14 2024-12-16 06:01:16      47        BENJAMIN SHAH         643
15 2024-12-16 05:43:41      48         ARIF BUDIMAN         860
16 2024-12-16 05:20:31      46    Sebastian Bennett         866
17 2024-12-30 20:12:10      50         ARIF BUDIMAN          87
18 2024-12-30 20:12:10      50         ARIF BUDIMAN         859
19 2024-12-31 04:36:23      51                 test           2
20 2024-12-27 08:06:36      64              ANDI M.          87
21 2024-12-27 08:06:36      64              ANDI M.         859
22 2024-12-31 05:17:42      61         ARIF BUDIMAN          87
23 2024-12-31 05:17:42      61         ARIF BUDIMAN         859
24 2024-12-30 03:41:59      65            BUDIYANTO          87
25 2024-12-30 03:41:59      65            BUDIYANTO         859
26 2024-12-31 04:00:11      49                 test           1
                    Job Role   industry               company_name work_type
1          digital marketing Technology                     jajaja full_time
2         system ux engineer       <NA>        XarrowAI Industries full_time
3                ux engineer       <NA>                   Morcelle full_time
4               data science Technology                        abc full_time
5              fraud analyst    Finance                          a  contract
6          software engineer       <NA>        Tech Solutions Inc. full_time
7  associate product manager       <NA>                     Stripe full_time
8     senior product manager       <NA>                      Plaid full_time
9            product manager       <NA>                      Plaid full_time
10              data science Technology                       test part_time
11               ux designer       <NA>                   Morcelle full_time
12     mechatronics engineer       <NA>      Borcelle Technologies full_time
13   junior project engineer       <NA> Salford & Co Manufacturing full_time
14           system engineer       <NA>         Arrowai Industries full_time
15               ux engineer       <NA>                   Morcelle full_time
16  junior digital marketing       <NA>            Ingoude Company full_time
17               ux designer       <NA>                   Morcelle full_time
18        system ux engineer       <NA>        XarrowAI Industries full_time
19         digital marketing    Finance                       test full_time
20               ux designer       <NA>                   Morcelle full_time
21        system ux engineer       <NA>        XarrowAI Industries full_time
22               ux designer       <NA>                   Morcelle full_time
23        system ux engineer       <NA>        XarrowAI Industries full_time
24               ux designer       <NA>                   Morcelle full_time
25        system ux engineer       <NA>        XarrowAI Industries full_time
26              data science Technology                      12312 full_time
               ends_at           starts_at          MoE
1                 <NA> 2024-11-13 02:29:42 1.605334e+00
2  2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
3                 <NA> 2022-12-31 17:00:00 2.435185e+01
4  2024-12-14 17:53:20 2024-11-14 17:53:16 1.000001e+00
5                 <NA> 2024-05-13 09:25:28 7.729043e+00
6                 <NA> 2022-06-30 17:00:00 3.048519e+01
7  2018-04-30 17:00:00 2016-02-29 17:00:00 2.636667e+01
8  2020-11-30 17:00:00 2018-05-31 17:00:00 3.046667e+01
9                 <NA> 2020-12-31 17:00:00 4.868519e+01
10 2024-02-16 04:33:09 2024-02-16 04:33:07 4.903549e-07
11                <NA> 2022-12-31 17:00:00 2.435185e+01
12                <NA> 2022-12-31 17:00:00 2.435185e+01
13 2020-12-31 17:00:00 2020-02-29 17:00:00 1.020000e+01
14 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
15                <NA> 2022-12-31 17:00:00 2.435185e+01
16                <NA> 2019-12-31 17:00:00 6.088519e+01
17                <NA> 2022-12-31 17:00:00 2.435185e+01
18 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
19 2024-08-31 04:33:10 2023-02-17 08:21:41 1.869471e+01
20                <NA> 2022-12-31 17:00:00 2.435185e+01
21 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
22                <NA> 2022-12-31 17:00:00 2.435185e+01
23 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
24                <NA> 2022-12-31 17:00:00 2.435185e+01
25 2022-11-30 17:00:00 2021-01-31 17:00:00 2.226667e+01
26 2023-05-31 03:52:04 2022-02-28 03:51:57 1.523334e+01
'data.frame':	26 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-13 03:09:43" "2024-12-31 06:15:24" ...
 $ User ID     :integer64 38 37 37 40 3 36 43 43 ... 
 $ Fullname    : chr  "Putri Rosalinda" "Estelle Darcy" "Estelle Darcy" "Imelda Putri Azzahra" ...
 $ Job Role ID :integer64 2 859 860 1 3 17 205 865 ... 
 $ Job Role    : chr  "digital marketing" "system ux engineer" "ux engineer" "data science" ...
 $ industry    : chr  "Technology" NA NA "Technology" ...
 $ company_name: chr  "jajaja" "XarrowAI Industries" "Morcelle" "abc" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: NA "2022-11-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2024-11-13 02:29:42" "2021-01-31 17:00:00" ...
 $ MoE         : num  1.61 22.27 24.35 1 7.73 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning: There was 1 warning in `mutate()`.
ℹ In argument: `dob = dmy_hm(dob)`.
Caused by warning:
! All formats failed to parse. No formats found.
[1] "TOEFL Score Data"
Major is not configured. Skipping major matching.
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	17 obs. of  19 variables:
 $ Candidate          : chr  "Putri Rosalinda" "Estelle Darcy" "Imelda Putri Azzahra" "test" ...
 $ Education          : chr  "D3" "S1" "S3" NA ...
 $ Institution        : chr  "Aalborg University" "Universitas Indonesia" "Stikes Mitra Ria Husada" "Aberystwyth University" ...
 $ Experience         : num  0 3 0 0 2 8 0 1 4 1 ...
 $ GPA                : num  4 3.55 4 0 3.5 4 0 99 98 99 ...
 $ Domisili           : chr  "jakarta" "123 Anywhere St., Any City" NA NA ...
 $ Expected_Salary    :integer64 100000 1 10000000 1 1 1 1000 1 ... 
 $ Industry           : chr  "{\"Technology\"}" "{\"\"}" "{\"Technology\"}" "{\"Finance\"}" ...
 $ Skillntools        : 'pq__varchar' chr  "{\"Microsoft Excel\",Python}" "{Automation,\"Efficiency Improvement\",\"Project Management\",\"Robotic Control\",\"System Optimization\",Testi"| __truncated__ "{Canva,\"layanan pelanggan\",\"Microsoft Excel\",\"Ms. Access\",Tableau,Ubersuggest}" "{Git}" ...
 $ Major              : chr  "Teknik Metalurgi" "Administrasi" "Administrasi" "Administrasi Asuransi dan Aktuaria" ...
 $ user_job_vacancy_id:integer64 2 113 3 12 1 4 6 5 ... 
 $ dob                : POSIXct, format: NA NA ...
 $ age                : num  NA NA NA NA NA NA NA NA NA NA ...
 $ toefl_score        : num  NA 555 700 1 NA 450 67 430 450 400 ...
 $ edu_matched        : logi  TRUE FALSE FALSE NA FALSE FALSE ...
 $ major_match        : logi  NA NA NA NA NA NA ...
 $ edu_major_weight   : logi  NA NA NA NA NA NA ...
 $ age_level          : num  0 0 0 0 0 0 0 0 0 0 ...
 $ toefl_level        : num  0 3 0 1 0 3 1 2 3 2 ...
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] "D3"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] "0-3 tahun"
Warning: `data_frame()` was deprecated in tibble 1.1.0.
ℹ Please use `tibble()` instead.
[1] "0-3 tahun"
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          1
6 S1          2
7 S2          3
8 S3          4


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 1.4


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       1
3 6-11 tahun      2
4 11-99 tahun     3
5 0-99 tahun      4


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 1.6


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     1
6 3.5-4       2


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


$Age
$Age$MrU_Age
[1] 0.4


$Toefl
$Toefl$MrU_Toefl
[1] 0.6


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 1.4

$weight_gpa
[1] 1.6

$weight_domisili
[1] 0

$weight_es
[1] 0

$weight_industry
[1] 0

$weight_skill
[1] 0

$weight_education_major
[1] 0

$weight_age
[1] 0.4

$weight_toefl
[1] 0.6

[1] "Scaled value"
$weight_we
[1] 35

$weight_gpa
[1] 40

$weight_age
[1] 10

$weight_toefl
[1] 15

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.5"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 98"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 100"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate domisili: jakarta"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: New York, USA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: San Francisco, California"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banggai - Sulawesi Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kalimantan Utara, Indonesia"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Bulungan - Kalimantan Utara"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Besar - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Pati - Jawa Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banjar - Kalimantan Selatan"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.5"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 98"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 100"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.5"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 98"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 99"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 100"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 4"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: 2.9-3.2"
[1] "Candidate domisili: jakarta"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: New York, USA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: San Francisco, California"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banggai - Sulawesi Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kalimantan Utara, Indonesia"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Bulungan - Kalimantan Utara"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Besar - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Pati - Jawa Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banjar - Kalimantan Selatan"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: jakarta"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: New York, USA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: San Francisco, California"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: NA"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banggai - Sulawesi Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kalimantan Utara, Indonesia"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: 123 Anywhere St., Any City"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Bulungan - Kalimantan Utara"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Besar - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Pati - Jawa Tengah"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Banjar - Kalimantan Selatan"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Candidate domisili: Kabupaten Aceh Barat - Aceh"
[1] "Base line: Kabupaten Aceh Barat - Aceh"
character(0)
[1] "Test New Create List Matching Criteria"
[1] "Done New Calculation"
[1] "Test New Code Done"
              Candidate Education                Institution Experience    GPA
1       Putri Rosalinda        D3         Aalborg University          0   4.00
2         Estelle Darcy        S1      Universitas Indonesia          3   3.55
3  Imelda Putri Azzahra        S3    Stikes Mitra Ria Husada          0   4.00
4                  test      <NA>     Aberystwyth University          0   0.00
5              John Doe        S1   University Of Technology          2   3.50
6           Luna Thomas        S1   Universitas Cenderawasih          8   4.00
7                  test        D3           Aalto University          0   0.00
8           Nadia Omara        S1     Universitas Hasanuddin          1  99.00
9         BENJAMIN SHAH        S1  Universitas Sam Ratulangi          4  98.00
10         ARIF BUDIMAN        S1 Universitas Borneo Tarakan          1  99.00
11    Sebastian Bennett        S1     Universitas Mulawarman          4 100.00
12         ARIF BUDIMAN        S1      Universitas Halmahera          3   3.55
13                 test        D3         Aalborg University          1   3.55
14              ANDI M.        S1     Engineering University          3   4.00
15         ARIF BUDIMAN        S1     Engineering University          3   3.55
16            BUDIYANTO        S1    Universitas Gadjah Mada          3   3.55
17                 test        S1    Universitas Gadjah Mada          1   3.55
                                Domisili Expected_Salary       Industry
1                                jakarta          100000 {"Technology"}
2             123 Anywhere St., Any City               1           {""}
3                                   <NA>        10000000 {"Technology"}
4                                   <NA>               1    {"Finance"}
5                          New York, USA               1           {""}
6              San Francisco, California               1           {""}
7                                   <NA>            1000 {"Technology"}
8                 Anywhere St., Any City               1           {""}
9    Kabupaten Banggai - Sulawesi Tengah               1           {""}
10           Kalimantan Utara, Indonesia               1           {""}
11            123 Anywhere St., Any City               1           {""}
12 Kabupaten Bulungan - Kalimantan Utara               1           {""}
13           Kabupaten Aceh Besar - Aceh            1000    {"Finance"}
14                    Pati - Jawa Tengah               1           {""}
15           Kabupaten Aceh Barat - Aceh               1           {""}
16 Kabupaten Banjar - Kalimantan Selatan               1           {""}
17           Kabupaten Aceh Barat - Aceh               1 {"Technology"}
                                                                                                                                                                         Skillntools
1                                                                                                                                                         {"Microsoft Excel",Python}
2                                                  {Automation,"Efficiency Improvement","Project Management","Robotic Control","System Optimization",Testing,"UX Design",Validation}
3                                                                                                     {Canva,"layanan pelanggan","Microsoft Excel","Ms. Access",Tableau,Ubersuggest}
4                                                                                                                                                                              {Git}
5                                                                                                                                            {CSS,HTML,java,Javascript,MySQL,Spring}
6  {"Cross-functional Team Leadership","Customer Feedback Analysis","Financial Data Integration","Market analysis","Payment Processing","Product Strategy","User Experience Design"}
7                                                                                                                                                                              {Git}
8                                                                                                                        {Automation,"Process Improvement","Skill name","UX Design"}
9                                                                       {Automation,"Control Systems","Feasibility Studies",Mechatronics,"Project Management",Robotics,"Skill name"}
10                                                                                                                           {"Automation Systems","Project Management","UX Design"}
11                                                                                                                                                                     {"Tool name"}
12                                                                                                         {"Project Management","Robotic Control Systems","Skill name","UX Design"}
13                                                                                                                                                                               {R}
14                                                                                                           {"Project Management","Skill name","System Design",Testing,"UX Design"}
15                                              {Automation,"Process Improvement","Robotic Control Systems","Skill name","System Optimization","Testing and Validation","UX Design"}
16                                      {"Automation Systems","Project Management","Robotic Control System","Skill name","Technical Expertise","Testing and Validation","UX Design"}
17                                                                                                                                                                               {R}
                                Major user_job_vacancy_id  dob age toefl_score
1                    Teknik Metalurgi                   2 <NA>  NA          NA
2                        Administrasi                 113 <NA>  NA         555
3                        Administrasi                   3 <NA>  NA         700
4  Administrasi Asuransi dan Aktuaria                  12 <NA>  NA           1
5                    Computer Science                   1 <NA>  NA          NA
6                        Administrasi                   4 <NA>  NA         450
7                        Administrasi                   6 <NA>  NA          67
8                             Ekonomi                   5 <NA>  NA         430
9                 Manajemen Pemasaran                   9 <NA>  NA         450
10                              Hukum                   8 <NA>  NA         400
11                      Ilmu Komputer                   7 <NA>  NA         400
12                            Ekonomi                 109 <NA>  NA         555
13          Administriasi Perkantoran                 111 <NA>  NA         555
14                Process Engineering                  67 <NA>  NA         242
15                Process Engineering                 112 <NA>  NA         555
16  Agribisnis Perikanan dan Kelautan                 108 <NA>  NA         555
17  Agribisnis Perikanan dan Kelautan                 110 <NA>  NA         555
   edu_matched major_match edu_major_weight age_level toefl_level MrU_Score_Age
1         TRUE          NA               NA         0           0             0
2        FALSE          NA               NA         0           3             0
3        FALSE          NA               NA         0           0             0
4           NA          NA               NA         0           1             0
5        FALSE          NA               NA         0           0             0
6        FALSE          NA               NA         0           3             0
7         TRUE          NA               NA         0           1             0
8        FALSE          NA               NA         0           2             0
9        FALSE          NA               NA         0           3             0
10       FALSE          NA               NA         0           2             0
11       FALSE          NA               NA         0           2             0
12       FALSE          NA               NA         0           3             0
13        TRUE          NA               NA         0           3             0
14       FALSE          NA               NA         0           1             0
15       FALSE          NA               NA         0           3             0
16       FALSE          NA               NA         0           3             0
17       FALSE          NA               NA         0           3             0
   MrU_Score_Toefl MrU_Score_Edu MrU_Score_WE MrU_Score_GPA MrU_Score_Domisili
1                0             0           35            40                  0
2                9             0           35            40                  0
3                0             0           35            40                  0
4                3             0           35             0                  0
5                0             0           35            40                  0
6                9             0           35            40                  0
7                3             0           35             0                  0
8                6             0           35             0                  0
9                9             0           35             0                  0
10               6             0           35             0                  0
11               6             0           35             0                  0
12               9             0           35            40                  0
13               9             0           35            40                  0
14               3             0           35            40                  0
15               9             0           35            40                  0
16               9             0           35            40                  0
17               9             0           35            40                  0
   MrU_Score_ES MrU_Score_Industry MrU_Score_Skillntools Match_Item_Edu
1             0                  0                     0       D3, TRUE
2             0                  0                     0       S1, TRUE
3             0                  0                     0       S3, TRUE
4             0                  0                     0      D3, FALSE
5             0                  0                     0       S1, TRUE
6             0                  0                     0       S1, TRUE
7             0                  0                     0       D3, TRUE
8             0                  0                     0       S1, TRUE
9             0                  0                     0       S1, TRUE
10            0                  0                     0       S1, TRUE
11            0                  0                     0       S1, TRUE
12            0                  0                     0       S1, TRUE
13            0                  0                     0       D3, TRUE
14            0                  0                     0       S1, TRUE
15            0                  0                     0       S1, TRUE
16            0                  0                     0       S1, TRUE
17            0                  0                     0       S1, TRUE
   Unmatch_Item_Edu Match_Item_WE Unmatch_Item_WE Match_Item_GPA
1              NULL          0, 1            NULL        4, TRUE
2              NULL          3, 1            NULL     3.55, TRUE
3              NULL          0, 1            NULL        4, TRUE
4              NULL          0, 1            NULL           NULL
5              NULL          2, 1            NULL      3.5, TRUE
6              NULL          8, 1            NULL        4, TRUE
7              NULL          0, 1            NULL           NULL
8              NULL          1, 1            NULL           NULL
9              NULL          4, 1            NULL           NULL
10             NULL          1, 1            NULL           NULL
11             NULL          4, 1            NULL           NULL
12             NULL          3, 1            NULL     3.55, TRUE
13             NULL          1, 1            NULL     3.55, TRUE
14             NULL          3, 1            NULL        4, TRUE
15             NULL          3, 1            NULL     3.55, TRUE
16             NULL          3, 1            NULL     3.55, TRUE
17             NULL          1, 1            NULL     3.55, TRUE
               Unmatch_Item_GPA Match_Item_Domisili Unmatch_Item_Domisili
1                          NULL                NULL                  NULL
2                          NULL                NULL                  NULL
3                          NULL                NULL                  NULL
4                      0, FALSE                NULL                  NULL
5                          NULL                NULL                  NULL
6                          NULL                NULL                  NULL
7                      0, FALSE                NULL                  NULL
8  Invalid candidate GPA, FALSE                NULL                  NULL
9  Invalid candidate GPA, FALSE                NULL                  NULL
10 Invalid candidate GPA, FALSE                NULL                  NULL
11 Invalid candidate GPA, FALSE                NULL                  NULL
12                         NULL                NULL                  NULL
13                         NULL                NULL                  NULL
14                         NULL                NULL                  NULL
15                         NULL                NULL                  NULL
16                         NULL                NULL                  NULL
17                         NULL                NULL                  NULL
   Match_Item_ES Unmatch_Item_ES Match_Item_Industry Unmatch_Item_Industry
1   1 - 2, FALSE    1e+05, 0e+00                NULL                  NULL
2           1, 1            NULL                NULL                  NULL
3   1 - 2, FALSE    1e+07, 0e+00                NULL                  NULL
4           1, 1            NULL                NULL                  NULL
5           1, 1            NULL                NULL                  NULL
6           1, 1            NULL                NULL                  NULL
7   1 - 2, FALSE         1000, 0                NULL                  NULL
8           1, 1            NULL                NULL                  NULL
9           1, 1            NULL                NULL                  NULL
10          1, 1            NULL                NULL                  NULL
11          1, 1            NULL                NULL                  NULL
12          1, 1            NULL                NULL                  NULL
13  1 - 2, FALSE         1000, 0                NULL                  NULL
14          1, 1            NULL                NULL                  NULL
15          1, 1            NULL                NULL                  NULL
16          1, 1            NULL                NULL                  NULL
17          1, 1            NULL                NULL                  NULL
                                                                                    Match_Item_Skillntools
1  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
2  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
3  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
4  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
5  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
6  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
7  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
8  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
9  Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
10 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
11 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
12 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
13 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
14 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
15 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
16 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
17 Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE
                                                                                                                                                                                                   Unmatch_Item_Skillntools
1                                                                                                                                                                                     Microsoft Excel, FALSE, Python, FALSE
2                                      Automation, FALSE, Efficiency Improvement, FALSE, Project Management, FALSE, Robotic Control, FALSE, System Optimization, FALSE, Testing, FALSE, UX Design, FALSE, Validation, FALSE
3                                                                                                     Canva, FALSE, layanan pelanggan, FALSE, Microsoft Excel, FALSE, Ms. Access, FALSE, Tableau, FALSE, Ubersuggest, FALSE
4                                                                                                                                                                                                                Git, FALSE
5                                                                                                                                      CSS, FALSE, HTML, FALSE, java, FALSE, Javascript, FALSE, MySQL, FALSE, Spring, FALSE
6  Cross-functional Team Leadership, FALSE, Customer Feedback Analysis, FALSE, Financial Data Integration, FALSE, Market analysis, FALSE, Payment Processing, FALSE, Product Strategy, FALSE, User Experience Design, FALSE
7                                                                                                                                                                                                                Git, FALSE
8                                                                                                                                        Automation, FALSE, Process Improvement, FALSE, Skill name, FALSE, UX Design, FALSE
9                                                                 Automation, FALSE, Control Systems, FALSE, Feasibility Studies, FALSE, Mechatronics, FALSE, Project Management, FALSE, Robotics, FALSE, Skill name, FALSE
10                                                                                                                                                   Automation Systems, FALSE, Project Management, FALSE, UX Design, FALSE
11                                                                                                                                                                                                         Tool name, FALSE
12                                                                                                                           Project Management, FALSE, Robotic Control Systems, FALSE, Skill name, FALSE, UX Design, FALSE
13                                                                                                                                                                                                                 R, FALSE
14                                                                                                                     Project Management, FALSE, Skill name, FALSE, System Design, FALSE, Testing, FALSE, UX Design, FALSE
15                                            Automation, FALSE, Process Improvement, FALSE, Robotic Control Systems, FALSE, Skill name, FALSE, System Optimization, FALSE, Testing and Validation, FALSE, UX Design, FALSE
16                                      Automation Systems, FALSE, Project Management, FALSE, Robotic Control System, FALSE, Skill name, FALSE, Technical Expertise, FALSE, Testing and Validation, FALSE, UX Design, FALSE
17                                                                                                                                                                                                                 R, FALSE
   Match_Item_Major                        Unmatch_Item_Major Match_Item_Age
1         NA, FALSE                   Teknik Metalurgi, FALSE           NULL
2         NA, FALSE                       Administrasi, FALSE           NULL
3         NA, FALSE                       Administrasi, FALSE           NULL
4         NA, FALSE Administrasi Asuransi dan Aktuaria, FALSE           NULL
5         NA, FALSE                   Computer Science, FALSE           NULL
6         NA, FALSE                       Administrasi, FALSE           NULL
7         NA, FALSE                       Administrasi, FALSE           NULL
8         NA, FALSE                            Ekonomi, FALSE           NULL
9         NA, FALSE                Manajemen Pemasaran, FALSE           NULL
10        NA, FALSE                              Hukum, FALSE           NULL
11        NA, FALSE                      Ilmu Komputer, FALSE           NULL
12        NA, FALSE                            Ekonomi, FALSE           NULL
13        NA, FALSE          Administriasi Perkantoran, FALSE           NULL
14        NA, FALSE                Process Engineering, FALSE           NULL
15        NA, FALSE                Process Engineering, FALSE           NULL
16        NA, FALSE  Agribisnis Perikanan dan Kelautan, FALSE           NULL
17        NA, FALSE  Agribisnis Perikanan dan Kelautan, FALSE           NULL
   Unmatch_Item_Age Match_Item_Toefl  Unmatch_Item_Toefl Total_Score
1  Invalid age data             NULL Invalid TOEFL score          75
2  Invalid age data        555, TRUE                NULL          84
3  Invalid age data             NULL                 700          75
4  Invalid age data          1, TRUE                NULL          38
5  Invalid age data             NULL Invalid TOEFL score          75
6  Invalid age data        450, TRUE                NULL          84
7  Invalid age data         67, TRUE                NULL          38
8  Invalid age data        430, TRUE                NULL          41
9  Invalid age data        450, TRUE                NULL          44
10 Invalid age data        400, TRUE                NULL          41
11 Invalid age data        400, TRUE                NULL          41
12 Invalid age data        555, TRUE                NULL          84
13 Invalid age data        555, TRUE                NULL          84
14 Invalid age data        242, TRUE                NULL          78
15 Invalid age data        555, TRUE                NULL          84
16 Invalid age data        555, TRUE                NULL          84
17 Invalid age data        555, TRUE                NULL          84
                                                                                                                                                                                                                                                                                                                                                                                     matching_criteria
1                                                                                                                                                        1 - 2, FALSE, 1e+05, FALSE, 4, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Microsoft Excel, FALSE, Python, FALSE, 0, TRUE, D3, TRUE, Invalid age data, Invalid TOEFL score
2                                   1, TRUE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation, FALSE, Efficiency Improvement, FALSE, Project Management, FALSE, Robotic Control, FALSE, System Optimization, FALSE, Testing, FALSE, UX Design, FALSE, Validation, FALSE, 3, TRUE, S1, TRUE, Invalid age data, 555, TRUE
3                                                                                        1 - 2, FALSE, 1e+07, FALSE, 4, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Canva, FALSE, layanan pelanggan, FALSE, Microsoft Excel, FALSE, Ms. Access, FALSE, Tableau, FALSE, Ubersuggest, FALSE, 0, TRUE, S3, TRUE, Invalid age data, 700
4                                                                                                                                                                                                                1, TRUE, 0, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Git, FALSE, 0, TRUE, D3, FALSE, Invalid age data, 1, TRUE
5                                                                                                                          1, TRUE, 3.5, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, CSS, FALSE, HTML, FALSE, java, FALSE, Javascript, FALSE, MySQL, FALSE, Spring, FALSE, 2, TRUE, S1, TRUE, Invalid age data, Invalid TOEFL score
6  1, TRUE, 4, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Cross-functional Team Leadership, FALSE, Customer Feedback Analysis, FALSE, Financial Data Integration, FALSE, Market analysis, FALSE, Payment Processing, FALSE, Product Strategy, FALSE, User Experience Design, FALSE, 8, TRUE, S1, TRUE, Invalid age data, 450, TRUE
7                                                                                                                                                                                              1 - 2, FALSE, 1000, FALSE, 0, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Git, FALSE, 0, TRUE, D3, TRUE, Invalid age data, 67, TRUE
8                                                                                                                   1, TRUE, Invalid candidate GPA, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation, FALSE, Process Improvement, FALSE, Skill name, FALSE, UX Design, FALSE, 1, TRUE, S1, TRUE, Invalid age data, 430, TRUE
9                                            1, TRUE, Invalid candidate GPA, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation, FALSE, Control Systems, FALSE, Feasibility Studies, FALSE, Mechatronics, FALSE, Project Management, FALSE, Robotics, FALSE, Skill name, FALSE, 4, TRUE, S1, TRUE, Invalid age data, 450, TRUE
10                                                                                                                              1, TRUE, Invalid candidate GPA, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation Systems, FALSE, Project Management, FALSE, UX Design, FALSE, 1, TRUE, S1, TRUE, Invalid age data, 400, TRUE
11                                                                                                                                                                                    1, TRUE, Invalid candidate GPA, FALSE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Tool name, FALSE, 4, TRUE, S1, TRUE, Invalid age data, 400, TRUE
12                                                                                                                        1, TRUE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Project Management, FALSE, Robotic Control Systems, FALSE, Skill name, FALSE, UX Design, FALSE, 3, TRUE, S1, TRUE, Invalid age data, 555, TRUE
13                                                                                                                                                                                            1 - 2, FALSE, 1000, FALSE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, R, FALSE, 1, TRUE, D3, TRUE, Invalid age data, 555, TRUE
14                                                                                                                     1, TRUE, 4, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Project Management, FALSE, Skill name, FALSE, System Design, FALSE, Testing, FALSE, UX Design, FALSE, 3, TRUE, S1, TRUE, Invalid age data, 242, TRUE
15                                         1, TRUE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation, FALSE, Process Improvement, FALSE, Robotic Control Systems, FALSE, Skill name, FALSE, System Optimization, FALSE, Testing and Validation, FALSE, UX Design, FALSE, 3, TRUE, S1, TRUE, Invalid age data, 555, TRUE
16                                   1, TRUE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, Automation Systems, FALSE, Project Management, FALSE, Robotic Control System, FALSE, Skill name, FALSE, Technical Expertise, FALSE, Testing and Validation, FALSE, UX Design, FALSE, 3, TRUE, S1, TRUE, Invalid age data, 555, TRUE
17                                                                                                                                                                                                              1, TRUE, 3.55, TRUE, Collaborative Working, FALSE, Reporting and Managing Stakeholders, FALSE, Stakeholder Management, FALSE, R, FALSE, 1, TRUE, S1, TRUE, Invalid age data, 555, TRUE
[1] "Cal MrU Done"
[1] "Scale MrC"
# A tibble: 4 × 4
  level       value scale_factor new_value
  <chr>       <dbl>        <dbl>     <dbl>
1 0-3 tahun     0          0.638       0  
2 3-6 tahun    16.7        0.638      10.6
3 6-11 tahun   33.3        0.638      21.3
4 11-99 tahun  50          0.638      31.9
[1] "Scale MrC Done"
[1] "Start MrC New Code"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

Warning: There was 1 warning in `mutate()`.
ℹ In argument: `MrC_results = list(calculate_mrc_scores(cur_data(),
  matchmaking_config))`.
ℹ In row 1.
Caused by warning:
! `cur_data()` was deprecated in dplyr 1.1.0.
ℹ Please use `pick()` instead.
[1] "Done MrC New Code"
[1] "Cal MrC Done"
[1] "Finalized Table"
Rows: 17
Columns: 5
$ user_job_vacancy_id <int64> 4, 113, 109, 112, 108, 110, 111, 67, 3, 2, 1, 9,…
$ main_score          <dbl> 84, 84, 84, 84, 84, 84, 84, 78, 75, 75, 75, 44, 41…
$ additional_score    <dbl> 77, 55, 55, 55, 55, 55, 43, 55, 68, 43, 13, 23, 23…
$ matching_criteria   <named list> [[[[1, TRUE]], []], [[], []], [[], []], [[[…
$ additional_values   <list> [[[], [["S1"]]], [[], [["8 tahun"]]], [[], [["4"]…
# A tibble: 17 × 5
   user_job_vacancy_id main_score additional_score matching_criteria
               <int64>      <dbl>            <dbl> <named list>     
 1                   4         84               77 <named list [10]>
 2                 113         84               55 <named list [10]>
 3                 109         84               55 <named list [10]>
 4                 112         84               55 <named list [10]>
 5                 108         84               55 <named list [10]>
 6                 110         84               55 <named list [10]>
 7                 111         84               43 <named list [10]>
 8                  67         78               55 <named list [10]>
 9                   3         75               68 <named list [10]>
10                   2         75               43 <named list [10]>
11                   1         75               13 <named list [10]>
12                   9         44               23 <named list [10]>
13                   7         41               23 <named list [10]>
14                   5         41               13 <named list [10]>
15                   8         41               13 <named list [10]>
16                  12         38                0 <named list [10]>
17                   6         38                0 <named list [10]>
# ℹ 1 more variable: additional_values <list>
[1] "Finalized Done"
[1] "Write for DB"
