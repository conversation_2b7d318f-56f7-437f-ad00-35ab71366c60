
Attaching package: ‘dplyr’

The following objects are masked from ‘package:stats’:

    filter, lag

The following objects are masked from ‘package:base’:

    intersect, setdiff, setequal, union


Attaching package: ‘lubridate’

The following objects are masked from ‘package:base’:

    date, intersect, setdiff, union


Attaching package: ‘zoo’

The following objects are masked from ‘package:base’:

    as.Date, as.Date.numeric

Running plumber API at http://0.0.0.0:5656
Running swagger Docs at http://127.0.0.1:5656/__docs__/

Attaching package: ‘jsonlite’

The following object is masked from ‘package:purrr’:

    flatten


Attaching package: ‘stringdist’

The following object is masked from ‘package:tidyr’:

    extract

[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 1655
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Painting Quality Intern"
$ id                               <int64> 1655
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "[], ActiveCampaign"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kota Jakarta Utara - DKI Jakarta"
$ job_level                        <chr> NA
$ job_type                         <chr> "intern"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Engineering, Kesehatan Hewan"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 2



$Major
$Major$weight
[1] 0

$Major$baseline
$Major$baseline[[1]]
[1] "Teknik Sipil"

$Major$baseline[[2]]
[1] "Teknik Informatika"



$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 1


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 1


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0.4



$Education_Major
$Education_Major$conditions
$Education_Major$conditions[[1]]
$Education_Major$conditions[[1]]$weight
[1] 0

$Education_Major$conditions[[1]]$major_relevance
[1] "any"

$Education_Major$conditions[[1]]$candidate_education
$Education_Major$conditions[[1]]$candidate_education[[1]]
[1] "D3"



$Education_Major$conditions[[2]]
$Education_Major$conditions[[2]]$weight
[1] 0.5

$Education_Major$conditions[[2]]$major_relevance
[1] "not_related"

$Education_Major$conditions[[2]]$candidate_education
$Education_Major$conditions[[2]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[2]]$candidate_education[[2]]
[1] "D4"



$Education_Major$conditions[[3]]
$Education_Major$conditions[[3]]$weight
[1] 1

$Education_Major$conditions[[3]]$major_relevance
[1] "related"

$Education_Major$conditions[[3]]$candidate_education
$Education_Major$conditions[[3]]$candidate_education[[1]]
[1] "S1"

$Education_Major$conditions[[3]]$candidate_education[[2]]
[1] "D4"




$Education_Major$MrU_Education_Major
$Education_Major$MrU_Education_Major$weight
[1] 1.4



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3


$Working_Experience$MrC_WE[[5]]
$Working_Experience$MrC_WE[[5]]$level
[1] "0-15 tahun"

$Working_Experience$MrC_WE[[5]]$value
[1] 0



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0.2



[1] NA
[[1]]
[1] "Teknik Sipil"

[[2]]
[1] "Teknik Informatika"

[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                    Role                     Email User ID
1 2024-12-23 02:21:15 Painting <NAME_EMAIL>      84
   Fullname                  Kota - Kab Provinces Degree  GPA      Major
1 tigaratus Kabupaten Aceh Barat - Aceh      <NA>     S1 3.55 Accounting
        Institution Degree 2nd GPA 2nd Major 2nd Institution 2nd
1 Aarhus University       <NA>      NA      <NA>            <NA>
           Job Role      YoE Experience Skill & Tools
1 digital marketing 1.719708          0      {Python}
                                                                                                             CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf
  Linkedin Link Portfolio Link Instagram Link Twitter Link   State
1          <NA>           <NA>           <NA>         <NA> applied
  Status Availability  dob  pob  ktp address              hp gender
1        open_to_work <NA> <NA> <NA>    <NA> +**************   male
                         dcp current_max current_min expect_max expect_min
1 tigaratus-p18dsjrj5bhuym65           0           0          0          0
  job_vacancy_id user_job_vacancy_id Calculated YoE
1           1655                  33              1
'data.frame':	1 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 02:21:15"
 $ Role               : chr "Painting Quality Intern"
 $ Email              : chr "<EMAIL>"
 $ User ID            :integer64 84 
 $ Fullname           : chr "tigaratus"
 $ Kota - Kab         : chr "Kabupaten Aceh Barat - Aceh"
 $ Provinces          : chr NA
 $ Degree             : chr "S1"
 $ GPA                : num 3.55
 $ Major              : chr "Accounting"
 $ Institution        : chr "Aarhus University"
 $ Degree 2nd         : chr NA
 $ GPA 2nd            : num NA
 $ Major 2nd          : chr NA
 $ Institution 2nd    : chr NA
 $ Job Role           : chr "digital marketing"
 $ YoE                : num 1.72
 $ Experience         : int 0
 $ Skill & Tools      : 'pq__varchar' chr "{Python}"
 $ CV Upload          : chr "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf"
 $ Linkedin Link      : chr NA
 $ Portfolio Link     : chr NA
 $ Instagram Link     : chr NA
 $ Twitter Link       : chr NA
 $ State              : chr "applied"
 $ Status Availability: 'pq_status_availability' chr "open_to_work"
 $ dob                : POSIXct, format: NA
 $ pob                : chr NA
 $ ktp                : chr NA
 $ address            : chr NA
 $ hp                 : chr "+**************"
 $ gender             : chr "male"
 $ dcp                : chr "tigaratus-p18dsjrj5bhuym65"
 $ current_max        :integer64 0 
 $ current_min        :integer64 0 
 $ expect_max         :integer64 0 
 $ expect_min         :integer64 0 
 $ job_vacancy_id     :integer64 1655 
 $ user_job_vacancy_id:integer64 33 
 $ Calculated YoE     : int 1
          Applied Date User ID            Fullname Job Role ID
1  2024-12-18 04:57:29     103     Putri Rosalinda           2
2  2024-12-23 02:21:15      84           tigaratus           2
3  2024-12-22 07:20:48      86       tigaratus sat           3
4  2024-12-23 01:31:08     100       Estelle Darcy          87
5  2024-12-18 04:45:39      97 Hasna Nazhifa Juned         774
6  2024-12-18 04:57:29     103     Putri Rosalinda         780
7  2024-12-18 04:57:29     103     Putri Rosalinda         780
8  2024-12-21 06:29:13      76    Rachelle Beaudry         850
9  2024-12-21 06:29:13      76    Rachelle Beaudry         851
10 2024-12-23 01:31:08     100       Estelle Darcy         851
11 2024-12-21 06:29:13      76    Rachelle Beaudry         852
12 2024-12-21 06:29:13      76    Rachelle Beaudry         852
13 2024-12-21 06:29:13      76    Rachelle Beaudry         852
14 2024-12-21 06:29:13      76    Rachelle Beaudry         853
15 2024-12-21 06:29:13      76    Rachelle Beaudry         853
16 2024-12-21 06:29:13      76    Rachelle Beaudry         853
17 2024-12-21 06:29:13      76    Rachelle Beaudry         854
18 2024-12-21 06:29:13      76    Rachelle Beaudry         854
19 2024-12-21 06:29:13      76    Rachelle Beaudry         854
20 2024-12-21 06:29:13      76    Rachelle Beaudry         855
21 2024-12-21 06:29:13      76    Rachelle Beaudry         856
22 2024-12-21 06:29:13      76    Rachelle Beaudry         857
23 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
24 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         859
25 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         860
26 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         861
27 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         862
28 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         863
29 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         864
30 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         865
31 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         866
32 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         867
33 2024-12-18 04:45:39      97 Hasna Nazhifa Juned         868
34 2024-12-18 04:57:29     103     Putri Rosalinda         869
35 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
36 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         870
37 2024-12-18 06:30:43     104 Hasna Nazhifa Juned         872
                                                                   Job Role
1                                                         digital marketing
2                                                         digital marketing
3                                                             fraud analyst
4                                                               ux designer
5                                                                  job name
6                                                          junior recruiter
7                                                          junior recruiter
8                                                               ux engineer
9                                                        system ux engineer
10                                                       system ux engineer
11                                                        junior accountant
12                                                        junior accountant
13                                                        junior accountant
14                                                               accountant
15                                                               accountant
16                                                               accountant
17                                                     accounting executive
18                                                     accounting executive
19                                                     accounting executive
20                                                    engineering executive
21                                                        graduate engineer
22                                                         project engineer
23                                            scorer for psychological test
24                                            scorer for psychological test
25                                                                 assessor
26                                 talent acquisition & document specialist
27                                                              interviewer
28                                                      freelance recruiter
29 consultant & project coordinator for recruitment, selection & assessment
30                                                    freelance facilitator
31                                                freelance project manager
32                                freelance trainer assistant (facilitator)
33                                                   recruitment specialist
34                                                       people recruitment
35                                         consultant & project coordinator
36                                         consultant & project coordinator
37                                              analyst & project assistant
     industry                                    company_name work_type
1  Technology                                          kucing full_time
2  Technology                                          Lalala full_time
3     Finance                                          asdsad full_time
4        <NA>                                        Morcelle full_time
5        <NA>                     Analyst & Project Assistant full_time
6        <NA>            PT Go Online Destinations (Pegipegi) full_time
7        <NA>            PT Go Online Destinations (Pegipegi) full_time
8        <NA>                                        Morcelle full_time
9        <NA>                             XarrowAI Industries full_time
10       <NA>                             XarrowAI Industries full_time
11       <NA>                              Arowwai Industries full_time
12       <NA>                              Arowwai Industries full_time
13       <NA>                              Arowwai Industries full_time
14       <NA>                                    Salford & Co full_time
15       <NA>                                    Salford & Co full_time
16       <NA>                                    Salford & Co full_time
17       <NA>                            Borcelle Corporation full_time
18       <NA>                            Borcelle Corporation full_time
19       <NA>                            Borcelle Corporation full_time
20       <NA>                           Borcelle Technologies full_time
21       <NA>                              Arowwai Industries full_time
22       <NA>                                    Salford & Co full_time
23       <NA>                             ASTRA INTERNATIONAL freelance
24       <NA>                            ASTRA DAIHATSU MOTOR freelance
25       <NA>                         PT ENIGMA PUTRA MANDIRI freelance
26       <NA>                PT TENRIAWARU ELIT INTERNASIONAL full_time
27       <NA>                                        STAFFINC freelance
28       <NA>                         PT KILAT JAYA INDONESIA freelance
29       <NA>                     PT TJITRA SELARAS SAMPOERNO full_time
30       <NA> PT INSAN BARU INDONESIA (THE NEW YOU INSTITUTE) freelance
31       <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) freelance
32       <NA>      PT SAMALA SERASI UNGGUL (RUMAH SIAP KERJA) freelance
33       <NA>            PT RAKAMIN KOLEKTIF MADANI (RAKAMIN) full_time
34       <NA>                                  Social Connect full_time
35       <NA>                             Tjitra & associates full_time
36       <NA>                             Tjitra & associates full_time
37       <NA>                             Tjitra & associates full_time
               ends_at           starts_at       MoE
1  2024-09-18 04:51:45 2024-02-18 04:51:41  7.100001
2                 <NA> 2023-04-05 09:46:31 20.923116
3                 <NA> 2022-01-22 07:17:58 35.526554
4                 <NA> 2022-12-31 17:00:00 24.079748
5  2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
6  2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
7  2022-09-30 17:00:00 2021-12-31 17:00:00  9.100000
8                 <NA> 2022-12-31 17:00:00 24.079748
9  2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
10 2022-11-30 17:00:00 2021-01-31 17:00:00 22.266667
11 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
12 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
13 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
14 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
15 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
16 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
17                <NA> 2022-12-31 17:00:00 24.079748
18                <NA> 2022-12-31 17:00:00 24.079748
19                <NA> 2022-12-31 17:00:00 24.079748
20                <NA> 2022-12-31 17:00:00 24.079748
21 2020-12-31 17:00:00 2020-01-31 17:00:00 11.166667
22 2022-11-30 17:00:00 2021-02-28 17:00:00 21.333333
23                <NA> 2019-04-30 17:00:00 68.779748
24                <NA> 2019-12-31 17:00:00 60.613082
25 2022-11-30 17:00:00 2022-10-31 17:00:00  1.000000
26 2024-09-30 17:00:00 2023-01-31 17:00:00 20.266667
27 2021-11-30 17:00:00 2021-10-31 17:00:00  1.000000
28 2023-10-31 17:00:00 2023-09-30 17:00:00  1.033333
29 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
30 2024-01-31 17:00:00 2023-11-30 17:00:00  2.066667
31 2024-09-30 17:00:00 2024-04-30 17:00:00  5.100000
32                <NA> 2024-04-30 17:00:00  7.879748
33                <NA> 2024-09-30 17:00:00  2.779748
34 2022-11-30 17:00:00 2020-07-31 17:00:00 28.400000
35 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
36 2022-09-30 17:00:00 2021-10-31 17:00:00 11.133333
37 2021-09-30 17:00:00 2020-07-31 17:00:00 14.200000
'data.frame':	37 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-18 04:57:29" "2024-12-23 02:21:15" ...
 $ User ID     :integer64 103 84 86 100 97 103 103 76 ... 
 $ Fullname    : chr  "Putri Rosalinda" "tigaratus" "tigaratus sat" "Estelle Darcy" ...
 $ Job Role ID :integer64 2 2 3 87 *********** 850 ... 
 $ Job Role    : chr  "digital marketing" "digital marketing" "fraud analyst" "ux designer" ...
 $ industry    : chr  "Technology" "Technology" "Finance" NA ...
 $ company_name: chr  "kucing" "Lalala" "asdsad" "Morcelle" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2024-09-18 04:51:45" NA ...
 $ starts_at   : POSIXct, format: "2024-02-18 04:51:41" "2023-04-05 09:46:31" ...
 $ MoE         : num  7.1 20.9 35.5 24.1 14.2 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
'data.frame':	1 obs. of  14 variables:
 $ Candidate          : chr "tigaratus"
 $ Education          : chr "S1"
 $ Institution        : chr "Aarhus University"
 $ Experience         : num 1
 $ GPA                : num 3.55
 $ Domisili           : chr "Kabupaten Aceh Barat - Aceh"
 $ Expected_Salary    :integer64 0 
 $ Industry           : chr "{\"Technology\"}"
 $ Skillntools        : 'pq__varchar' chr "{Python}"
 $ Major              : chr "Accounting"
 $ user_job_vacancy_id:integer64 33 
 $ edu_matched        : logi TRUE
 $ major_match        : logi FALSE
 $ edu_major_weight   : num 0.5
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] "S1"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
Warning: `data_frame()` was deprecated in tibble 1.1.0.
ℹ Please use `tibble()` instead.
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          1
8 S3          2


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0.2


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 2


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 0



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 0



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 0.4



$Education_Major
$Education_Major$MrU_Education_Major
[1] 1.4


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0.2

$weight_gpa
[1] 2

$weight_domisili
[1] 0

$weight_es
[1] 0

$weight_industry
[1] 0

$weight_skill
[1] 0.4

$weight_education_major
[1] 1.4

[1] "Scaled value"
$weight_we
[1] 5

$weight_gpa
[1] 50

$weight_skill
[1] 10

$weight_education_major
[1] 35

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Test New Create List Matching Criteria"
[1] "Done New Calculation"
[1] "Test New Code Done"
  Candidate Education       Institution Experience  GPA
1 tigaratus        S1 Aarhus University          1 3.55
                     Domisili Expected_Salary       Industry Skillntools
1 Kabupaten Aceh Barat - Aceh               0 {"Technology"}    {Python}
       Major user_job_vacancy_id edu_matched major_match edu_major_weight
1 Accounting                  33        TRUE       FALSE              0.5
  MrU_Score_Edu MrU_Score_WE MrU_Score_GPA MrU_Score_Domisili MrU_Score_ES
1            17            0             0                  0            0
  MrU_Score_Industry MrU_Score_Skillntools Match_Item_Edu Unmatch_Item_Edu
1                  0                     0       S1, TRUE             NULL
  Match_Item_WE Unmatch_Item_WE Match_Item_GPA            Unmatch_Item_GPA
1          NULL            NULL           NULL Invalid baseline GPA, FALSE
                      Match_Item_Domisili              Unmatch_Item_Domisili
1 Kota Jakarta Utara - DKI Jakarta, FALSE Kabupaten Aceh Barat - Aceh, FALSE
  Match_Item_ES Unmatch_Item_ES Match_Item_Industry Unmatch_Item_Industry
1          0, 1            NULL                NULL                  NULL
            Match_Item_Skillntools Unmatch_Item_Skillntools Match_Item_Major
1 [], FALSE, ActiveCampaign, FALSE            Python, FALSE             NULL
                              Unmatch_Item_Major Total_Score
1 Teknik Sipil, FALSE, Teknik Informatika, FALSE          17
                                                                                                                                                                                                             matching_criteria
1 0, TRUE, Kota Jakarta Utara - DKI Jakarta, FALSE, Kabupaten Aceh Barat - Aceh, FALSE, Invalid baseline GPA, FALSE, [], FALSE, ActiveCampaign, FALSE, Python, FALSE, S1, TRUE, Teknik Sipil, FALSE, Teknik Informatika, FALSE
[1] "Cal MrU Done"
[1] "Scale MrC"
# A tibble: 4 × 4
  level       value scale_factor new_value
  <chr>       <dbl>        <dbl>     <dbl>
1 0-3 tahun       0          1.5         0
2 3-6 tahun       0          1.5         0
3 6-11 tahun      0          1.5         0
4 11-99 tahun     0          1.5         0
[1] "Scale MrC Done"
[1] "Start MrC New Code"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

Warning: There was 1 warning in `mutate()`.
ℹ In argument: `MrC_results = list(calculate_mrc_scores(cur_data(),
  matchmaking_config))`.
ℹ In row 1.
Caused by warning:
! `cur_data()` was deprecated in dplyr 1.1.0.
ℹ Please use `pick()` instead.
[1] "Done MrC New Code"
[1] "Cal MrC Done"
[1] "Finalized Table"
Rows: 1
Columns: 5
$ user_job_vacancy_id <int64> 33
$ main_score          <dbl> 17
$ additional_score    <dbl> 0
$ matching_criteria   <named list> [[[[0, TRUE]], []], [[], []], [[["Kota Jakarta U…
$ additional_values   <list> [[[], []], [[], []], [[], []], [[], []], [[], []],…
# A tibble: 1 × 5
  user_job_vacancy_id main_score additional_score matching_criteria
              <int64>      <dbl>            <dbl> <named list>     
1                  33         17                0 <named list [8]> 
# ℹ 1 more variable: additional_values <list>
[1] "Finalized Done"
[1] "Write for DB"
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 2872
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "testing pemaketan"
$ id                               <int64> 2872
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "1"
$ `Education Level`                <chr> NA
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> NA
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Pendidikan Dokter"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Pendidikan Dokter"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] FALSE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 1

$Education$MrC_Education[[2]]$disabled
[1] FALSE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 2

$Education$MrC_Education[[3]]$disabled
[1] FALSE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 3

$Education$MrC_Education[[4]]$disabled
[1] FALSE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 4

$Education$MrC_Education[[5]]$disabled
[1] FALSE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 5

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 6

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 7

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0

$Education$MrU_Education$required
[1] FALSE



$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "1"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0

$Working_Experience$MrU_WE$required
[1] FALSE



[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date              Role                     Email User ID
1 2024-12-23 02:21:28 <NAME_EMAIL>  232583
                Fullname Kota - Kab Provinces Degree GPA             Major
1 JHON DAVID ALBERTSISUS  tangerang      <NA>     S1   0 Digital Marketing
                        Institution Degree 2nd GPA 2nd
1 Google Skills Of Tomorrow Program         S1       0
                       Major 2nd Institution 2nd         Job Role      YoE
1 Learning In Ui/Ux Design Tools      It Academy graphic designer 2.663014
  Experience
1          0
                                                                                              Skill & Tools
1 {"Augmented Reality","Data Analysis","Graphic Design","Microsoft Power BI","UI Design",Unity,"UX Design"}
                                                                                                    CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf
                            Linkedin Link
1 https://www.linkedin.com/in/mhmoudahmad
                                         Portfolio Link Instagram Link
1 https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/           <NA>
  Twitter Link                           State Status Availability
1         <NA> assessment_technical_assessment        open_to_work
                  dob  pob  ktp
1 1991-09-16 17:00:00 <NA> <NA>
                                                                                                                             address
1 {"address_type"=>"id_card", "address"=>"TMN CIKANDE BLK C-04", "ward"=>nil, "district"=>nil, "city"=>"TANGERANG", "province"=>nil}
             hp gender                                     dcp current_max
1 +624223423423   male jhon-david-albertsisus-qd4qkfopyx9hecvm           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1           0          2          1           2872             2162385
  Calculated YoE
1              2
'data.frame':	1 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 02:21:28"
 $ Role               : chr "testing pemaketan"
 $ Email              : chr "<EMAIL>"
 $ User ID            :integer64 232583 
 $ Fullname           : chr "JHON DAVID ALBERTSISUS"
 $ Kota - Kab         : chr "tangerang"
 $ Provinces          : chr NA
 $ Degree             : chr "S1"
 $ GPA                : num 0
 $ Major              : chr "Digital Marketing"
 $ Institution        : chr "Google Skills Of Tomorrow Program"
 $ Degree 2nd         : chr "S1"
 $ GPA 2nd            : num 0
 $ Major 2nd          : chr "Learning In Ui/Ux Design Tools"
 $ Institution 2nd    : chr "It Academy"
 $ Job Role           : chr "graphic designer"
 $ YoE                : num 2.66
 $ Experience         : int 0
 $ Skill & Tools      : 'pq__varchar' chr "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ CV Upload          : chr "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf"
 $ Linkedin Link      : chr "https://www.linkedin.com/in/mhmoudahmad"
 $ Portfolio Link     : chr "https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/"
 $ Instagram Link     : chr NA
 $ Twitter Link       : chr NA
 $ State              : chr "assessment_technical_assessment"
 $ Status Availability: 'pq_status_availability' chr "open_to_work"
 $ dob                : POSIXct, format: "1991-09-16 17:00:00"
 $ pob                : chr NA
 $ ktp                : chr NA
 $ address            : chr "{\"address_type\"=>\"id_card\", \"address\"=>\"TMN CIKANDE BLK C-04\", \"ward\"=>nil, \"district\"=>nil, \"city"| __truncated__
 $ hp                 : chr "+624223423423"
 $ gender             : chr "male"
 $ dcp                : chr "jhon-david-albertsisus-qd4qkfopyx9hecvm"
 $ current_max        :integer64 0 
 $ current_min        :integer64 0 
 $ expect_max         :integer64 2 
 $ expect_min         :integer64 1 
 $ job_vacancy_id     :integer64 2872 
 $ user_job_vacancy_id:integer64 2162385 
 $ Calculated YoE     : int 2
         Applied Date User ID               Fullname Job Role ID
1 2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS         711
2 2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1409
3 2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1410
                     Job Role industry
1            graphic designer  Finance
2 computer science instructor     <NA>
3              representative     <NA>
                                company_name work_type             ends_at
1                                    BioDose full_time 2023-07-31 17:00:00
2                  Gilgamesh Institute (SYR) full_time 2021-09-30 17:00:00
3 Al-Basel Fair Invention & Innovation (SYR) full_time 2018-09-29 17:00:00
            starts_at        MoE
1 2022-09-30 17:00:00 10.1333333
2 2019-12-31 17:00:00 21.3000000
3 2018-08-31 17:00:00  0.9666667
'data.frame':	3 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 02:21:28" "2024-12-23 02:21:28" ...
 $ User ID     :integer64 232583 232583 232583 
 $ Fullname    : chr  "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS"
 $ Job Role ID :integer64 711 1409 1410 
 $ Job Role    : chr  "graphic designer" "computer science instructor" "representative"
 $ industry    : chr  "Finance" NA NA
 $ company_name: chr  "BioDose" "Gilgamesh Institute (SYR)" "Al-Basel Fair Invention & Innovation (SYR)"
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time"
 $ ends_at     : POSIXct, format: "2023-07-31 17:00:00" "2021-09-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2022-09-30 17:00:00" "2019-12-31 17:00:00" ...
 $ MoE         : num  10.133 21.3 0.967
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	1 obs. of  14 variables:
 $ Candidate          : chr "JHON DAVID ALBERTSISUS"
 $ Education          : chr "S1"
 $ Institution        : chr "Google Skills Of Tomorrow Program"
 $ Experience         : num 2
 $ GPA                : num 0
 $ Domisili           : chr "tangerang"
 $ Expected_Salary    :integer64 1 
 $ Industry           : chr "{\"Finance\"}"
 $ Skillntools        : 'pq__varchar' chr "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ Major              : chr "Digital Marketing"
 $ user_job_vacancy_id:integer64 2162385 
 $ edu_matched        : logi NA
 $ major_match        : logi FALSE
 $ edu_major_weight   : logi NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] NA
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          0
8 S3          0


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_domisili
[1] 20

$weight_es
[1] 20

$weight_skill
[1] 60

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 1657
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "Test Config Match Rate"
$ id                               <int64> 1657
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "Python"
$ `Education Level`                <chr> "S1"
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> "entry_level"
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "onsite"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Engineering"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Ilmu Komputer"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$name
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] TRUE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$name
[1] "D1"

$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 0

$Education$MrC_Education[[2]]$disabled
[1] TRUE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$name
[1] "D2"

$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 0

$Education$MrC_Education[[3]]$disabled
[1] TRUE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$name
[1] "D3"

$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 0

$Education$MrC_Education[[4]]$disabled
[1] TRUE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$name
[1] "D4"

$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 0

$Education$MrC_Education[[5]]$disabled
[1] TRUE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$name
[1] "S1"

$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 0

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$name
[1] "S2"

$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 0

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$name
[1] "S3"

$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 0

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3

$Education$MrU_Education$required
[1] TRUE

$Education$MrU_Education$minimum_config
$Education$MrU_Education$minimum_config$key
[1] "name"

$Education$MrU_Education$minimum_config$value
[1] "S1"




$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "Python"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$name
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$name
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$name
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$name
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2

$Working_Experience$MrU_WE$required
[1] TRUE

$Working_Experience$MrU_WE$minimum_config
$Working_Experience$MrU_WE$minimum_config$key
[1] "name"

$Working_Experience$MrU_WE$minimum_config$value
[1] "0-2 tahun"




[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date                   Role                     Email User ID
1 2024-12-23 01:32:25 Test Config <NAME_EMAIL>      84
2 2024-12-23 02:22:00 Test Config <NAME_EMAIL>      86
       Fullname                  Kota - Kab Provinces Degree  GPA      Major
1     tigaratus Kabupaten Aceh Barat - Aceh      <NA>     S1 3.55 Accounting
2 tigaratus sat Kabupaten Aceh Barat - Aceh      <NA>     S1 3.55       <NA>
              Institution Degree 2nd GPA 2nd Major 2nd Institution 2nd
1       Aarhus University       <NA>      NA      <NA>            <NA>
2 Universitas Gadjah Mada       <NA>      NA      <NA>            <NA>
           Job Role      YoE Experience                   Skill & Tools
1 digital marketing 1.719718          0                        {Python}
2     fraud analyst 2.920000          0 {"Google Spreadsheet",Python,R}
                                                                                                             CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf
2 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-b3bdddbb-6c08-4f5a-9788-592cbec3ecef.pdf
  Linkedin Link Portfolio Link Instagram Link Twitter Link   State
1          <NA>           <NA>           <NA>         <NA> applied
2          <NA>           <NA>           <NA>         <NA> applied
  Status Availability  dob  pob  ktp address              hp gender
1        open_to_work <NA> <NA> <NA>    <NA> +**************   male
2        open_to_work <NA> <NA> <NA>    <NA> +62213123131231   male
                             dcp current_max current_min expect_max expect_min
1     tigaratus-p18dsjrj5bhuym65           0           0          0          0
2 tigaratus-sat-qahu71ufhjgyoi9a           0           0          0          0
  job_vacancy_id user_job_vacancy_id Calculated YoE
1           1657                  32              1
2           1657                  34              2
'data.frame':	2 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 01:32:25" "2024-12-23 02:22:00"
 $ Role               : chr  "Test Config Match Rate" "Test Config Match Rate"
 $ Email              : chr  "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 84 86 
 $ Fullname           : chr  "tigaratus" "tigaratus sat"
 $ Kota - Kab         : chr  "Kabupaten Aceh Barat - Aceh" "Kabupaten Aceh Barat - Aceh"
 $ Provinces          : chr  NA NA
 $ Degree             : chr  "S1" "S1"
 $ GPA                : num  3.55 3.55
 $ Major              : chr  "Accounting" NA
 $ Institution        : chr  "Aarhus University" "Universitas Gadjah Mada"
 $ Degree 2nd         : chr  NA NA
 $ GPA 2nd            : num  NA NA
 $ Major 2nd          : chr  NA NA
 $ Institution 2nd    : chr  NA NA
 $ Job Role           : chr  "digital marketing" "fraud analyst"
 $ YoE                : num  1.72 2.92
 $ Experience         : int  0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{Python}" "{\"Google Spreadsheet\",Python,R}"
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-c51176ea-e337-4ca6-980f-6494d5320438.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/toyota/files/yanardian3-b3bdddbb-6c08-4f5a-9788-592cbec3ecef.pdf"
 $ Linkedin Link      : chr  NA NA
 $ Portfolio Link     : chr  NA NA
 $ Instagram Link     : chr  NA NA
 $ Twitter Link       : chr  NA NA
 $ State              : chr  "applied" "applied"
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: NA NA
 $ pob                : chr  NA NA
 $ ktp                : chr  NA NA
 $ address            : chr  NA NA
 $ hp                 : chr  "+**************" "+62213123131231"
 $ gender             : chr  "male" "male"
 $ dcp                : chr  "tigaratus-p18dsjrj5bhuym65" "tigaratus-sat-qahu71ufhjgyoi9a"
 $ current_max        :integer64 0 0 
 $ current_min        :integer64 0 0 
 $ expect_max         :integer64 0 0 
 $ expect_min         :integer64 0 0 
 $ job_vacancy_id     :integer64 1657 1657 
 $ user_job_vacancy_id:integer64 32 34 
 $ Calculated YoE     : int  1 2
         Applied Date User ID      Fullname Job Role ID          Job Role
1 2024-12-23 01:32:25      84     tigaratus           2 digital marketing
2 2024-12-23 02:22:00      86 tigaratus sat           3     fraud analyst
    industry company_name work_type ends_at           starts_at      MoE
1 Technology       Lalala full_time    <NA> 2023-04-05 09:46:31 20.92323
2    Finance       asdsad full_time    <NA> 2022-01-22 07:17:58 35.52667
'data.frame':	2 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 01:32:25" "2024-12-23 02:22:00"
 $ User ID     :integer64 84 86 
 $ Fullname    : chr  "tigaratus" "tigaratus sat"
 $ Job Role ID :integer64 2 3 
 $ Job Role    : chr  "digital marketing" "fraud analyst"
 $ industry    : chr  "Technology" "Finance"
 $ company_name: chr  "Lalala" "asdsad"
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time"
 $ ends_at     : POSIXct, format: NA NA
 $ starts_at   : POSIXct, format: "2023-04-05 09:46:31" "2022-01-22 07:17:58"
 $ MoE         : num  20.9 35.5
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	2 obs. of  14 variables:
 $ Candidate          : chr  "tigaratus" "tigaratus sat"
 $ Education          : chr  "S1" "S1"
 $ Institution        : chr  "Aarhus University" "Universitas Gadjah Mada"
 $ Experience         : num  1 2
 $ GPA                : num  3.55 3.55
 $ Domisili           : chr  "Kabupaten Aceh Barat - Aceh" "Kabupaten Aceh Barat - Aceh"
 $ Expected_Salary    :integer64 0 0 
 $ Industry           : chr  "{\"Technology\"}" "{\"Finance\"}"
 $ Skillntools        : 'pq__varchar' chr  "{Python}" "{\"Google Spreadsheet\",Python,R}"
 $ Major              : chr  "Accounting" NA
 $ user_job_vacancy_id:integer64 32 34 
 $ edu_matched        : logi  TRUE TRUE
 $ major_match        : logi  FALSE FALSE
 $ edu_major_weight   : logi  NA NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] "S1"
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] "0-3 tahun"
[1] "0-3 tahun"
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 3


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          1
8 S3          2


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 2


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       1
3 6-11 tahun      2
4 11-99 tahun     3
5 0-99 tahun      4


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 3

$weight_we
[1] 2

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_education
[1] 30

$weight_we
[1] 20

$weight_domisili
[1] 10

$weight_es
[1] 10

$weight_skill
[1] 30

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3.55"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 2872
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "testing pemaketan"
$ id                               <int64> 2872
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "1"
$ `Education Level`                <chr> NA
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> NA
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Pendidikan Dokter"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Pendidikan Dokter"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] FALSE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 1

$Education$MrC_Education[[2]]$disabled
[1] FALSE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 2

$Education$MrC_Education[[3]]$disabled
[1] FALSE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 3

$Education$MrC_Education[[4]]$disabled
[1] FALSE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 4

$Education$MrC_Education[[5]]$disabled
[1] FALSE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 5

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 6

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 7

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0

$Education$MrU_Education$required
[1] FALSE



$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "1"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0

$Working_Experience$MrU_WE$required
[1] FALSE



[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date              Role                     Email User ID
1 2024-12-23 02:32:15 testing pemaketan  <EMAIL>   11152
2 2024-12-23 02:21:28 <NAME_EMAIL>  232583
                Fullname Kota - Kab    Provinces Degree GPA             Major
1             Orochimaru     Konoha Jakarta Raya     S3   0 Pendidikan Dokter
2 JHON DAVID ALBERTSISUS  tangerang         <NA>     S1   0 Digital Marketing
                        Institution Degree 2nd GPA 2nd
1    Universitas Pendidikan Ganesha         S3       0
2 Google Skills Of Tomorrow Program         S1       0
                       Major 2nd Institution 2nd         Job Role       YoE
1  Full Stack Product Management           Revou       Co-Founder 54.824048
2 Learning In Ui/Ux Design Tools      It Academy graphic designer  2.663014
  Experience
1          0
2          0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Skill & Tools
1 {"Adobe Illustrator","Algoritma dan Struktur Data Fundamental",Android,"API (Aplication Programing Interface)","Backend Developer for Mobiles Fundamentals","Backend Developer Fundamentals",Bug,"Build Tools","Campaign Management",CI/CD,Clouds,"CMS (Content Management System)",Communication693,"Conditional Formatting","Content Analysis","Cross-functional Collaboration","Customer Feedback Analysis","Data Fetching","Design Changes","Design Thinking","Developer Softskill",Development,df,"Domain and Hosting","Facebook Ads","Facebook Business Manager",Firebase,"Fundamental of Structured Query Language","Google Analytics","Google Search Console","Introduction to Advertising","Introduction to SEO","Issue Resolution","Keyword Research","Machine Learning Fundamental","Market Funneling",Math,"Ms. Excel",MySQL,"Network Engineer","NoSQL Database",Organisation1240,Organisation851,Proactive3872,"Product Development","Product Improvement","Product Presentation",Self-motivated2545,"SEO Foundation","Softskill for Designer",Tableau,"Team Management",Teamwork1515,Teamwork3502,test,"UI/UX Fundamentals","Usability Testing","User Research",wda,"Workflow Streamlining","Work under pressure3904"}
2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {"Augmented Reality","Data Analysis","Graphic Design","Microsoft Power BI","UI Design",Unity,"UX Design"}
                                                                                                    CV Upload
1 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf
2 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf
                            Linkedin Link
1  https://www.linkedin.com/in/orochimaru
2 https://www.linkedin.com/in/mhmoudahmad
                                          Portfolio Link Instagram Link
1 https://issuu.com/orochimaru/docs/portfolio_orochimaru           <NA>
2  https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/           <NA>
  Twitter Link                           State Status Availability
1         <NA> assessment_technical_assessment        open_to_work
2         <NA> assessment_technical_assessment        open_to_work
                  dob  pob  ktp
1 2024-02-15 01:43:52 <NA> <NA>
2 1991-09-16 17:00:00 <NA> <NA>
                                                                                                                             address
1                                                                                                                               <NA>
2 {"address_type"=>"id_card", "address"=>"TMN CIKANDE BLK C-04", "ward"=>nil, "district"=>nil, "city"=>"TANGERANG", "province"=>nil}
             hp gender                                     dcp current_max
1   12345678910   male          student-dig30-lls5n1gorem4chyr           0
2 +624223423423   male jhon-david-albertsisus-qd4qkfopyx9hecvm           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1           1          2          1           2872             2162386
2           0          2          1           2872             2162385
  Calculated YoE
1             25
2              2
'data.frame':	2 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 02:32:15" "2024-12-23 02:21:28"
 $ Role               : chr  "testing pemaketan" "testing pemaketan"
 $ Email              : chr  "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 11152 232583 
 $ Fullname           : chr  "Orochimaru" "JHON DAVID ALBERTSISUS"
 $ Kota - Kab         : chr  "Konoha" "tangerang"
 $ Provinces          : chr  "Jakarta Raya" NA
 $ Degree             : chr  "S3" "S1"
 $ GPA                : num  0 0
 $ Major              : chr  "Pendidikan Dokter" "Digital Marketing"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Google Skills Of Tomorrow Program"
 $ Degree 2nd         : chr  "S3" "S1"
 $ GPA 2nd            : num  0 0
 $ Major 2nd          : chr  "Full Stack Product Management" "Learning In Ui/Ux Design Tools"
 $ Institution 2nd    : chr  "Revou" "It Academy"
 $ Job Role           : chr  "Co-Founder" "graphic designer"
 $ YoE                : num  54.82 2.66
 $ Experience         : int  0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf"
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/orochimaru" "https://www.linkedin.com/in/mhmoudahmad"
 $ Portfolio Link     : chr  "https://issuu.com/orochimaru/docs/portfolio_orochimaru" "https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/"
 $ Instagram Link     : chr  NA NA
 $ Twitter Link       : chr  NA NA
 $ State              : chr  "assessment_technical_assessment" "assessment_technical_assessment"
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: "2024-02-15 01:43:52" "1991-09-16 17:00:00"
 $ pob                : chr  NA NA
 $ ktp                : chr  NA NA
 $ address            : chr  NA "{\"address_type\"=>\"id_card\", \"address\"=>\"TMN CIKANDE BLK C-04\", \"ward\"=>nil, \"district\"=>nil, \"city"| __truncated__
 $ hp                 : chr  "12345678910" "+624223423423"
 $ gender             : chr  "male" "male"
 $ dcp                : chr  "student-dig30-lls5n1gorem4chyr" "jhon-david-albertsisus-qd4qkfopyx9hecvm"
 $ current_max        :integer64 0 0 
 $ current_min        :integer64 1 0 
 $ expect_max         :integer64 2 2 
 $ expect_min         :integer64 1 1 
 $ job_vacancy_id     :integer64 2872 2872 
 $ user_job_vacancy_id:integer64 2162386 2162385 
 $ Calculated YoE     : int  25 2
          Applied Date User ID               Fullname Job Role ID
1  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS         711
2  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1409
3  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1410
4  2024-12-23 02:32:15   11152             Orochimaru         755
5  2024-12-23 02:32:15   11152             Orochimaru         755
6  2024-12-23 02:32:15   11152             Orochimaru         755
7  2024-12-23 02:32:15   11152             Orochimaru        1161
8  2024-12-23 02:32:15   11152             Orochimaru        1165
9  2024-12-23 02:32:15   11152             Orochimaru        1272
10 2024-12-23 02:32:15   11152             Orochimaru        1271
11 2024-12-23 02:32:15   11152             Orochimaru        1267
12 2024-12-23 02:32:15   11152             Orochimaru        1263
13 2024-12-23 02:32:15   11152             Orochimaru        1404
14 2024-12-23 02:32:15   11152             Orochimaru         893
15 2024-12-23 02:32:15   11152             Orochimaru        1404
16 2024-12-23 02:32:15   11152             Orochimaru         893
17 2024-12-23 02:32:15   11152             Orochimaru        1404
18 2024-12-23 02:32:15   11152             Orochimaru         893
                      Job Role          industry
1             graphic designer           Finance
2  computer science instructor              <NA>
3               representative              <NA>
4                      founder        Technology
5                      founder        Technology
6                      founder        Technology
7          Shareholder/Partner        Technology
8         Associate Consultant Digital Marketing
9        front end developer i Digital Marketing
10         digital marketing i           Careers
11          test new role edit           Finance
12             co founder edit Digital Marketing
13   project admin distributor              <NA>
14            customer service              <NA>
15   project admin distributor              <NA>
16            customer service              <NA>
17   project admin distributor              <NA>
18            customer service              <NA>
                                 company_name work_type             ends_at
1                                     BioDose full_time 2023-07-31 17:00:00
2                   Gilgamesh Institute (SYR) full_time 2021-09-30 17:00:00
3  Al-Basel Fair Invention & Innovation (SYR) full_time 2018-09-29 17:00:00
4                               Beasiswa Dian full_time                <NA>
5                              Bea Siswa Dian part_time                <NA>
6              MAM! feel good food restaurant full_time 2024-04-17 06:30:36
7                   PT Jelita Karisma Persada full_time                <NA>
8                                   Hay Group part_time 2011-09-30 00:00:00
9                                      test I part_time 2023-12-10 05:29:10
10                                   testedit part_time 2024-01-10 05:25:29
11                                  test edit part_time 2024-04-17 04:48:15
12                               Frame A Trip  contract 2024-04-17 04:48:43
13                                PT Bank DKI  contract 2021-01-31 17:00:00
14                                PT Bank DKI full_time 2023-05-31 17:00:00
15                                PT Bank DKI  contract 2021-01-31 17:00:00
16                                PT Bank DKI full_time 2023-05-31 17:00:00
17                                PT Bank DKI  contract 2021-01-31 17:00:00
18                                PT Bank DKI full_time 2023-05-31 17:00:00
             starts_at          MoE
1  2022-09-30 17:00:00 1.013333e+01
2  2019-12-31 17:00:00 2.130000e+01
3  2018-08-31 17:00:00 9.666667e-01
4  2015-01-01 00:00:00 1.214703e+02
5  2015-03-01 00:00:00 1.195036e+02
6  2017-03-01 00:00:00 8.680904e+01
7  2013-01-01 00:00:00 1.458036e+02
8  2008-01-01 00:00:00 4.560000e+01
9  2023-12-10 05:28:30 1.549846e-05
10 2023-12-10 05:24:31 1.033355e+00
11 2023-12-10 05:14:57 4.299382e+00
12 2017-08-01 00:00:00 8.170668e+01
13 2020-12-31 17:00:00 1.033333e+00
14 2021-10-31 17:00:00 1.923333e+01
15 2020-12-31 17:00:00 1.033333e+00
16 2021-10-31 17:00:00 1.923333e+01
17 2020-12-31 17:00:00 1.033333e+00
18 2021-10-31 17:00:00 1.923333e+01
'data.frame':	18 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 02:21:28" "2024-12-23 02:21:28" ...
 $ User ID     :integer64 232583 232583 232583 11152 11152 11152 11152 11152 ... 
 $ Fullname    : chr  "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "Orochimaru" ...
 $ Job Role ID :integer64 711 1409 1410 *********** 1161 1165 ... 
 $ Job Role    : chr  "graphic designer" "computer science instructor" "representative" "founder" ...
 $ industry    : chr  "Finance" NA NA "Technology" ...
 $ company_name: chr  "BioDose" "Gilgamesh Institute (SYR)" "Al-Basel Fair Invention & Innovation (SYR)" "Beasiswa Dian" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2023-07-31 17:00:00" "2021-09-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2022-09-30 17:00:00" "2019-12-31 17:00:00" ...
 $ MoE         : num  10.133 21.3 0.967 121.47 119.504 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	2 obs. of  14 variables:
 $ Candidate          : chr  "Orochimaru" "JHON DAVID ALBERTSISUS"
 $ Education          : chr  "S3" "S1"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Google Skills Of Tomorrow Program"
 $ Experience         : num  25 2
 $ GPA                : num  0 0
 $ Domisili           : chr  "Konoha" "tangerang"
 $ Expected_Salary    :integer64 1 1 
 $ Industry           : chr  "{\"Technology\", \"Digital Marketing\", \"Careers\", \"Finance\"}" "{\"Finance\"}"
 $ Skillntools        : 'pq__varchar' chr  "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ Major              : chr  "Pendidikan Dokter" "Digital Marketing"
 $ user_job_vacancy_id:integer64 2162386 2162385 
 $ edu_matched        : logi  NA NA
 $ major_match        : logi  FALSE FALSE
 $ edu_major_weight   : logi  NA NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] NA
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          0
8 S3          0


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_domisili
[1] 20

$weight_es
[1] 20

$weight_skill
[1] 60

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 2872
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "testing pemaketan"
$ id                               <int64> 2872
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "1"
$ `Education Level`                <chr> NA
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> NA
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Pendidikan Dokter"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Pendidikan Dokter"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] FALSE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 1

$Education$MrC_Education[[2]]$disabled
[1] FALSE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 2

$Education$MrC_Education[[3]]$disabled
[1] FALSE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 3

$Education$MrC_Education[[4]]$disabled
[1] FALSE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 4

$Education$MrC_Education[[5]]$disabled
[1] FALSE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 5

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 6

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 7

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0

$Education$MrU_Education$required
[1] FALSE



$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "1"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0

$Working_Experience$MrU_WE$required
[1] FALSE



[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date              Role                     Email User ID
1 2024-12-23 02:32:15 testing pemaketan  <EMAIL>   11152
2 2024-12-23 02:41:56 testing pemaketan  <EMAIL>   11153
3 2024-12-23 02:21:28 <NAME_EMAIL>  232583
                Fullname                                        Kota - Kab
1             Orochimaru                                            Konoha
2              Adem Sari Solagidih , Chas, District - Bokaro, Pin – 827013
3 JHON DAVID ALBERTSISUS                                         tangerang
     Provinces Degree GPA                  Major
1 Jakarta Raya     S3   0      Pendidikan Dokter
2         <NA>     D3   0 Mechanical Engineering
3         <NA>     S1   0      Digital Marketing
                        Institution Degree 2nd GPA 2nd
1    Universitas Pendidikan Ganesha         S3       0
2  Universitas Nusa Mandiri Jakarta       <NA>       0
3 Google Skills Of Tomorrow Program         S1       0
                       Major 2nd                   Institution 2nd
1  Full Stack Product Management                             Revou
2              Digital Marketing Google Skills Of Tomorrow Program
3 Learning In Ui/Ux Design Tools                        It Academy
          Job Role        YoE Experience
1       Co-Founder 54.8241051          0
2           intern  0.8328767          0
3 graphic designer  2.6630137          0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Skill & Tools
1 {"Adobe Illustrator","Algoritma dan Struktur Data Fundamental",Android,"API (Aplication Programing Interface)","Backend Developer for Mobiles Fundamentals","Backend Developer Fundamentals",Bug,"Build Tools","Campaign Management",CI/CD,Clouds,"CMS (Content Management System)",Communication693,"Conditional Formatting","Content Analysis","Cross-functional Collaboration","Customer Feedback Analysis","Data Fetching","Design Changes","Design Thinking","Developer Softskill",Development,df,"Domain and Hosting","Facebook Ads","Facebook Business Manager",Firebase,"Fundamental of Structured Query Language","Google Analytics","Google Search Console","Introduction to Advertising","Introduction to SEO","Issue Resolution","Keyword Research","Machine Learning Fundamental","Market Funneling",Math,"Ms. Excel",MySQL,"Network Engineer","NoSQL Database",Organisation1240,Organisation851,Proactive3872,"Product Development","Product Improvement","Product Presentation",Self-motivated2545,"SEO Foundation","Softskill for Designer",Tableau,"Team Management",Teamwork1515,Teamwork3502,test,"UI/UX Fundamentals","Usability Testing","User Research",wda,"Workflow Streamlining","Work under pressure3904"}
2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {AUTOCAD,"Copper Tubes","Design Thinking","Fitting and Welding","Google Analytics","Graphic Design","IBM Cloud",Math,"Ms. Excel",MySQL,"piping drawings","piping layouts",Self-motivated202,"Solar Energy","SOLID WORKS",Tableau,test,"UI Design","UX Design","Wooden Structure"}
3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {"Augmented Reality","Data Analysis","Graphic Design","Microsoft Power BI","UI Design",Unity,"UX Design"}
                                                                                                                                  CV Upload
1                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf
2 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/miniprojectcertificate407IAPTGI1352022-86c64337-8d13-4f76-a838-edc6aa2dedf8.pdf
3                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf
                            Linkedin Link
1  https://www.linkedin.com/in/orochimaru
2    https://www.linkedin.com/in/mikle-zu
3 https://www.linkedin.com/in/mhmoudahmad
                                          Portfolio Link Instagram Link
1 https://issuu.com/orochimaru/docs/portfolio_orochimaru           <NA>
2                                                                  <NA>
3  https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/           <NA>
  Twitter Link                           State Status Availability
1         <NA> assessment_technical_assessment        open_to_work
2         <NA> assessment_technical_assessment        open_to_work
3         <NA> assessment_technical_assessment        open_to_work
                  dob  pob  ktp
1 2024-02-15 01:43:52 <NA> <NA>
2 2002-10-18 17:00:00 <NA> <NA>
3 1991-09-16 17:00:00 <NA> <NA>
                                                                                                                             address
1                                                                                                                               <NA>
2                                                                                                                               <NA>
3 {"address_type"=>"id_card", "address"=>"TMN CIKANDE BLK C-04", "ward"=>nil, "district"=>nil, "city"=>"TANGERANG", "province"=>nil}
             hp gender                                     dcp current_max
1   12345678910   male          student-dig30-lls5n1gorem4chyr           0
2 +916201317199 female          student-dig31-jzu6zy91rysfo3tb           0
3 +624223423423   male jhon-david-albertsisus-qd4qkfopyx9hecvm           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1           1          2          1           2872             2162386
2           1          3          2           2872             2162387
3           0          2          1           2872             2162385
  Calculated YoE
1             25
2              1
3              2
'data.frame':	3 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 02:32:15" "2024-12-23 02:41:56" ...
 $ Role               : chr  "testing pemaketan" "testing pemaketan" "testing pemaketan"
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 11152 11153 232583 
 $ Fullname           : chr  "Orochimaru" "Adem Sari" "JHON DAVID ALBERTSISUS"
 $ Kota - Kab         : chr  "Konoha" "Solagidih , Chas, District - Bokaro, Pin – 827013" "tangerang"
 $ Provinces          : chr  "Jakarta Raya" NA NA
 $ Degree             : chr  "S3" "D3" "S1"
 $ GPA                : num  0 0 0
 $ Major              : chr  "Pendidikan Dokter" "Mechanical Engineering" "Digital Marketing"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Universitas Nusa Mandiri Jakarta" "Google Skills Of Tomorrow Program"
 $ Degree 2nd         : chr  "S3" NA "S1"
 $ GPA 2nd            : num  0 0 0
 $ Major 2nd          : chr  "Full Stack Product Management" "Digital Marketing" "Learning In Ui/Ux Design Tools"
 $ Institution 2nd    : chr  "Revou" "Google Skills Of Tomorrow Program" "It Academy"
 $ Job Role           : chr  "Co-Founder" "intern" "graphic designer"
 $ YoE                : num  54.824 0.833 2.663
 $ Experience         : int  0 0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{AUTOCAD,\"Copper Tubes\",\"Design Thinking\",\"Fitting and Welding\",\"Google Analytics\",\"Graphic Design\",\"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/miniprojectcertificate407IAPTGI1352022-86c64337-8d13-"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf"
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/orochimaru" "https://www.linkedin.com/in/mikle-zu" "https://www.linkedin.com/in/mhmoudahmad"
 $ Portfolio Link     : chr  "https://issuu.com/orochimaru/docs/portfolio_orochimaru" "" "https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/"
 $ Instagram Link     : chr  NA NA NA
 $ Twitter Link       : chr  NA NA NA
 $ State              : chr  "assessment_technical_assessment" "assessment_technical_assessment" "assessment_technical_assessment"
 $ Status Availability: 'pq_status_availability' chr  "open_to_work" "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: "2024-02-15 01:43:52" "2002-10-18 17:00:00" ...
 $ pob                : chr  NA NA NA
 $ ktp                : chr  NA NA NA
 $ address            : chr  NA NA "{\"address_type\"=>\"id_card\", \"address\"=>\"TMN CIKANDE BLK C-04\", \"ward\"=>nil, \"district\"=>nil, \"city"| __truncated__
 $ hp                 : chr  "12345678910" "+916201317199" "+624223423423"
 $ gender             : chr  "male" "female" "male"
 $ dcp                : chr  "student-dig30-lls5n1gorem4chyr" "student-dig31-jzu6zy91rysfo3tb" "jhon-david-albertsisus-qd4qkfopyx9hecvm"
 $ current_max        :integer64 0 0 0 
 $ current_min        :integer64 1 1 0 
 $ expect_max         :integer64 2 3 2 
 $ expect_min         :integer64 1 2 1 
 $ job_vacancy_id     :integer64 2872 2872 2872 
 $ user_job_vacancy_id:integer64 2162386 2162387 2162385 
 $ Calculated YoE     : int  25 1 2
          Applied Date User ID               Fullname Job Role ID
1  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS         711
2  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1409
3  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1410
4  2024-12-23 02:32:15   11152             Orochimaru         755
5  2024-12-23 02:32:15   11152             Orochimaru         755
6  2024-12-23 02:32:15   11152             Orochimaru         755
7  2024-12-23 02:32:15   11152             Orochimaru        1161
8  2024-12-23 02:32:15   11152             Orochimaru        1165
9  2024-12-23 02:32:15   11152             Orochimaru        1272
10 2024-12-23 02:32:15   11152             Orochimaru        1271
11 2024-12-23 02:32:15   11152             Orochimaru        1267
12 2024-12-23 02:32:15   11152             Orochimaru        1263
13 2024-12-23 02:32:15   11152             Orochimaru        1404
14 2024-12-23 02:32:15   11152             Orochimaru         893
15 2024-12-23 02:32:15   11152             Orochimaru        1404
16 2024-12-23 02:32:15   11152             Orochimaru         893
17 2024-12-23 02:32:15   11152             Orochimaru        1404
18 2024-12-23 02:32:15   11152             Orochimaru         893
19 2024-12-23 02:41:56   11153              Adem Sari         711
                      Job Role          industry
1             graphic designer           Finance
2  computer science instructor              <NA>
3               representative              <NA>
4                      founder        Technology
5                      founder        Technology
6                      founder        Technology
7          Shareholder/Partner        Technology
8         Associate Consultant Digital Marketing
9        front end developer i Digital Marketing
10         digital marketing i           Careers
11          test new role edit           Finance
12             co founder edit Digital Marketing
13   project admin distributor              <NA>
14            customer service              <NA>
15   project admin distributor              <NA>
16            customer service              <NA>
17   project admin distributor              <NA>
18            customer service              <NA>
19            graphic designer              <NA>
                                 company_name work_type             ends_at
1                                     BioDose full_time 2023-07-31 17:00:00
2                   Gilgamesh Institute (SYR) full_time 2021-09-30 17:00:00
3  Al-Basel Fair Invention & Innovation (SYR) full_time 2018-09-29 17:00:00
4                               Beasiswa Dian full_time                <NA>
5                              Bea Siswa Dian part_time                <NA>
6              MAM! feel good food restaurant full_time 2024-04-17 06:30:36
7                   PT Jelita Karisma Persada full_time                <NA>
8                                   Hay Group part_time 2011-09-30 00:00:00
9                                      test I part_time 2023-12-10 05:29:10
10                                   testedit part_time 2024-01-10 05:25:29
11                                  test edit part_time 2024-04-17 04:48:15
12                               Frame A Trip  contract 2024-04-17 04:48:43
13                                PT Bank DKI  contract 2021-01-31 17:00:00
14                                PT Bank DKI full_time 2023-05-31 17:00:00
15                                PT Bank DKI  contract 2021-01-31 17:00:00
16                                PT Bank DKI full_time 2023-05-31 17:00:00
17                                PT Bank DKI  contract 2021-01-31 17:00:00
18                                PT Bank DKI full_time 2023-05-31 17:00:00
19                                    BioDose full_time 2023-07-31 17:00:00
             starts_at          MoE
1  2022-09-30 17:00:00 1.013333e+01
2  2019-12-31 17:00:00 2.130000e+01
3  2018-08-31 17:00:00 9.666667e-01
4  2015-01-01 00:00:00 1.214705e+02
5  2015-03-01 00:00:00 1.195038e+02
6  2017-03-01 00:00:00 8.680904e+01
7  2013-01-01 00:00:00 1.458038e+02
8  2008-01-01 00:00:00 4.560000e+01
9  2023-12-10 05:28:30 1.549846e-05
10 2023-12-10 05:24:31 1.033355e+00
11 2023-12-10 05:14:57 4.299382e+00
12 2017-08-01 00:00:00 8.170668e+01
13 2020-12-31 17:00:00 1.033333e+00
14 2021-10-31 17:00:00 1.923333e+01
15 2020-12-31 17:00:00 1.033333e+00
16 2021-10-31 17:00:00 1.923333e+01
17 2020-12-31 17:00:00 1.033333e+00
18 2021-10-31 17:00:00 1.923333e+01
19 2022-09-30 17:00:00 1.013333e+01
'data.frame':	19 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 02:21:28" "2024-12-23 02:21:28" ...
 $ User ID     :integer64 232583 232583 232583 11152 11152 11152 11152 11152 ... 
 $ Fullname    : chr  "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "Orochimaru" ...
 $ Job Role ID :integer64 711 1409 1410 *********** 1161 1165 ... 
 $ Job Role    : chr  "graphic designer" "computer science instructor" "representative" "founder" ...
 $ industry    : chr  "Finance" NA NA "Technology" ...
 $ company_name: chr  "BioDose" "Gilgamesh Institute (SYR)" "Al-Basel Fair Invention & Innovation (SYR)" "Beasiswa Dian" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2023-07-31 17:00:00" "2021-09-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2022-09-30 17:00:00" "2019-12-31 17:00:00" ...
 $ MoE         : num  10.133 21.3 0.967 121.47 119.504 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	3 obs. of  14 variables:
 $ Candidate          : chr  "Orochimaru" "Adem Sari" "JHON DAVID ALBERTSISUS"
 $ Education          : chr  "S3" "D3" "S1"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Universitas Nusa Mandiri Jakarta" "Google Skills Of Tomorrow Program"
 $ Experience         : num  25 1 2
 $ GPA                : num  0 0 0
 $ Domisili           : chr  "Konoha" "Solagidih , Chas, District - Bokaro, Pin – 827013" "tangerang"
 $ Expected_Salary    :integer64 1 2 1 
 $ Industry           : chr  "{\"Technology\", \"Digital Marketing\", \"Careers\", \"Finance\"}" "{\"\"}" "{\"Finance\"}"
 $ Skillntools        : 'pq__varchar' chr  "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{AUTOCAD,\"Copper Tubes\",\"Design Thinking\",\"Fitting and Welding\",\"Google Analytics\",\"Graphic Design\",\"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ Major              : chr  "Pendidikan Dokter" "Mechanical Engineering" "Digital Marketing"
 $ user_job_vacancy_id:integer64 2162386 2162387 2162385 
 $ edu_matched        : logi  NA NA NA
 $ major_match        : logi  FALSE FALSE FALSE
 $ edu_major_weight   : logi  NA NA NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] NA
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          0
8 S3          0


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_domisili
[1] 20

$weight_es
[1] 20

$weight_skill
[1] 60

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
[1] "Recruiter Input"
[1] "Base Line 2"
[1] "SELECT * FROM (\n    SELECT DISTINCT\n        jv.name AS \"Job Role\",\n        jv.id,\n        jv.minimum_salary,\n        jv.maximum_salary,\n        jrg.name AS \"Job Group Role\",\n        jr.name AS \"Job Role Name\",\n        STRING_AGG(DISTINCT c.name, ', ') AS \"Tools and Competencies Mastery\",\n        el.name AS \"Education Level\",\n        ic.name AS \"Previous Job Industry\",\n        l.name AS \"Domicile\",\n        jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        STRING_AGG(DISTINCT um.name, ', ') AS \"Major\"\n    FROM job_vacancies jv\n    LEFT JOIN job_vacancy_competencies jvc ON jvc.job_vacancy_id = jv.id\n        AND jvc.discarded_at IS NULL\n    LEFT JOIN job_role_groups jrg ON jrg.id = jv.job_role_group_id\n        AND jrg.discarded_at IS NULL\n    LEFT JOIN job_roles jr ON jr.id = jv.job_role_id\n        AND jr.discarded_at IS NULL\n    LEFT JOIN competencies c ON c.id = jvc.competency_id\n        AND c.discarded_at IS NULL\n    LEFT JOIN education_levels el ON el.id = jv.education_level_id\n        AND el.discarded_at IS NULL\n    LEFT JOIN industry_categories ic ON ic.id = jv.industry_category_id\n        AND ic.discarded_at IS NULL\n    LEFT JOIN public.locations l ON l.id = jv.location_id\n        AND l.discarded_at IS NULL\n    LEFT JOIN job_vacancy_university_majors jvum ON jvum.job_vacancy_id = jv.id\n        AND jvum.discarded_at IS NULL\n    LEFT JOIN university_majors um ON um.id = jvum.university_major_id\n        AND um.discarded_at IS NULL\n    WHERE jv.discarded_at IS NULL\n    GROUP BY jv.name, jv.minimum_salary, jv.maximum_salary, jrg.name, jr.name, el.name, ic.name, l.name, jv.job_level,\n        jv.job_type,\n        jv.job_vacancy_type,\n        jv.work_mode,\n        jv.min_age,\n        jv.qualifications,\n        jv.max_age,\n        jr.id,\n        jv.id\n) AS subquery\nWHERE id = $1"
[1] "integer"
[1] 2872
[1] TRUE
Rows: 1
Columns: 18
$ `Job Role`                       <chr> "testing pemaketan"
$ id                               <int64> 2872
$ minimum_salary                   <dbl> 1
$ maximum_salary                   <dbl> 2
$ `Job Group Role`                 <chr> "Data"
$ `Job Role Name`                  <chr> NA
$ `Tools and Competencies Mastery` <chr> "1"
$ `Education Level`                <chr> NA
$ `Previous Job Industry`          <chr> NA
$ Domicile                         <chr> "Kabupaten Aceh Barat - Aceh"
$ job_level                        <chr> NA
$ job_type                         <chr> "full_time"
$ job_vacancy_type                 <chr> "talent_scouting"
$ work_mode                        <chr> "hybrid"
$ min_age                          <int> NA
$ qualifications                   <chr> ""
$ max_age                          <int> NA
$ Major                            <chr> "Pendidikan Dokter"
[1] "Recruiter Input Done"
[1] "Base Line Setup"
[1] "Base Line Setup Done"
[1] NA
[1] "Setup Config From DB"
$GPA
$GPA$MrC_GPA
$GPA$MrC_GPA[[1]]
$GPA$MrC_GPA[[1]]$level
[1] "0-2.5"

$GPA$MrC_GPA[[1]]$value
[1] 0


$GPA$MrC_GPA[[2]]
$GPA$MrC_GPA[[2]]$level
[1] "2.5-2.7"

$GPA$MrC_GPA[[2]]$value
[1] 0


$GPA$MrC_GPA[[3]]
$GPA$MrC_GPA[[3]]$level
[1] "2.7-2.9"

$GPA$MrC_GPA[[3]]$value
[1] 0


$GPA$MrC_GPA[[4]]
$GPA$MrC_GPA[[4]]$level
[1] "2.9-3.2"

$GPA$MrC_GPA[[4]]$value
[1] 0


$GPA$MrC_GPA[[5]]
$GPA$MrC_GPA[[5]]$level
[1] "3.2-3.5"

$GPA$MrC_GPA[[5]]$value
[1] 0


$GPA$MrC_GPA[[6]]
$GPA$MrC_GPA[[6]]$level
[1] "3.5-4"

$GPA$MrC_GPA[[6]]$value
[1] 0



$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0

$GPA$MrU_GPA$required
[1] TRUE



$Major
$Major$MrC_Major
$Major$MrC_Major[[1]]
$Major$MrC_Major[[1]]$name
[1] "Pendidikan Dokter"

$Major$MrC_Major[[1]]$value
[1] 0

$Major$MrC_Major[[1]]$order_level
[1] 0



$Major$MrU_Major
$Major$MrU_Major$weight
[1] 0

$Major$MrU_Major$required
[1] TRUE



$Domisili
$Domisili$MrC_Domisil
$Domisili$MrC_Domisil[[1]]
$Domisili$MrC_Domisil[[1]]$name
[1] "Kabupaten Aceh Barat - Aceh"

$Domisili$MrC_Domisil[[1]]$value
[1] 0

$Domisili$MrC_Domisil[[1]]$order_level
[1] 0



$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1

$Domisili$MrU_Domisil$required
[1] TRUE



$Industry
$Industry$MrC_Industry
list()

$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 1

$Industry$MrU_Industry$required
[1] TRUE



$Education
$Education$MrC_Education
$Education$MrC_Education[[1]]
$Education$MrC_Education[[1]]$level
[1] "SMA/SMK"

$Education$MrC_Education[[1]]$value
[1] 0

$Education$MrC_Education[[1]]$disabled
[1] FALSE

$Education$MrC_Education[[1]]$order_level
[1] 0


$Education$MrC_Education[[2]]
$Education$MrC_Education[[2]]$level
[1] "D1"

$Education$MrC_Education[[2]]$value
[1] 1

$Education$MrC_Education[[2]]$disabled
[1] FALSE

$Education$MrC_Education[[2]]$order_level
[1] 1


$Education$MrC_Education[[3]]
$Education$MrC_Education[[3]]$level
[1] "D2"

$Education$MrC_Education[[3]]$value
[1] 2

$Education$MrC_Education[[3]]$disabled
[1] FALSE

$Education$MrC_Education[[3]]$order_level
[1] 2


$Education$MrC_Education[[4]]
$Education$MrC_Education[[4]]$level
[1] "D3"

$Education$MrC_Education[[4]]$value
[1] 3

$Education$MrC_Education[[4]]$disabled
[1] FALSE

$Education$MrC_Education[[4]]$order_level
[1] 3


$Education$MrC_Education[[5]]
$Education$MrC_Education[[5]]$level
[1] "D4"

$Education$MrC_Education[[5]]$value
[1] 4

$Education$MrC_Education[[5]]$disabled
[1] FALSE

$Education$MrC_Education[[5]]$order_level
[1] 4


$Education$MrC_Education[[6]]
$Education$MrC_Education[[6]]$level
[1] "S1"

$Education$MrC_Education[[6]]$value
[1] 5

$Education$MrC_Education[[6]]$disabled
[1] FALSE

$Education$MrC_Education[[6]]$order_level
[1] 5


$Education$MrC_Education[[7]]
$Education$MrC_Education[[7]]$level
[1] "S2"

$Education$MrC_Education[[7]]$value
[1] 6

$Education$MrC_Education[[7]]$disabled
[1] FALSE

$Education$MrC_Education[[7]]$order_level
[1] 6


$Education$MrC_Education[[8]]
$Education$MrC_Education[[8]]$level
[1] "S3"

$Education$MrC_Education[[8]]$value
[1] 7

$Education$MrC_Education[[8]]$disabled
[1] FALSE

$Education$MrC_Education[[8]]$order_level
[1] 7



$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0

$Education$MrU_Education$required
[1] FALSE



$Skillntools
$Skillntools$MrC_Skillntools
$Skillntools$MrC_Skillntools[[1]]
$Skillntools$MrC_Skillntools[[1]]$name
[1] "1"

$Skillntools$MrC_Skillntools[[1]]$value
[1] 0

$Skillntools$MrC_Skillntools[[1]]$order_level
[1] 0



$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3

$Skillntools$MrU_Skillntools$required
[1] TRUE



$Expected_Salary
$Expected_Salary$MrC_ES
$Expected_Salary$MrC_ES[[1]]
$Expected_Salary$MrC_ES[[1]]$name
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$level
[1] "< 1.0"

$Expected_Salary$MrC_ES[[1]]$value
[1] 0


$Expected_Salary$MrC_ES[[2]]
$Expected_Salary$MrC_ES[[2]]$name
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$level
[1] "1.0 - 2.0"

$Expected_Salary$MrC_ES[[2]]$value
[1] 0


$Expected_Salary$MrC_ES[[3]]
$Expected_Salary$MrC_ES[[3]]$name
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$level
[1] "> 2.0"

$Expected_Salary$MrC_ES[[3]]$value
[1] 0



$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1

$Expected_Salary$MrU_ES$required
[1] TRUE



$Working_Experience
$Working_Experience$MrC_WE
$Working_Experience$MrC_WE[[1]]
$Working_Experience$MrC_WE[[1]]$level
[1] "0-2 tahun"

$Working_Experience$MrC_WE[[1]]$value
[1] 0

$Working_Experience$MrC_WE[[1]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[1]]$order_level
[1] 0


$Working_Experience$MrC_WE[[2]]
$Working_Experience$MrC_WE[[2]]$level
[1] "2-5 tahun"

$Working_Experience$MrC_WE[[2]]$value
[1] 1

$Working_Experience$MrC_WE[[2]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[2]]$order_level
[1] 1


$Working_Experience$MrC_WE[[3]]
$Working_Experience$MrC_WE[[3]]$level
[1] "7-12 tahun"

$Working_Experience$MrC_WE[[3]]$value
[1] 2

$Working_Experience$MrC_WE[[3]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[3]]$order_level
[1] 2


$Working_Experience$MrC_WE[[4]]
$Working_Experience$MrC_WE[[4]]$level
[1] "10-15 tahun"

$Working_Experience$MrC_WE[[4]]$value
[1] 3

$Working_Experience$MrC_WE[[4]]$disabled
[1] FALSE

$Working_Experience$MrC_WE[[4]]$order_level
[1] 3



$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0

$Working_Experience$MrU_WE$required
[1] FALSE



[1] NA
[1] NA
[1] "Setup Config From DB"
[1] "User Input"
[1] "Setup Candidate"
         Applied Date              Role                     Email User ID
1 2024-12-23 02:55:19 testing pemaketan       <EMAIL>    1683
2 2024-12-23 02:32:15 testing pemaketan  <EMAIL>   11152
3 2024-12-23 02:41:56 testing pemaketan  <EMAIL>   11153
4 2024-12-23 02:21:28 <NAME_EMAIL>  232583
                Fullname                                        Kota - Kab
1        Eliyahu Ramírez                   Kabupaten Sidoarjo - Jawa Timur
2             Orochimaru                                            Konoha
3              Adem Sari Solagidih , Chas, District - Bokaro, Pin – 827013
4 JHON DAVID ALBERTSISUS                                         tangerang
     Provinces  Degree GPA                  Major
1         <NA> SMA/SMK   3      Pendidikan Dokter
2 Jakarta Raya      S3   0      Pendidikan Dokter
3         <NA>      D3   0 Mechanical Engineering
4         <NA>      S1   0      Digital Marketing
                        Institution Degree 2nd GPA 2nd
1    Universitas Pendidikan Ganesha    SMA/SMK       3
2    Universitas Pendidikan Ganesha         S3       0
3  Universitas Nusa Mandiri Jakarta       <NA>       0
4 Google Skills Of Tomorrow Program         S1       0
                       Major 2nd                   Institution 2nd
1              Pendidikan Dokter    Universitas Pendidikan Ganesha
2  Full Stack Product Management                             Revou
3              Digital Marketing Google Skills Of Tomorrow Program
4 Learning In Ui/Ux Design Tools                        It Academy
             Job Role        YoE Experience
1 front end developer  4.0705906          0
2          Co-Founder 54.8241904          0
3              intern  0.8328767          0
4    graphic designer  2.6630137          0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Skill & Tools
1                                                                                                                                                                                                                       {"Algoritma dan Struktur Data Fundamental","Analisi Data Sistem",Android,asdasd,"Backend Developer Fundamentals","BI Tools : Dashboard","Build Tools","Campaign Management",ccccc,"CMS (Content Management System)",Communication2311,Communication2780,"Copywriting - UI/UX","corel draw",Da,dalang,"Data Analyst",Development,"Fast learner1313","Fast learner2121",figma,"Fundamental of Reinforcement Learning","Google Spreadsheet",HTML,"Introduction to Advertising",Jenkins,Leadership3630,Leadership940,"Market Funneling","Market Research",Math,"Ms. Access","Networking skills3526","NoSQL Database Management System","Object Oriented Programming (OOP)",Proactive3690,"Problem solving1387",Python,R,"Sketching and Testing","Softskill for Designer",SQL,"SQL Operation",t1,Tableau,Teamwork2520,"Technical savvy208","Test lagi",Ubersuggest,"uji nama panjang agar diketahui apa yang akan terjadi jika data terlalu melebihi batas kewajaran manusia",uye,Wayang,"Work under pressure2634"}
2 {"Adobe Illustrator","Algoritma dan Struktur Data Fundamental",Android,"API (Aplication Programing Interface)","Backend Developer for Mobiles Fundamentals","Backend Developer Fundamentals",Bug,"Build Tools","Campaign Management",CI/CD,Clouds,"CMS (Content Management System)",Communication693,"Conditional Formatting","Content Analysis","Cross-functional Collaboration","Customer Feedback Analysis","Data Fetching","Design Changes","Design Thinking","Developer Softskill",Development,df,"Domain and Hosting","Facebook Ads","Facebook Business Manager",Firebase,"Fundamental of Structured Query Language","Google Analytics","Google Search Console","Introduction to Advertising","Introduction to SEO","Issue Resolution","Keyword Research","Machine Learning Fundamental","Market Funneling",Math,"Ms. Excel",MySQL,"Network Engineer","NoSQL Database",Organisation1240,Organisation851,Proactive3872,"Product Development","Product Improvement","Product Presentation",Self-motivated2545,"SEO Foundation","Softskill for Designer",Tableau,"Team Management",Teamwork1515,Teamwork3502,test,"UI/UX Fundamentals","Usability Testing","User Research",wda,"Workflow Streamlining","Work under pressure3904"}
3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {AUTOCAD,"Copper Tubes","Design Thinking","Fitting and Welding","Google Analytics","Graphic Design","IBM Cloud",Math,"Ms. Excel",MySQL,"piping drawings","piping layouts",Self-motivated202,"Solar Energy","SOLID WORKS",Tableau,test,"UI Design","UX Design","Wooden Structure"}
4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            {"Augmented Reality","Data Analysis","Graphic Design","Microsoft Power BI","UI Design",Unity,"UX Design"}
                                                                                                                                  CV Upload
1                                  https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/dummy-8cc72758-bdd5-46e7-a15d-c82facd976a0.jpg
2                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf
3 https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/miniprojectcertificate407IAPTGI1352022-86c64337-8d13-4f76-a838-edc6aa2dedf8.pdf
4                               https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf
                                Linkedin Link
1 https://www.linkedin.com/in/Eliyahu-Ramírez
2      https://www.linkedin.com/in/orochimaru
3        https://www.linkedin.com/in/mikle-zu
4     https://www.linkedin.com/in/mhmoudahmad
                                          Portfolio Link Instagram Link
1                                   https://www.test.com           <NA>
2 https://issuu.com/orochimaru/docs/portfolio_orochimaru           <NA>
3                                                                  <NA>
4  https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/           <NA>
  Twitter Link                           State Status Availability
1         <NA> assessment_technical_assessment         looking_job
2         <NA> assessment_technical_assessment        open_to_work
3         <NA> assessment_technical_assessment        open_to_work
4         <NA> assessment_technical_assessment        open_to_work
                  dob      pob           ktp
1 2023-07-04 09:27:03 Boyolali jl dimana aja
2 2024-02-15 01:43:52     <NA>          <NA>
3 2002-10-18 17:00:00     <NA>          <NA>
4 1991-09-16 17:00:00     <NA>          <NA>
                                                                                                                             address
1                                                                                                                               <NA>
2                                                                                                                               <NA>
3                                                                                                                               <NA>
4 {"address_type"=>"id_card", "address"=>"TMN CIKANDE BLK C-04", "ward"=>nil, "district"=>nil, "city"=>"TANGERANG", "province"=>nil}
             hp gender                                     dcp current_max
1   12349191910   male                  genji-stdtfl54lnceewau  1000000000
2   12345678910   male          student-dig30-lls5n1gorem4chyr           0
3 +916201317199 female          student-dig31-jzu6zy91rysfo3tb           0
4 +624223423423   male jhon-david-albertsisus-qd4qkfopyx9hecvm           0
  current_min expect_max expect_min job_vacancy_id user_job_vacancy_id
1    10000000    1800000     500000           2872             2162388
2           1          2          1           2872             2162386
3           1          3          2           2872             2162387
4           0          2          1           2872             2162385
  Calculated YoE
1              1
2             25
3              1
4              2
'data.frame':	4 obs. of  40 variables:
 $ Applied Date       : POSIXct, format: "2024-12-23 02:55:19" "2024-12-23 02:32:15" ...
 $ Role               : chr  "testing pemaketan" "testing pemaketan" "testing pemaketan" "testing pemaketan"
 $ Email              : chr  "<EMAIL>" "<EMAIL>" "<EMAIL>" "<EMAIL>"
 $ User ID            :integer64 1683 11152 11153 232583 
 $ Fullname           : chr  "Eliyahu Ramírez" "Orochimaru" "Adem Sari" "JHON DAVID ALBERTSISUS"
 $ Kota - Kab         : chr  "Kabupaten Sidoarjo - Jawa Timur" "Konoha" "Solagidih , Chas, District - Bokaro, Pin – 827013" "tangerang"
 $ Provinces          : chr  NA "Jakarta Raya" NA NA
 $ Degree             : chr  "SMA/SMK" "S3" "D3" "S1"
 $ GPA                : num  3 0 0 0
 $ Major              : chr  "Pendidikan Dokter" "Pendidikan Dokter" "Mechanical Engineering" "Digital Marketing"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Universitas Pendidikan Ganesha" "Universitas Nusa Mandiri Jakarta" "Google Skills Of Tomorrow Program"
 $ Degree 2nd         : chr  "SMA/SMK" "S3" NA "S1"
 $ GPA 2nd            : num  3 0 0 0
 $ Major 2nd          : chr  "Pendidikan Dokter" "Full Stack Product Management" "Digital Marketing" "Learning In Ui/Ux Design Tools"
 $ Institution 2nd    : chr  "Universitas Pendidikan Ganesha" "Revou" "Google Skills Of Tomorrow Program" "It Academy"
 $ Job Role           : chr  "front end developer" "Co-Founder" "intern" "graphic designer"
 $ YoE                : num  4.071 54.824 0.833 2.663
 $ Experience         : int  0 0 0 0
 $ Skill & Tools      : 'pq__varchar' chr  "{\"Algoritma dan Struktur Data Fundamental\",\"Analisi Data Sistem\",Android,asdasd,\"Backend Developer Fundame"| __truncated__ "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{AUTOCAD,\"Copper Tubes\",\"Design Thinking\",\"Fitting and Welding\",\"Google Analytics\",\"Graphic Design\",\"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ CV Upload          : chr  "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/dummy-8cc72758-bdd5-46e7-a15d-c82facd976a0.jpg" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-48e4f67f-794a-474b-8272-a2449babc975.pdf" "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/miniprojectcertificate407IAPTGI1352022-86c64337-8d13-"| __truncated__ "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/Test_PDF-8617981f-efcc-45e9-9748-afc7fdcce723.pdf"
 $ Linkedin Link      : chr  "https://www.linkedin.com/in/Eliyahu-Ramírez" "https://www.linkedin.com/in/orochimaru" "https://www.linkedin.com/in/mikle-zu" "https://www.linkedin.com/in/mhmoudahmad"
 $ Portfolio Link     : chr  "https://www.test.com" "https://issuu.com/orochimaru/docs/portfolio_orochimaru" "" "https://rakamin-staging-e2ssxfei5-rakamin.vercel.app/"
 $ Instagram Link     : chr  NA NA NA NA
 $ Twitter Link       : chr  NA NA NA NA
 $ State              : chr  "assessment_technical_assessment" "assessment_technical_assessment" "assessment_technical_assessment" "assessment_technical_assessment"
 $ Status Availability: 'pq_status_availability' chr  "looking_job" "open_to_work" "open_to_work" "open_to_work"
 $ dob                : POSIXct, format: "2023-07-04 09:27:03" "2024-02-15 01:43:52" ...
 $ pob                : chr  "Boyolali" NA NA NA
 $ ktp                : chr  "jl dimana aja" NA NA NA
 $ address            : chr  NA NA NA "{\"address_type\"=>\"id_card\", \"address\"=>\"TMN CIKANDE BLK C-04\", \"ward\"=>nil, \"district\"=>nil, \"city"| __truncated__
 $ hp                 : chr  "12349191910" "12345678910" "+916201317199" "+624223423423"
 $ gender             : chr  "male" "male" "female" "male"
 $ dcp                : chr  "genji-stdtfl54lnceewau" "student-dig30-lls5n1gorem4chyr" "student-dig31-jzu6zy91rysfo3tb" "jhon-david-albertsisus-qd4qkfopyx9hecvm"
 $ current_max        :integer64 1000000000 0 0 0 
 $ current_min        :integer64 10000000 1 1 0 
 $ expect_max         :integer64 1800000 2 3 2 
 $ expect_min         :integer64 500000 1 2 1 
 $ job_vacancy_id     :integer64 2872 2872 2872 2872 
 $ user_job_vacancy_id:integer64 2162388 2162386 2162387 2162385 
 $ Calculated YoE     : int  1 25 1 2
          Applied Date User ID               Fullname Job Role ID
1  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS         711
2  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1409
3  2024-12-23 02:21:28  232583 JHON DAVID ALBERTSISUS        1410
4  2024-12-23 02:32:15   11152             Orochimaru         755
5  2024-12-23 02:32:15   11152             Orochimaru         755
6  2024-12-23 02:32:15   11152             Orochimaru         755
7  2024-12-23 02:32:15   11152             Orochimaru        1161
8  2024-12-23 02:32:15   11152             Orochimaru        1165
9  2024-12-23 02:32:15   11152             Orochimaru        1272
10 2024-12-23 02:32:15   11152             Orochimaru        1271
11 2024-12-23 02:32:15   11152             Orochimaru        1267
12 2024-12-23 02:32:15   11152             Orochimaru        1263
13 2024-12-23 02:32:15   11152             Orochimaru        1404
14 2024-12-23 02:32:15   11152             Orochimaru         893
15 2024-12-23 02:32:15   11152             Orochimaru        1404
16 2024-12-23 02:32:15   11152             Orochimaru         893
17 2024-12-23 02:32:15   11152             Orochimaru        1404
18 2024-12-23 02:32:15   11152             Orochimaru         893
19 2024-12-23 02:41:56   11153              Adem Sari         711
20 2024-12-23 02:55:19    1683        Eliyahu Ramírez        1381
21 2024-12-23 02:55:19    1683        Eliyahu Ramírez        1390
22 2024-12-23 02:55:19    1683        Eliyahu Ramírez        1380
23 2024-12-23 02:55:19    1683        Eliyahu Ramírez        <NA>
24 2024-12-23 02:55:19    1683        Eliyahu Ramírez         710
25 2024-12-23 02:55:19    1683        Eliyahu Ramírez          16
26 2024-12-23 02:55:19    1683        Eliyahu Ramírez          16
                      Job Role          industry
1             graphic designer           Finance
2  computer science instructor              <NA>
3               representative              <NA>
4                      founder        Technology
5                      founder        Technology
6                      founder        Technology
7          Shareholder/Partner        Technology
8         Associate Consultant Digital Marketing
9        front end developer i Digital Marketing
10         digital marketing i           Careers
11          test new role edit           Finance
12             co founder edit Digital Marketing
13   project admin distributor              <NA>
14            customer service              <NA>
15   project admin distributor              <NA>
16            customer service              <NA>
17   project admin distributor              <NA>
18            customer service              <NA>
19            graphic designer              <NA>
20                      asdasd            asdasd
21           new experienceess          new tech
22                software aja               asd
23                        <NA>           Finance
24         front end developer           Finance
25              data architect           Finance
26              data architect        Technology
                                 company_name work_type             ends_at
1                                     BioDose full_time 2023-07-31 17:00:00
2                   Gilgamesh Institute (SYR) full_time 2021-09-30 17:00:00
3  Al-Basel Fair Invention & Innovation (SYR) full_time 2018-09-29 17:00:00
4                               Beasiswa Dian full_time                <NA>
5                              Bea Siswa Dian part_time                <NA>
6              MAM! feel good food restaurant full_time 2024-04-17 06:30:36
7                   PT Jelita Karisma Persada full_time                <NA>
8                                   Hay Group part_time 2011-09-30 00:00:00
9                                      test I part_time 2023-12-10 05:29:10
10                                   testedit part_time 2024-01-10 05:25:29
11                                  test edit part_time 2024-04-17 04:48:15
12                               Frame A Trip  contract 2024-04-17 04:48:43
13                                PT Bank DKI  contract 2021-01-31 17:00:00
14                                PT Bank DKI full_time 2023-05-31 17:00:00
15                                PT Bank DKI  contract 2021-01-31 17:00:00
16                                PT Bank DKI full_time 2023-05-31 17:00:00
17                                PT Bank DKI  contract 2021-01-31 17:00:00
18                                PT Bank DKI full_time 2023-05-31 17:00:00
19                                    BioDose full_time 2023-07-31 17:00:00
20                                        asd full_time 2024-07-12 05:57:30
21                                          2 full_time 2024-07-12 05:57:30
22                                       test part_time                <NA>
23                                          a full_time 2024-06-30 04:57:52
24                                       asas part_time                <NA>
25                                       test part_time 2024-05-14 05:00:53
26                                       test part_time 2024-08-14 05:01:34
             starts_at          MoE
1  2022-09-30 17:00:00 1.013333e+01
2  2019-12-31 17:00:00 2.130000e+01
3  2018-08-31 17:00:00 9.666667e-01
4  2015-01-01 00:00:00 1.214708e+02
5  2015-03-01 00:00:00 1.195042e+02
6  2017-03-01 00:00:00 8.680904e+01
7  2013-01-01 00:00:00 1.458042e+02
8  2008-01-01 00:00:00 4.560000e+01
9  2023-12-10 05:28:30 1.549846e-05
10 2023-12-10 05:24:31 1.033355e+00
11 2023-12-10 05:14:57 4.299382e+00
12 2017-08-01 00:00:00 8.170668e+01
13 2020-12-31 17:00:00 1.033333e+00
14 2021-10-31 17:00:00 1.923333e+01
15 2020-12-31 17:00:00 1.033333e+00
16 2021-10-31 17:00:00 1.923333e+01
17 2020-12-31 17:00:00 1.033333e+00
18 2021-10-31 17:00:00 1.923333e+01
19 2022-09-30 17:00:00 1.013333e+01
20 2024-03-11 18:21:59 4.082767e+00
21 2024-07-11 18:25:49 1.601116e-02
22 2023-01-23 05:45:08 2.332951e+01
23 2024-02-29 04:57:49 4.066668e+00
24 2024-02-29 04:59:53 9.930561e+00
25 2024-02-14 05:00:51 3.000001e+00
26 2024-03-14 05:01:32 5.100001e+00
'data.frame':	26 obs. of  11 variables:
 $ Applied Date: POSIXct, format: "2024-12-23 02:21:28" "2024-12-23 02:21:28" ...
 $ User ID     :integer64 232583 232583 232583 11152 11152 11152 11152 11152 ... 
 $ Fullname    : chr  "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "JHON DAVID ALBERTSISUS" "Orochimaru" ...
 $ Job Role ID :integer64 711 1409 1410 *********** 1161 1165 ... 
 $ Job Role    : chr  "graphic designer" "computer science instructor" "representative" "founder" ...
 $ industry    : chr  "Finance" NA NA "Technology" ...
 $ company_name: chr  "BioDose" "Gilgamesh Institute (SYR)" "Al-Basel Fair Invention & Innovation (SYR)" "Beasiswa Dian" ...
 $ work_type   : 'pq_work_type' chr  "full_time" "full_time" "full_time" "full_time" ...
 $ ends_at     : POSIXct, format: "2023-07-31 17:00:00" "2021-09-30 17:00:00" ...
 $ starts_at   : POSIXct, format: "2022-09-30 17:00:00" "2019-12-31 17:00:00" ...
 $ MoE         : num  10.133 21.3 0.967 121.471 119.504 ...
[1] "Data Experience Processing - Start"
[1] "Data Experience Processing - Done"
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Warning in min(distances) :
  no non-missing arguments to min; returning Inf
Education_Major is not configured. Skipping education-major scoring.
'data.frame':	4 obs. of  14 variables:
 $ Candidate          : chr  "Eliyahu Ramírez" "Orochimaru" "Adem Sari" "JHON DAVID ALBERTSISUS"
 $ Education          : chr  "SMA/SMK" "S3" "D3" "S1"
 $ Institution        : chr  "Universitas Pendidikan Ganesha" "Universitas Pendidikan Ganesha" "Universitas Nusa Mandiri Jakarta" "Google Skills Of Tomorrow Program"
 $ Experience         : num  1 25 1 2
 $ GPA                : num  3 0 0 0
 $ Domisili           : chr  "Kabupaten Sidoarjo - Jawa Timur" "Konoha" "Solagidih , Chas, District - Bokaro, Pin – 827013" "tangerang"
 $ Expected_Salary    :integer64 500000 1 2 1 
 $ Industry           : chr  "{\"asdasd\", \"new tech\", \"asd\", \"Finance\", \"Technology\"}" "{\"Technology\", \"Digital Marketing\", \"Careers\", \"Finance\"}" "{\"\"}" "{\"Finance\"}"
 $ Skillntools        : 'pq__varchar' chr  "{\"Algoritma dan Struktur Data Fundamental\",\"Analisi Data Sistem\",Android,asdasd,\"Backend Developer Fundame"| __truncated__ "{\"Adobe Illustrator\",\"Algoritma dan Struktur Data Fundamental\",Android,\"API (Aplication Programing Interfa"| __truncated__ "{AUTOCAD,\"Copper Tubes\",\"Design Thinking\",\"Fitting and Welding\",\"Google Analytics\",\"Graphic Design\",\"| __truncated__ "{\"Augmented Reality\",\"Data Analysis\",\"Graphic Design\",\"Microsoft Power BI\",\"UI Design\",Unity,\"UX Design\"}"
 $ Major              : chr  "Pendidikan Dokter" "Pendidikan Dokter" "Mechanical Engineering" "Digital Marketing"
 $ user_job_vacancy_id:integer64 2162388 2162386 2162387 2162385 
 $ edu_matched        : logi  NA NA NA NA
 $ major_match        : logi  FALSE FALSE FALSE FALSE
 $ edu_major_weight   : logi  NA NA NA NA
[1] "Setup Candidate Done"
[1] "Setup Variable Input"
[1] "Setup MrU"
Education_Major is not configured. Returning default weight.
[1] "Setup MrC"
[1] NA
[1] "check_debug_here"
[1] "SMA/SMK" "D1"      "D2"      "D3"      "D4"      "S1"      "S2"     
[8] "S3"     
[1] NA
$`0-3 tahun`
[1] 0.0000 2.9999

$`3-6 tahun`
[1] 3.0000 5.9999

$`6-11 tahun`
[1]  6.0000 10.9999

$`11-99 tahun`
[1] 11 99

$`0-99 tahun`
[1]  0 99

[1] NA
[1] NA
[1] "Base line is NA or null. Returning all zeros."
[1] "or here"
[1] "Final Config Setup"
$MrU_Industry
$MrU_Industry$weight
[1] 0


$weight
[1] 0

$Education
$Education$MrU_Education
$Education$MrU_Education$weight
[1] 0


$Education$MrC_Education
# A tibble: 8 × 2
  level   value
  <chr>   <dbl>
1 SMA/SMK     0
2 D1          0
3 D2          0
4 D3          0
5 D4          0
6 S1          0
7 S2          0
8 S3          0


$Working_Experience
$Working_Experience$MrU_WE
$Working_Experience$MrU_WE$weight
[1] 0


$Working_Experience$MrC_WE
# A tibble: 5 × 2
  level       value
  <chr>       <dbl>
1 0-3 tahun       0
2 3-6 tahun       0
3 6-11 tahun      0
4 11-99 tahun     0
5 0-99 tahun      0


$GPA
$GPA$MrU_GPA
$GPA$MrU_GPA$weight
[1] 0


$GPA$MrC_GPA
# A tibble: 6 × 2
  level   value
  <chr>   <dbl>
1 0-2.5       0
2 2.5-2.7     0
3 2.7-2.9     0
4 2.9-3.2     0
5 3.2-3.5     0
6 3.5-4       0


$Domisili
$Domisili$MrU_Domisil
$Domisili$MrU_Domisil$weight
[1] 1



$Expected_Salary
$Expected_Salary$MrU_ES
$Expected_Salary$MrU_ES$weight
[1] 1



$Industry
$Industry$MrU_Industry
$Industry$MrU_Industry$weight
[1] 0



$Skillntools
$Skillntools$MrU_Skillntools
$Skillntools$MrU_Skillntools$weight
[1] 3



$Education_Major
$Education_Major$MrU_Education_Major
[1] 0


[1] "Final Config Done"
[1] "Scale MrU"
$weight_education
[1] 0

$weight_we
[1] 0

$weight_gpa
[1] 0

$weight_domisili
[1] 1

$weight_es
[1] 1

$weight_industry
[1] 0

$weight_skill
[1] 3

$weight_education_major
[1] 0

[1] "Scaled value"
$weight_domisili
[1] 20

$weight_es
[1] 20

$weight_skill
[1] 60

[1] "Scale Mru Done"
[1] "Cal MrU"
[1] "Test New Code"
[1] "Start New Function"
[1] "Done New Function"
[1] "Start New Calculation"
[1] "Candidate GPA: 3"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 3"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
[1] "Candidate GPA: 0"
[1] "Baseline GPA: NA"
<error/dplyr:::mutate_error>
Error in `mutate()` at magrittr/R/pipe.R:136:3:
ℹ In argument: `Match_Item_Major = mapply(...)`.
Caused by error in `if (min_distance <= 0.2) ...`:
! missing value where TRUE/FALSE needed
---
Backtrace:
     ▆
  1. ├─plumber::pr_run(pr("api.R"), port = 5656, host = "0.0.0.0")
  2. │ └─pr$run(...) at plumber/R/pr.R:532:3
  3. │   └─httpuv::runServer(host, port, self) at plumber/R/plumber.R:272:7
  4. │     └─httpuv::service(0) at httpuv/R/httpuv.R:718:3
  5. │       └─later::run_now(check_time, all = FALSE) at httpuv/R/httpuv.R:658:7
  6. │         └─later:::execCallbacks(timeoutSecs, all, loop$id) at later/R/later.R:302:3
  7. ├─httpuv (local) `<fn>`(`<env>`, `<externalptr>`) at later/R/RcppExports.R:45:5
  8. │ └─httpuv:::rookCall(private$app$call, req, req$.bodyData, seek(req$.bodyData)) at httpuv/R/httpuv.R:250:9
  9. │   ├─base::tryCatch(compute(), error = function(e) compute_error <<- e) at httpuv/R/httpuv.R:164:3
 10. │   │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 11. │   │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 12. │   │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 13. │   └─httpuv (local) compute() at httpuv/R/httpuv.R:164:3
 14. │     └─plumber (local) func(req) at httpuv/R/httpuv.R:117:5
 15. │       └─self$serve(req, res) at plumber/R/plumber.R:853:7
 16. │         └─plumber:::runSteps(...) at plumber/R/plumber.R:611:7
 17. │           └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 18. │             ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 19. │             │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 20. │             │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 21. │             │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 22. │             └─plumber (local) runStep() at plumber/R/async.R:104:3
 23. │               └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 24. │                 └─self$route(req, res) at plumber/R/plumber.R:568:9
 25. │                   ├─plumber:::withCurrentExecDomain(...) at plumber/R/plumber.R:836:7
 26. │                   │ └─promises::with_promise_domain(domain, expr) at plumber/R/async.R:161:3
 27. │                   │   └─domain$wrapSync(expr) at promises/R/domains.R:134:3
 28. │                   │     └─base::force(expr) at plumber/R/async.R:198:7
 29. │                   ├─plumber:::withWarn1(...) at plumber/R/plumber.R:837:9
 30. │                   │ └─base::force(expr) at plumber/R/async.R:21:3
 31. │                   └─plumber:::runStepsIfForwarding(NULL, errorHandlerStep, steps) at plumber/R/plumber.R:838:11
 32. │                     └─plumber:::runStepsUntil(...) at plumber/R/async.R:3:3
 33. │                       ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 34. │                       │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 35. │                       │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 36. │                       │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 37. │                       └─plumber (local) runStep() at plumber/R/async.R:104:3
 38. │                         └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 39. │                           └─h$exec(req, res) at plumber/R/plumber.R:705:11
 40. │                             └─plumber:::runSteps(...) at plumber/R/plumber-step.R:90:7
 41. │                               └─plumber:::runStepsUntil(...) at plumber/R/async.R:26:3
 42. │                                 ├─base::tryCatch(runStep(), error = errorHandlerStep) at plumber/R/async.R:104:3
 43. │                                 │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 44. │                                 │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 45. │                                 │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 46. │                                 └─plumber (local) runStep() at plumber/R/async.R:104:3
 47. │                                   └─plumber (local) nextStep(x) at plumber/R/async.R:74:7
 48. │                                     └─private$runHooksAround(...) at plumber/R/plumber-step.R:82:9
 49. │                                       └─plumber (local) execHook(i = length(stageHooks), args) at plumber/R/hookable.R:101:7
 50. │                                         ├─base::do.call(.next, getRelevantArgs(hookArgs, func = .next)) at plumber/R/hookable.R:86:11
 51. │                                         └─plumber (local) `<fn>`(...)
 52. │                                           └─base::do.call(private$func, relevant_args, envir = private$envir) at plumber/R/plumber-step.R:84:11
 53. ├─`<fn>`(...)
 54. │ ├─base::tryCatch(...) at api.R:136:3
 55. │ │ └─base (local) tryCatchList(expr, classes, parentenv, handlers)
 56. │ │   └─base (local) tryCatchOne(expr, names, parentenv, handlers[[1L]])
 57. │ │     └─base (local) doTryCatch(return(expr), name, parentenv, handler)
 58. │ └─base::source("match_making.R", local = exec_env) at api.R:149:5
 59. │   ├─base::withVisible(eval(ei, envir))
 60. │   └─base::eval(ei, envir)
 61. │     └─base::eval(ei, envir)
 62. │       └─candidates %>% ...
 63. ├─dplyr::mutate(...) at magrittr/R/pipe.R:136:3
 64. ├─dplyr:::mutate.data.frame(...) at dplyr/R/mutate.R:146:3
 65. │ └─dplyr:::mutate_cols(.data, dplyr_quosures(...), by) at dplyr/R/mutate.R:181:3
 66. │   ├─base::withCallingHandlers(...) at dplyr/R/mutate.R:268:3
 67. │   └─dplyr:::mutate_col(dots[[i]], data, mask, new_columns) at dplyr/R/mutate.R:273:7
 68. │     └─mask$eval_all_mutate(quo) at dplyr/R/mutate.R:380:9
 69. │       └─dplyr (local) eval() at dplyr/R/data-mask.R:94:7
 70. └─base::mapply(...)
 71.   └─`<fn>`(dots[[1L]][[1L]])
 72.     └─detect_major_match_items(candidate_major, base_line_major)
