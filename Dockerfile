FROM rhub/r-minimal:4.4.1

# Install system dependencies
RUN apk add --no-cache --update build-base \
  linux-headers \
  git \
  gfortran \
  libquadmath \
  unixodbc-dev \
  postgresql-dev \
  curl-dev \
  libxml2-dev \
  libsodium-dev \
  fontconfig-dev \
  harfbuzz-dev \
  fribidi-dev \
  freetype-dev \
  libpng-dev \
  tiff-dev \
  jpeg-dev

WORKDIR /scoring-candidate

COPY . .

# Install package dependencies from renv.lock
RUN Rscript -e 'install.packages("renv")'
RUN Rscript -e 'renv::restore()'

EXPOSE 5656

CMD ["Rscript", "run.R"]
