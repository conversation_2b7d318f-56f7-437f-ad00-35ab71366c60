library(dplyr)
library(stringdist)
library(lubridate)
library(tidyr)

# # Example data
# user_inputs <- data.frame(
#   Institution = c("Harvrd Univrsty", "Stanford", "UGM"),
#   stringsAsFactors = FALSE
# )
#
# mapped_data <- data.frame(
#   actual_input = c("Harvard University", "Stanford University", "Massachusetts Institute of Technology", "MIT"),
#   mapped_input = c("Harvard University", "Stanford University", "Massachusetts Institute of Technology", "Massachusetts Institute of Technology"),
#   rank_value = c("QS World Ranking 1 - 100", "QS World Ranking 1 - 100", "QS World Ranking 1 - 100", "QS World Ranking 1 - 100"),
#   return_value = c(2.5, 2.5, 3.0, 3.0),
#   stringsAsFactors = FALSE
# )

# Call university actual input from users in data frame candidates in column Institution
user_inputs <- candidates |>
  select(Candidate, Institution, user_job_vacancy_id) #|>
  #drop_na()

# Call data mapping university
mapped_data <- df_univ_redash

# This function running correctly
match_and_score <- function(user_inputs, mapped_data, threshold = 80) {
  # Input validation
  required_cols_user <- c("Candidate", "Institution", "user_job_vacancy_id")
  required_cols_mapped <- c("actual_input", "mapped_value", "rank_value", "return_value")

  if (!all(required_cols_user %in% colnames(user_inputs))) {
    stop("Missing required columns in user_inputs: ",
         paste(setdiff(required_cols_user, colnames(user_inputs)), collapse = ", "))
  }
  if (!all(required_cols_mapped %in% colnames(mapped_data))) {
    stop("Missing required columns in mapped_data: ",
         paste(setdiff(required_cols_mapped, colnames(mapped_data)), collapse = ", "))
  }

  # Ensure no NA values
  user_inputs <- user_inputs %>%
    mutate(Institution = replace_na(Institution, "Unknown"))

  mapped_data <- mapped_data %>%
    mutate(actual_input = replace_na(actual_input, "Unknown"))

  # Prepare the data
  results <- user_inputs %>%
    rowwise() %>%
    mutate(
      match_result = list(
        tryCatch({
          # Calculate string distances for current institution
          matches <- mapped_data %>%
            mutate(
              exact_ratio = (1 - stringdist(Institution, actual_input, method = "jw")) * 100,
              partial_ratio = (1 - stringdist(Institution, actual_input, method = "lv")) * 100,
              token_sort_ratio = (1 - stringdist(Institution, actual_input, method = "cosine")) * 100
            ) %>%
            filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold)

          # If no matches found, return default values
          if (nrow(matches) == 0) {
            tibble(
              actual_input = Institution,
              mapped_value = "No Match Found",
              rank_value = "Not Ranked",
              return_value = 0,
              exact_ratio = 0,
              partial_ratio = 0,
              token_sort_ratio = 0
            )
          } else {
            # Return best match based on exact_ratio
            matches %>%
              arrange(desc(exact_ratio)) %>%
              slice(1)
          }
        }, error = function(e) {
          # Handle errors gracefully
          message("Error in row: ", Institution, " - ", e$message)
          tibble(
            actual_input = Institution,
            mapped_value = "Error",
            rank_value = "Error",
            return_value = 0,
            exact_ratio = 0,
            partial_ratio = 0,
            token_sort_ratio = 0
          )
        })
      )
    ) %>%
    unnest(match_result) %>%
    mutate(
      pass = exact_ratio >= threshold,
      created_at = now(),
      updated_at = now(),
      input_table_name = "universities"
    ) %>%
    select(
      created_at,
      updated_at,
      actual_input = Institution,
      mapped_value,
      rank_value,
      return_value,
      exact_ratio,
      partial_ratio,
      token_sort_ratio,
      pass,
      input_table_name
    )

  return(results)
}


# match_and_score <- function(user_inputs, mapped_data, threshold = 80) {
#   # Input validation
#   required_cols_user <- c("Candidate", "Institution", "user_job_vacancy_id")
#   required_cols_mapped <- c("actual_input", "mapped_value", "rank_value", "return_value")
#
#   if (!all(required_cols_user %in% colnames(user_inputs))) {
#     stop("Missing required columns in user_inputs: ",
#          paste(setdiff(required_cols_user, colnames(user_inputs)), collapse = ", "))
#   }
#   if (!all(required_cols_mapped %in% colnames(mapped_data))) {
#     stop("Missing required columns in mapped_data: ",
#          paste(setdiff(required_cols_mapped, colnames(mapped_data)), collapse = ", "))
#   }
#
#   # Prepare the data
#   results <- user_inputs %>%
#     rowwise() %>%
#     mutate(
#       match_result = list({
#         # Calculate string distances for current institution
#         matches <- mapped_data %>%
#           mutate(
#             exact_ratio = (1 - stringdist(Institution, actual_input, method = "jw")) * 100,
#             partial_ratio = (1 - stringdist(Institution, actual_input, method = "lv")) * 100,
#             token_sort_ratio = (1 - stringdist(Institution, actual_input, method = "cosine")) * 100
#           ) %>%
#           filter(exact_ratio >= threshold | partial_ratio >= threshold | token_sort_ratio >= threshold)
#
#         # If no matches found, return default values
#         if (nrow(matches) == 0) {
#           return(tibble(
#             actual_input = Institution,
#             mapped_value = "No Match Found",
#             rank_value = "Not Ranked",
#             return_value = 0,
#             exact_ratio = 0,
#             partial_ratio = 0,
#             token_sort_ratio = 0
#           ))
#         }
#
#         # Return best match based on exact_ratio
#         matches %>%
#           arrange(desc(exact_ratio)) %>%
#           slice(1)
#       })
#     ) %>%
#     unnest(match_result) %>%
#     mutate(
#       pass = exact_ratio >= threshold,
#       created_at = now(),
#       updated_at = now(),
#       input_table_name = "universities"
#     ) %>%
#     select(
#       created_at,
#       updated_at,
#       actual_input = Institution,
#       mapped_value,
#       rank_value,
#       return_value,
#       exact_ratio,
#       partial_ratio,
#       token_sort_ratio,
#       pass,
#       input_table_name
#     )
#
#   return(results)
# }


# Apply the function
final_results <- match_and_score(user_inputs, mapped_data) # %>% slice(1:165)

# View results
print(final_results)
View(final_results)
