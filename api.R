library(plumber)
library(dotenv)
library(dplyr)
library(readr)
library(tidyr)
library(lubridate)
library(zoo)

# Load environment variables
load_dot_env(".env")

# Retrieve the token from the environment variable
valid_token <- Sys.getenv("VALID_TOKEN")

#* @apiTitle Data Experience Processing API
#* @apiDescription API for Processing Data Experience Candidate
#* @apiVersion 1.0.0

#* @filter cors
cors <- function(req, res) {
  res$setHeader("Access-Control-Allow-Origin", "*")
  if (req$REQUEST_METHOD == "OPTIONS") {
    res$setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
    res$setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type")
    res$status <- 200
    return(list())
  } else {
    plumber::forward()
  }
}

#* @filter authenticate
function(req, res) {
  # Skip authentication for Swagger UI routes and OPTIONS requests
  if (grepl("^/__docs__/", req$PATH_INFO) || req$PATH_INFO == "/openapi.json" || req$REQUEST_METHOD == "OPTIONS") {
    return(plumber::forward())
  }

  auth_header <- req$HTTP_AUTHORIZATION
  if (is.null(auth_header) || !grepl("^Bearer\\s", auth_header)) {
    res$status <- 401
    return(list(error = "Unauthorized: No valid bearer token provided"))
  }

  token <- sub("^Bearer\\s", "", auth_header)
  if (token != valid_token) {
    res$status <- 401
    return(list(error = "Unauthorized: Invalid token"))
  }

  plumber::forward()
}

#* Process data experience
#* @param job_vacancy_id:int The ID of the job vacancy
#* @param schema:string Tenant schema
#* @param recalculate_all:logical Should all data be recalculated?
#* @post /data_experience_processing/<job_vacancy_id:int>/<schema:string>/<recalculate_all:logical>
#* @security bearerAuth
function(job_vacancy_id, schema, recalculate_all, res) {
  # Input validation
  if (!is.numeric(job_vacancy_id) || job_vacancy_id <= 0) {
    res$status <- 400
    return(list(error = "Invalid job_vacancy_id"))
  }

  if (!is.logical(recalculate_all)) {
    recalculate_all <- as.logical(recalculate_all)
    if (is.na(recalculate_all)) {
      res$status <- 400
      return(list(error = "Invalid recalculate_all value"))
    }
  }


  # Your data processing logic
  tryCatch({
    # Create a list of parameters
    params <- list(
      job_vacancy_id = job_vacancy_id,
      schema = schema,
      recalculate_all = recalculate_all
    )

    # Create a new environment and assign the params to it
    exec_env <- new.env()
    exec_env$params <- params

    # Source the R script in the new environment
    source("calculation.R", local = exec_env)

    # Check if the script produced df_result
    if (!exists("df_result", envir = exec_env)) {
      stop("df_result not found after running calculation.R")
    }

    df_result <- get("df_result", envir = exec_env)

    # Convert df_result to a list or JSON
    processed_data <- as.list(df_result)  # Or toJSON(df_result) for JSON format

    # Return the processed data
    return(list(data = processed_data))
  }, error = function(e) {
    print(e)
    res$status <- 500
    return(list(error = paste("Internal server error:", e$message)))
  })
}


##--- Match Making ---##

#* Match making process
#* @param job_vacancy_id:int The ID of the job vacancy
#* @param schema:string Tenant schema
#* @param recalculate_all:logical Should all data be recalculated?
#* @post /match_making/<job_vacancy_id:int>/<schema:string>/<recalculate_all:logical>
#* @security bearerAuth
function(job_vacancy_id, schema, recalculate_all, res) {
  # Input validation
  if (!is.numeric(job_vacancy_id) || job_vacancy_id <= 0) {
    res$status <- 400
    return(list(error = "Invalid job_vacancy_id"))
  }

  if (!is.logical(recalculate_all)) {
    recalculate_all <- as.logical(recalculate_all)
    if (is.na(recalculate_all)) {
      res$status <- 400
      return(list(error = "Invalid recalculate_all value"))
    }
  }

  # Your match making logic
  tryCatch({
    # Create a list of parameters
    params <- list(
      job_vacancy_id = job_vacancy_id,
      schema = schema,
      recalculate_all = recalculate_all
    )

    # Create a new environment and assign the params to it
    exec_env <- new.env()
    exec_env$params <- params

    # Source the R script for match making in the new environment
    source("match_making.R", local = exec_env)

    # Check if the script produced df_result_matchmaking
    if (!exists("df_result", envir = exec_env)) {
      stop("df_result not found after running matchmaking.R")
    }

    df_result <- get("df_result", envir = exec_env)

    # Convert df_result_matchmaking to a list or JSON
    processed_data <- as.list(df_result)  # Or toJSON(df_result_matchmaking) for JSON format

    # Return the processed data
    return(list(data = processed_data))
  }, error = function(e) {
    print(e)
    res$status <- 500
    return(list(error = paste("Internal server error:", e$message)))
  })
}



#* @plumber
function(pr) {
  pr %>%
    pr_set_api_spec(function(spec) {
      spec$components$securitySchemes <- list(
        bearerAuth = list(
          type = "http",
          scheme = "bearer"
        )
      )
      spec$security <- list(list(bearerAuth = list()))
      return(spec)
    })
}
